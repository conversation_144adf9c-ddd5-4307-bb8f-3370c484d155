# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common commands

- npm run dev            # start development server
- npm run build          # build production bundles
- npm run start          # run production build locally
- npm run lint           # run ESLint checks
- npm run format         # auto-format code (ESLint & Prettier)
- npm run check-types    # run TypeScript type check
- npm test               # run unit tests (Jest)
- npm run test:e2e       # run Playwright integration/E2E tests
  
## Single-test invocation

- npm test -- <path_or_pattern>  # run specific Jest test file or suite

## High-level architecture

- Next.js 14 project with both App Router (`src/app/`) and legacy Page Router (`src/pages/`).
- UI components:
  - Global/shared: `src/components/`
  - Route-specific: alongside pages in `src/app/...`, e.g. `src/app/feature/components/`
- API:
  - Client-side wrappers in `src/api/`
  - Next.js API routes in `src/app/api/`
- Styling and assets:
  - Tailwind CSS configured via `tailwind.config.js`
  - Global styles in `src/styles/`
  - Public assets in `public/`
- Utilities:
  - `src/lib/` for core helpers (fetch, logger, templates)
  - `src/utils/` for miscellaneous utility functions
- State management:
  - Zustand/Jotai stores in `src/store/`
- Monorepo tooling and config:
  - Linting: ESLint + Biome (`biome.json`)
  - Formatting: Prettier
  - Git hooks: Husky + lint-staged
  - Commit lint: Commitlint + Commitizen
