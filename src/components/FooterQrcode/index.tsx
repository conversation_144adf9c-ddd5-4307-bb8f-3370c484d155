import { Footer, Image } from 'antd-mobile';
import React from 'react';

import { isPalmBaby } from '@/lib/utils';

import styles from './css.module.css';

const FooterQrCode = () => {
  return (
    <div className={styles.footer}>
      <div className="px-5 pt-[40px] text-center">
        <Footer label={isPalmBaby() ? '掌心宝贝' : '掌心智校'} style={{}} />
        <div
          className="flex flex-col items-center justify-center"
          style={{ marginBottom: '30px' }}
        >
          <Image
            alt=""
            height={100}
            src={
              isPalmBaby()
                ? '/images/palmBabyQrcode.png'
                : '/images/kidQrcode.png'
            }
            width={100}
          />
          <div className="mt-2 text-sm">
            <span className="text-gray-600">长按二维码了解更多</span>
          </div>
        </div>
      </div>
    </div>
  );
};
export default FooterQrCode;
