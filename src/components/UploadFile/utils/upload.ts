import { compress, generateKey, uploadObs } from '@/utils/obs';

/**
 * 上传媒体文件
 * @param file 文件对象
 * @param fileType 文件类型
 * @param progressCallback 进度回调函数
 * @returns 上传后的文件 URL
 */
export const uploadMedia = async (
  file: File | Blob,
  fileType: 'image' | 'video' | 'audio',
  progressCallback?: (progress: number) => void
): Promise<string> => {
  try {
    let folderName = 'upload';
    let uploadFile = file;
    let key = '';

    // 根据文件类型处理
    switch (fileType) {
      case 'image':
        folderName = 'upload/images';
        // 图片压缩
        if (file instanceof File && file.type.indexOf('image') > -1) {
          uploadFile = await compress(file);
        }
        key = generateKey(
          file instanceof File ? file.name : `image-${Date.now()}.jpg`,
          folderName
        );
        break;

      case 'video':
        folderName = 'upload/videos';
        key = generateKey(
          file instanceof File ? file.name : `video-${Date.now()}.mp4`,
          folderName
        );
        break;

      case 'audio':
        folderName = 'upload/audios';
        key = generateKey(`audio-${Date.now()}.wav`, folderName);
        break;

      default:
        throw new Error('不支持的文件类型');
    }

    // 上传文件
    const url = await uploadObs(uploadFile, key, false, progressCallback);
    console.log('🚀 ~ url:', url);

    if (!url) {
      throw new Error('上传失败');
    }

    return url;
  } catch (error) {
    console.error('上传失败：', error);
    throw error;
  }
};
