import clsx from 'clsx';
import { Pause, Play, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import type { FileType } from '@/components/UploadFile/types';

interface MediaPreviewProps {
  file: FileType;
  onClose: () => void;
}

const MediaPreview: React.FC<MediaPreviewProps> = ({ file, onClose }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const mediaRef = useRef<HTMLVideoElement | HTMLAudioElement | null>(null);

  // 获取文件 URL（优先使用远程 URL，如果没有则使用本地 URL）
  const fileUrl = file.status === 'success' ? file.url : file.localUrl;

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 播放/暂停媒体
  const togglePlay = () => {
    if (!mediaRef.current) return;

    if (isPlaying) {
      mediaRef.current.pause();
    } else {
      mediaRef.current.play().catch((error) => {
        console.error('播放失败：', error);
      });
    }
  };

  // 监听媒体播放状态变化
  useEffect(() => {
    const mediaElement = mediaRef.current;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    if (mediaElement) {
      mediaElement.addEventListener('play', handlePlay);
      mediaElement.addEventListener('pause', handlePause);
      mediaElement.addEventListener('ended', handleEnded);
    }

    return () => {
      if (mediaElement) {
        mediaElement.removeEventListener('play', handlePlay);
        mediaElement.removeEventListener('pause', handlePause);
        mediaElement.removeEventListener('ended', handleEnded);
      }
    };
  }, []);

  // 阻止点击事件冒泡
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 渲染媒体内容
  const renderMediaContent = () => {
    if (!fileUrl) return <div className="text-white">文件不可用</div>;

    switch (file.type) {
      case 'image':
        return (
          <img
            alt={file.name}
            className="max-h-[80vh] max-w-full object-contain"
            onClick={handleContentClick}
            src={fileUrl}
          />
        );
      case 'video':
        return (
          <video
            className="max-h-[80vh] max-w-full"
            controls
            onClick={handleContentClick}
            ref={mediaRef as React.RefObject<HTMLVideoElement>}
            src={fileUrl}
          >
            <track kind="captions" label="中文" src="" />
            您的浏览器不支持视频播放
          </video>
        );
      case 'audio':
        return (
          <div
            className="flex flex-col items-center rounded-lg bg-gradient-to-r from-green-100 to-blue-100 p-8"
            onClick={handleContentClick}
          >
            <div className="mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-white/30 backdrop-blur-sm">
              <button
                className="flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-white transition-colors hover:bg-green-600"
                onClick={togglePlay}
                type="button"
              >
                {isPlaying ? (
                  <Pause className="h-8 w-8" />
                ) : (
                  <Play className="h-8 w-8" />
                )}
              </button>
            </div>
            <h3 className="mb-2 font-medium text-gray-800 text-xl">
              {file.name}
            </h3>
            {file.duration && (
              <div className="text-gray-600">
                {formatDuration(file.duration || 0)}
              </div>
            )}
            <audio
              className="hidden"
              ref={mediaRef as React.RefObject<HTMLAudioElement>}
              src={fileUrl}
            >
              <track kind="captions" label="中文" src="" />
              您的浏览器不支持音频播放
            </audio>
          </div>
        );
      default:
        return <div className="text-white">不支持的文件类型</div>;
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      onClick={onClose}
    >
      <button
        className="absolute top-4 right-4 text-white hover:text-gray-300"
        onClick={onClose}
        type="button"
      >
        <X className="h-8 w-8" />
      </button>
      <div
        className={clsx(
          'mx-4 flex w-full max-w-4xl items-center justify-center',
          file.type === 'audio' ? 'max-w-md' : 'max-w-4xl'
        )}
      >
        {renderMediaContent()}
      </div>
    </div>
  );
};

export default MediaPreview;
