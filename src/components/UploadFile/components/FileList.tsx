import { CheckCircle2, Mic, Trash2, Video } from 'lucide-react';
import type React from 'react';
import { memo, useState } from 'react';
import type { FileType } from '../types';
import MediaPreview from './MediaPreview';

interface FileListProps {
  files: FileType[];
  onDelete: (id: string) => void;
}

const FileList: React.FC<FileListProps> = ({ files, onDelete }) => {
  console.log('🚀 ~ files:', files);
  const [previewFile, setPreviewFile] = useState<FileType | null>(null);

  // 将文件分成三列
  const chunkedFiles = chunkArray(files, 3);

  // 打开预览
  const handlePreview = (file: FileType) => {
    setPreviewFile(file);
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewFile(null);
  };

  // 格式化文件大小
  const formatFileSize = (bytes = 0) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  // 格式化时长
  const formatDuration = (seconds = 0) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 渲染单个文件项
  const renderFileItem = (file: FileType) => {
    console.log('🚀 ~ file:', file);
    const fileUrl = file.url || file.localUrl;

    return (
      <div
        className="relative overflow-hidden rounded-lg border border-gray-100 bg-white shadow-sm"
        key={file.id}
      >
        {/* 文件预览 */}
        <div className="cursor-pointer" onClick={() => handlePreview(file)}>
          {/* 文件预览内容 */}
          <div className="relative aspect-[4/3] w-full overflow-hidden rounded-t-lg bg-gray-100">
            {file.type === 'image' && (
              <img
                alt={file.name}
                className="h-full w-full object-cover"
                src={fileUrl}
              />
            )}

            {file.type === 'video' && (
              <div className="flex h-full w-full items-center justify-center bg-gray-800">
                {fileUrl && (
                  <video
                    className="h-full w-full object-cover opacity-80"
                    src={fileUrl}
                  >
                    <track kind="captions" label="中文" src="" />
                    您的浏览器不支持视频播放
                  </video>
                )}
                <div className="absolute inset-0 flex items-center justify-center">
                  <Video className="h-10 w-10 rounded-full bg-black/50 p-2 text-white" />
                </div>
              </div>
            )}

            {file.type === 'audio' && (
              <div className="flex h-full w-full items-center justify-center bg-gradient-to-r from-green-50 to-blue-50">
                <Mic className="h-10 w-10 text-green-500" />
                {file.duration && (
                  <span className="absolute right-2 bottom-1 rounded bg-white/70 px-1 text-gray-500 text-xs">
                    {formatDuration(file.duration)}
                  </span>
                )}
              </div>
            )}

            {/* 上传成功标识 */}
            {file.status === 'success' && (
              <div className="absolute top-1 right-1">
                <CheckCircle2 className="h-5 w-5 rounded-full bg-white text-green-500" />
              </div>
            )}
          </div>
        </div>

        {/* 文件信息 */}
        <div className="p-2">
          <div className="flex items-center justify-between">
            <span className="max-w-[100%] truncate font-medium text-xs">
              {file.name}
            </span>
          </div>
          <div className="flex items-center justify-between">
            {/* 文件大小 */}
            {file.size ? (
              <div className="mt-1 text-gray-400 text-xs">
                {formatFileSize(file.size)}
              </div>
            ) : (
              <div className="mt-1 text-gray-400 text-xs">未知大小</div>
            )}
            <button
              className="text-gray-400 hover:text-red-500"
              onClick={() => onDelete(file.id)}
              type="button"
            >
              <Trash2 className="h-4 w-4 text-red-400" />
            </button>
          </div>
        </div>

        {/* 上传进度条 */}
        {file.status === 'uploading' && (
          <div className="absolute bottom-0 left-0 h-1 w-full bg-gray-100">
            <div
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${file.progress}%` }}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex-1">
      {files.length === 0 ? (
        <div className="flex h-40 items-center justify-center text-gray-400">
          暂无文件，请点击下方按钮上传
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-4">
          {chunkedFiles.map((column, columnIndex) => (
            <div className="space-y-4" key={`column-${columnIndex}`}>
              {column.map((file) => renderFileItem(file))}
            </div>
          ))}
        </div>
      )}

      {/* 媒体预览模态框 */}
      {previewFile && (
        <MediaPreview file={previewFile} onClose={handleClosePreview} />
      )}
    </div>
  );
};

// 将数组分成指定列数的二维数组
function chunkArray<T>(array: T[], columns: number): T[][] {
  if (array.length === 0) return Array(columns).fill([]);

  const result: T[][] = Array(columns)
    .fill(0)
    .map(() => []);

  array.forEach((item, index) => {
    const columnIndex = index % columns;
    if (result[columnIndex]) {
      result[columnIndex].push(item);
    }
  });

  return result;
}

export default memo(FileList);
