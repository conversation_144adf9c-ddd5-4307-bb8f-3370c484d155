import { Image, Mic, Video } from 'lucide-react';
import type React from 'react';
import { useRef } from 'react';

interface UploadButtonsProps {
  onImageSelect: (files: FileList) => void;
  onVideoSelect: (files: FileList) => void;
  onRecordClick: () => void;
}

const UploadButtons: React.FC<UploadButtonsProps> = ({
  onImageSelect,
  onVideoSelect,
  onRecordClick,
}) => {
  const imageInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // 处理图片选择
  const handleImageClick = () => {
    imageInputRef.current?.click();
  };

  // 处理视频选择
  const handleVideoClick = () => {
    videoInputRef.current?.click();
  };

  // 处理图片文件选择
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onImageSelect(e.target.files);
      // 重置 input，允许选择相同文件
      e.target.value = '';
    }
  };

  // 处理视频文件选择
  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onVideoSelect(e.target.files);
      // 重置 input，允许选择相同文件
      e.target.value = '';
    }
  };

  return (
    <div className="grid grid-cols-3 gap-4">
      {/* 图片上传按钮 */}
      <div className="relative">
        <button
          className="flex w-full flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-colors hover:bg-blue-50"
          onClick={handleImageClick}
          type="button"
        >
          <Image className="mb-2 h-8 w-8 text-blue-500" />
          <span className="text-xs">上传图片</span>
        </button>
        <input
          accept="image/*"
          aria-label="上传图片"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          multiple
          onChange={handleImageChange}
          ref={imageInputRef}
          type="file"
        />
      </div>

      {/* 视频上传按钮 */}
      <div className="relative">
        <button
          className="flex w-full flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-colors hover:bg-purple-50"
          onClick={handleVideoClick}
          type="button"
        >
          <Video className="mb-2 h-8 w-8 text-purple-500" />
          <span className="text-xs">上传视频</span>
        </button>
        <input
          accept="video/*"
          aria-label="上传视频"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          multiple
          onChange={handleVideoChange}
          ref={videoInputRef}
          type="file"
        />
      </div>

      {/* 录音按钮 */}
      <button
        className="flex flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-colors hover:bg-green-50"
        onClick={onRecordClick}
        type="button"
      >
        <Mic className="mb-2 h-8 w-8 text-green-500" />
        <span className="text-xs">录音</span>
      </button>
    </div>
  );
};

export default UploadButtons;
