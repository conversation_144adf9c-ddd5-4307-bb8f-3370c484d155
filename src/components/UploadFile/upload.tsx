'use client';

import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import FileList from './components/FileList';
import RecordingModal from './components/RecordingModal';
import UploadButtons from './components/UploadButtons';
import type { FileType } from './types';

// 组件引用类型定义
export interface UploadRef {
  getFiles: () => FileType[];
  clearFiles: () => void;
  addFiles: (newFiles: FileType[]) => void;
}

// 组件属性类型定义
export interface UploadProps {
  initialFiles?: FileType[];
  className?: string;
  onDelete?: (id: string) => void;
}

const Upload = forwardRef<UploadRef, UploadProps>((props, ref) => {
  const { initialFiles = [], className = '', onDelete } = props;

  // 文件列表状态
  const [files, setFiles] = useState<FileType[]>(initialFiles);

  // 录音模态框状态
  const [showRecordingModal, setShowRecordingModal] = useState(false);

  // 当初始文件列表变化时更新状态
  useEffect(() => {
    setFiles(initialFiles);
  }, [initialFiles]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    // 获取当前文件列表
    getFiles: () => files,

    // 清空文件列表
    clearFiles: () => {
      setFiles([]);
    },

    // 添加文件到列表
    addFiles: (newFiles: FileType[]) => {
      setFiles((prev) => [...prev, ...newFiles]);
      // 如果有需要上传的文件，开始上传
      const filesToUpload = newFiles.filter(
        (file) => file.status === 'uploading' && file.file
      );
      for (const fileItem of filesToUpload) {
        uploadFile(fileItem);
      }
    },
  }));

  // 处理图片上传
  const handleImageUpload = (selectedFiles: FileList) => {
    const newFiles = Array.from(selectedFiles).map((file) => ({
      id: `image-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: file.name,
      type: 'image' as const,
      url: '',
      localUrl: URL.createObjectURL(file),
      status: 'uploading' as const,
      progress: 0,
      size: file.size,
      file, // 保存文件对象用于上传
    }));

    setFiles((prev) => [...prev, ...newFiles]);

    // 开始上传队列
    for (const fileItem of newFiles) {
      uploadFile(fileItem);
    }
  };

  // 处理视频上传
  const handleVideoUpload = (selectedFiles: FileList) => {
    const newFiles = Array.from(selectedFiles).map((file) => ({
      id: `video-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: file.name,
      type: 'video' as const,
      url: '',
      localUrl: URL.createObjectURL(file),
      status: 'uploading' as const,
      progress: 0,
      size: file.size,
      file, // 保存文件对象用于上传
    }));

    setFiles((prev) => [...prev, ...newFiles]);

    // 开始上传队列
    for (const fileItem of newFiles) {
      uploadFile(fileItem);
    }
  };

  // 处理录音完成
  const handleRecordingComplete = (audioBlob: Blob, duration: number) => {
    const fileItem = {
      id: `audio-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: `录音-${new Date().toLocaleTimeString()}`,
      type: 'audio' as const,
      url: '',
      localUrl: URL.createObjectURL(audioBlob),
      status: 'uploading' as const,
      progress: 0,
      size: audioBlob.size,
      duration,
      file: audioBlob,
    };

    setFiles((prev) => [...prev, fileItem]);
    setShowRecordingModal(false);

    // 开始上传
    uploadFile(fileItem);
  };

  // 上传文件
  const uploadFile = async (fileItem: FileType) => {
    try {
      // 导入上传工具函数
      const { uploadMedia } = await import('./utils/upload');

      // 上传文件并获取进度
      if (!fileItem.file) throw new Error('文件对象不存在');
      const url = await uploadMedia(
        fileItem.file,
        fileItem.type,
        (progress) => {
          // 更新上传进度
          setFiles((prev) =>
            prev.map((item) =>
              item.id === fileItem.id ? { ...item, progress } : item
            )
          );
        }
      );

      // 上传成功，更新文件状态
      setFiles((prev) =>
        prev.map((item) =>
          item.id === fileItem.id
            ? { ...item, url, status: 'success', progress: 100 }
            : item
        )
      );
    } catch (error) {
      console.error('上传失败：', error);
      // 上传失败，更新文件状态
      setFiles((prev) =>
        prev.map((item) =>
          item.id === fileItem.id ? { ...item, status: 'error' } : item
        )
      );
    }
  };

  // 删除文件
  const handleDeleteFile = useCallback(
    (id: string) => {
      setFiles((prev) => prev.filter((file) => file.id !== id));
      onDelete?.(id);
    },
    [onDelete]
  );

  return (
    <div className={`flex flex-col rounded-lg bg-gray-50 p-4 ${className}`}>
      {/* 文件列表 */}
      <FileList files={files} onDelete={handleDeleteFile} />

      {/* 上传按钮 */}
      <div className="mt-auto pt-4">
        <UploadButtons
          onImageSelect={handleImageUpload}
          onRecordClick={() => setShowRecordingModal(true)}
          onVideoSelect={handleVideoUpload}
        />
      </div>

      {/* 录音模态框 */}
      {showRecordingModal && (
        <RecordingModal
          onClose={() => setShowRecordingModal(false)}
          onComplete={handleRecordingComplete}
        />
      )}
    </div>
  );
});

Upload.displayName = 'Upload';

export default Upload;
