import { Image } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React from 'react';
import LaunchWeApp from '@/components/LaunchWeApp';
import { isPalmBaby } from '@/lib/utils';
import styles from './css.module.css';

const HeaderDown = ({ isTeacher = false }) => {
  const router = useRouter();

  const goDownApp = () => {
    router.push({
      pathname: '/download',
    });
  };

  const logo = isPalmBaby()
    ? 'https://unicorn-media.ancda.com/production/app/logo/palmBabyLogo.png'
    : isTeacher
      ? '/images/logo-teacher.png'
      : '/images/logo.png';

  return (
    <div className="fixed">
      <div className={styles.header}>
        <div className={styles.leftContainer}>
          <Image alt="" className={styles.logo} src={logo} />
          <div className={styles.title}>
            <div className={styles.titleName}>
              {isPalmBaby() ? '掌心宝贝' : '掌心智校'}
            </div>
            <div className={styles.titleDec}>家长和老师喜爱的家园共育平台</div>
          </div>
        </div>
        <div className={styles.customDownBut} onClick={goDownApp}>
          <LaunchWeApp size="small" />
        </div>
      </div>
    </div>
  );
};
export default HeaderDown;
