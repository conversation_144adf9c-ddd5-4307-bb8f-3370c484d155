import { Button, Toast } from 'antd-mobile';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { babyCaptchaId, captchaId } from '@/constant/env';
import api from '@/lib/api';
import initNECaptchaWithFallback from '@/lib/yidun-captcha';

function VerificationCodeButton({
  phoneNumber = '',
  disabled = false,
  className = '',
  scene = 3,
  optionConfig = {
    size: 'small',
    color: 'primary',
  },
  isPalmBaby = false,
}: any) {
  let timer: ReturnType<typeof setInterval>; // 定时器变量

  const mobile = useRef(''); // 是否可以发送验证码
  const [isSending, setIsSending] = useState(false); // 是否可以发送验证码
  const [secondsLeft, setSecondsLeft] = useState(0); // 剩余秒数
  const [loading, setLoading] = useState(false); // 是否可以发送验证码

  const captchaIns = useRef<any>(null);
  const [validate, setValidate] = useState<string>('');

  useEffect(() => {
    mobile.current = phoneNumber;
  }, [phoneNumber]);

  useEffect(() => {
    // 卸载组件时清除计时器
    return () => clearInterval(timer);
  }, []);

  const resetValidate = useCallback(() => {
    setValidate('');
    setTimeout(() => {
      captchaIns.current && captchaIns.current.refresh();
    }, 500);
  }, []);

  useEffect(() => {
    if (secondsLeft === 0) {
      clearInterval(timer);
      setIsSending(false);
      resetValidate();
    }
  }, [secondsLeft]);

  // 向服务端发送表单数据验证
  const ajax2Server = useCallback((validate: string) => {
    if (validate) {
      api
        .patch(
          '/v1/auth/captcha',
          {
            captchaValidate: validate,
          },
          {
            headers: {
              Brand: isPalmBaby ? 2 : 1,
            },
          }
        )
        .then(() => {
          submit(validate);
        })
        .catch(() => {
          resetValidate();
        });
    }
  }, []);

  // 初始化验证码
  const initNECaptcha = useCallback(() => {
    const cId = isPalmBaby ? babyCaptchaId : captchaId;
    console.log('captchaId', cId);
    initNECaptchaWithFallback(
      {
        captchaId,
        width: '300px',
        mode: 'popup',
        apiVersion: 2,
        errorFallbackCount: 3, // 验证码服务宕机最多尝试次数，默认是 3 次
        popupStyles: {
          position: 'absolute',
          top: '20%',
        },
        onVerify(err: any, data: { validate: string; isFallback: boolean }) {
          console.log('err', err);
          if (err) return; // 当验证失败时，内部会自动 refresh 方法，无需手动再调用一次
          setValidate(data.validate);
          ajax2Server(data.validate);
        },
      },
      function onload(instance: any) {
        captchaIns.current = instance;
      },
      function onerror(err: any) {
        console.log('[error]', err.code, err.message);
      }
    );
  }, [ajax2Server]);

  // 点击按钮
  const handleClick = useCallback(async () => {
    console.log('handleClick', phoneNumber, validate);
    if (phoneNumber.trim() === '') {
      Toast.show('请输入有效的手机号');
      return;
    }
    if (validate) {
      ajax2Server(validate);
    } else {
      captchaIns.current && captchaIns.current.verify();
    }
  }, [phoneNumber, validate]);

  useEffect(() => {
    initNECaptcha();
  }, [initNECaptcha]);

  /**
   * Submits the captcha validation and sends a request to the server to get the SMS code.
   * @param captchaValidate - The captcha validation string.
   */
  const submit = (captchaValidate: string) => {
    console.log('mobile', mobile.current);
    setLoading(true);
    const data = {
      reCode: '86',
      mobile: mobile.current,
      scene,
      captchaValidate,
    };
    if (!isSending) {
      // 发送验证码请求
      api
        .post('/v1/auth/smsCode', data, {
          headers: {
            Brand: isPalmBaby ? 2 : 1,
          },
        })
        .then(() => {
          Toast.show({
            icon: 'success',
            content: '发送成功',
          });
          setLoading(false);
          setIsSending(true);
          setSecondsLeft(60);
          timer = setInterval(() => {
            setSecondsLeft((s) => s - 1);
          }, 1000);
        })
        .catch((error) => {
          setLoading(false);
          setIsSending(false);
          setSecondsLeft(0);
          resetValidate();
        });
    }
  };

  return (
    <Button
      {...optionConfig}
      className={className}
      disabled={disabled || isSending}
      loading={loading}
      loadingText="发送中..."
      onClick={handleClick}
    >
      {isSending ? `${secondsLeft}秒后重新发送` : '发送验证码'}
    </Button>
  );
}

export default VerificationCodeButton;
