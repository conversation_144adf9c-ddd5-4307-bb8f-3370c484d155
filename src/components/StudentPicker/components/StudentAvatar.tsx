'use client';

import clsx from 'clsx';
import { X } from 'lucide-react';
import Image from 'next/image';
import React from 'react';
import { defaultAvatar } from '@/constant/config';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
}

interface StudentAvatarProps {
  student: Student;
  onRemove?: (studentId: string) => void;
  size?: 'sm' | 'md' | 'lg';
}

const StudentAvatar: React.FC<StudentAvatarProps> = ({
  student,
  onRemove,
  size = 'md',
}) => {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(student.studentId);
    }
  };

  const sizeClasses = {
    sm: {
      container: 'w-16',
      avatar: 'w-10 h-10',
      name: 'text-xs',
      removeBtn: 'w-4 h-4 -top-1 -right-1',
    },
    md: {
      container: 'w-20',
      avatar: 'w-14 h-14',
      name: 'text-sm',
      removeBtn: 'w-5 h-5 -top-1.5 -right-1.5',
    },
    lg: {
      container: 'w-24',
      avatar: 'w-16 h-16',
      name: 'text-base',
      removeBtn: 'w-6 h-6 -top-2 -right-2',
    },
  };

  return (
    <div
      className={clsx(
        'relative flex flex-col items-center',
        sizeClasses[size].container
      )}
    >
      <div className="relative">
        <div
          className={clsx(
            'overflow-hidden rounded-full',
            sizeClasses[size].avatar
          )}
        >
          <Image
            alt={student.studentName}
            className="h-full w-full object-cover"
            height={64}
            onError={(e) => {
              // 图片加载失败时使用默认头像
              (e.target as HTMLImageElement).src = defaultAvatar;
            }}
            src={student.avatar || defaultAvatar}
            width={64}
          />
        </div>
        {onRemove && (
          <button
            className={clsx(
              'absolute flex items-center justify-center rounded-full bg-red-500',
              'cursor-pointer text-white shadow-sm',
              sizeClasses[size].removeBtn
            )}
            onClick={handleRemove}
            type="button"
          >
            <X className="h-full w-full p-0.5" />
          </button>
        )}
      </div>
      <span
        className={clsx(
          'mt-1 w-full truncate text-center text-gray-700',
          sizeClasses[size].name
        )}
      >
        {student.studentName}
      </span>
    </div>
  );
};

// 使用 React.memo 包装组件，避免不必要的重新渲染
export default React.memo(StudentAvatar);
