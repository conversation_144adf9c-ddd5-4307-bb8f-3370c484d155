// 学生选择器

'use client';

import { <PERSON><PERSON><PERSON>, Picker, Toast } from 'antd-mobile';
import { Plus } from 'lucide-react';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { getClassList, getStudentListByClass } from '@/api/common';
import StudentAvatar from './components/StudentAvatar';

// 类型定义
interface ClassNode {
  id: string;
  name: string;
  type: number;
  children: ClassNode[];
  [key: string]: any;
}

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
}

interface StudentPickerProps {
  onSelect?: (
    classId: string,
    className: string,
    student: Student | null
  ) => void;
  onMultiSelect?: (students: Student[]) => void;
  value?: [string, string] | null;
  multiValue?: Student[];
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  classId?: string;
  className?: string;
  hideUI?: boolean;
  hideTitle?: boolean;
  title?: string;
}

// 定义组件暴露的方法接口
export interface StudentPickerRef {
  selectStudentsByClassId: () => Promise<Student | null>;
}

// 扁平化处理班级树
function flattenClassTree(data: ClassNode[]): ClassNode[] {
  const result: ClassNode[] = [];

  const processNode = (node: ClassNode) => {
    if (node.children && node.children.length > 0) {
      if (node.type === 2) {
        node.children.forEach((child) => result.push(child));
      }
      node.children.forEach(processNode);
    }
  };

  data.forEach(processNode);
  return result;
}

// 提取班级数据加载逻辑
function useClassData() {
  const [loading, setLoading] = useState(false);
  const [classData, setClassData] = useState<ClassNode[]>([]);

  useEffect(() => {
    const fetchClassList = async () => {
      setLoading(true);
      try {
        const response = await getClassList();
        const data = response?.data || response;

        if (Array.isArray(data)) {
          setClassData(flattenClassTree(data));
        } else if (data?.children) {
          setClassData(flattenClassTree([data as ClassNode]));
        } else if (data?.list && Array.isArray(data.list)) {
          setClassData(flattenClassTree(data.list));
        } else {
          setClassData([]);
        }
      } catch (error) {
        Toast.show({
          content: '获取班级列表失败',
          position: 'center',
        });
        setClassData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchClassList();
  }, []);

  return { classData, loading };
}

// 提取学生数据加载逻辑
function useStudentData(initialClassId?: string) {
  const [studentMap, setStudentMap] = useState<Record<string, Student[]>>({});
  const [loadedClassIds, setLoadedClassIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  const fetchStudentList = useCallback(
    async (classId: string) => {
      if (studentMap[classId] && loadedClassIds.has(classId)) {
        return;
      }

      try {
        setLoading(true);
        setStudentMap((prev) => ({ ...prev, [classId]: [] }));

        const response = await getStudentListByClass(classId);
        const data = response?.data || response;

        if (data?.list && Array.isArray(data.list)) {
          const students = data.list.map((student: any) => ({
            studentId: student.id || '',
            studentName: student.name || '',
            avatar: student.avatar || '',
            gender: student.gender || 0,
            classId,
          }));

          setStudentMap((prev) => ({ ...prev, [classId]: students }));
        } else {
          setStudentMap((prev) => ({ ...prev, [classId]: [] }));
        }

        setLoadedClassIds((prev) => {
          const newSet = new Set(prev);
          newSet.add(classId);
          return newSet;
        });
      } catch (error) {
        Toast.show({
          content: '获取学生列表失败',
          position: 'center',
        });
        setStudentMap((prev) => ({ ...prev, [classId]: [] }));
        setLoadedClassIds((prev) => {
          const newSet = new Set(prev);
          newSet.add(classId);
          return newSet;
        });
      } finally {
        setLoading(false);
      }
    },
    [loadedClassIds]
  );

  useEffect(() => {
    if (initialClassId) {
      fetchStudentList(initialClassId);
    }
  }, [initialClassId, fetchStudentList]);

  return { studentMap, loadedClassIds, fetchStudentList, loading };
}

// 主组件
const StudentPicker = forwardRef<StudentPickerRef, StudentPickerProps>(
  (
    {
      onMultiSelect,
      value,
      multiValue = [],
      disabled = false,
      multiple = false,
      classId,
      className,
      hideUI = false,
      hideTitle = false,
      title = '关联儿童',
    },
    ref
  ) => {
    // 使用提取的自定义 hooks
    const { classData, loading: classLoading } = useClassData();
    const {
      studentMap,
      loadedClassIds,
      fetchStudentList,
      loading: studentLoading,
    } = useStudentData(classId || value?.[0]);

    const [visible, setVisible] = useState(false);
    const [studentPickerVisible, setStudentPickerVisible] = useState(false);

    const [selectedStudents, setSelectedStudents] = useState<Student[]>(
      multiValue || []
    );

    // 初始化直接指定的班级
    useEffect(() => {
      if (classId) {
        fetchStudentList(classId);
      }
    }, [classId, fetchStudentList]);

    // 初始化多选值
    useEffect(() => {
      if (multiValue?.length > 0) {
        setSelectedStudents(multiValue);
      }
    }, [multiValue]);

    // 实现 selectStudentsByClassId 方法
    const selectStudentsByClassId = async (): Promise<Student | null> => {
      // 如果没有设置 hideUI，则正常显示 UI
      return new Promise((resolve) => {
        // 设置一个临时的回调函数来处理学生选择
        const handleStudentSelected = (student: Student) => {
          // 选择完成后，移除临时回调
          tempHandleConfirmRef.current = null;
          // 返回选中的学生
          resolve(student);
        };

        // 保存临时回调
        tempHandleConfirmRef.current = handleStudentSelected;

        // 打开学生选择器
        setStudentPickerVisible(true);
      });
    };

    // 创建一个临时引用来保存选择回调
    const tempHandleConfirmRef = useRef<((student: Student) => void) | null>(
      null
    );

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      selectStudentsByClassId,
    }));

    // 选项数据生成
    const options = useMemo(() => {
      return classData.map((classItem) => {
        const students = studentMap[classItem.id] || [];
        const isClassLoaded = loadedClassIds.has(classItem.id);

        return {
          label: classItem.name,
          value: classItem.id,
          children: isClassLoaded
            ? students.length > 0
              ? students.map((student) => ({
                  label: student.studentName,
                  value: student.studentId,
                  student,
                }))
              : [{ label: '暂无学生', value: 'no-students', disabled: true }]
            : [{ label: '加载中...', value: 'loading', disabled: true }],
        };
      });
    }, [classData, studentMap, loadedClassIds]);

    // 学生选择器选项
    const studentOptions = useMemo(() => {
      if (!(classId && studentMap[classId])) return [];

      const students = studentMap[classId] || [];
      return students.length > 0
        ? students.map((student) => ({
            label: student.studentName,
            value: student.studentId,
          }))
        : [{ label: '暂无学生', value: 'no-students', disabled: true }];
    }, [classId, studentMap]);

    // 处理选择器打开
    const handlePickerOpen = () => {
      if (disabled) return;

      // 如果指定了班级 ID，则打开学生选择器
      if (classId) {
        setStudentPickerVisible(true);
      } else {
        setVisible(true);
      }
    };

    // 处理级联选择器确认
    const handleConfirm = (val: any[]) => {
      if (
        val.length === 2 &&
        val[0] &&
        val[1] &&
        val[1] !== 'loading' &&
        val[1] !== 'no-students'
      ) {
        const classId = String(val[0]);
        const studentId = String(val[1]);

        const selectedClass = classData.find((c) => c.id === classId);
        const selectedStudent = studentMap[classId]?.find(
          (s) => s.studentId === studentId
        );

        if (selectedClass && selectedStudent) {
          const studentExists = selectedStudents.some(
            (s) => s.studentId === selectedStudent.studentId
          );

          if (!studentExists) {
            const newSelectedStudents = [...selectedStudents, selectedStudent];
            setSelectedStudents(newSelectedStudents);
            onMultiSelect?.(newSelectedStudents);
          }
        }
      }
      setVisible(false);
    };

    // 处理学生选择器确认
    const handleStudentConfirm = (val: any[], extend?: any) => {
      if (val.length > 0 && val[0] && val[0] !== 'no-students' && classId) {
        const studentId = String(val[0]);
        const student = studentMap[classId]?.find(
          (s) => s.studentId === studentId
        );

        if (student) {
          const studentExists = selectedStudents.some(
            (s) => s.studentId === student.studentId
          );

          if (!studentExists) {
            const newSelectedStudents = [...selectedStudents, student];
            setSelectedStudents(newSelectedStudents);
            onMultiSelect?.(newSelectedStudents);
          }
          tempHandleConfirmRef.current?.(student);
        }
      }
      setStudentPickerVisible(false);
    };

    // 处理选择取消
    const handleCancel = () => setVisible(false);

    // 处理学生选择器取消
    const handleStudentCancel = () => setStudentPickerVisible(false);

    // 处理级联选择器值变化
    const handleCascaderChange = (val: any[], extend: any) => {
      if (val.length > 0 && val[0] && extend?.items[0]?.value === val[0]) {
        fetchStudentList(String(val[0]));
      }
    };

    // 移除已选择的学生
    const handleRemoveStudent = (studentId: string) => {
      const newSelectedStudents = selectedStudents.filter(
        (s) => s.studentId !== studentId
      );
      setSelectedStudents(newSelectedStudents);
      onMultiSelect?.(newSelectedStudents);
    };

    return (
      <div className="student-picker">
        {!hideUI && (
          <div className="space-y-3">
            {!hideTitle && (
              <div className="mb-1 font-medium text-gray-700 text-sm">
                {title}
              </div>
            )}
            <div className="flex flex-wrap items-center gap-2">
              {selectedStudents.map((student) => (
                <React.Suspense
                  fallback={<div className="h-20 w-16" />}
                  key={student.studentId}
                >
                  <StudentAvatar
                    onRemove={handleRemoveStudent}
                    size="sm"
                    student={student}
                  />
                </React.Suspense>
              ))}
              {(multiple || (!multiple && selectedStudents.length === 0)) && (
                <button
                  className="flex items-center text-primary text-sm hover:text-primary/80"
                  onClick={handlePickerOpen}
                  type="button"
                >
                  <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full border-2 border-gray-400">
                    <Plus className="h-4 w-4" />
                  </div>
                </button>
              )}
            </div>
          </div>
        )}

        {/* 级联选择器 - 当没有指定班级 ID 时使用 */}
        {!classId && (
          <CascadePicker
            onCancel={handleCancel}
            onConfirm={handleConfirm}
            onSelect={handleCascaderChange}
            options={options}
            title="选择班级和学生"
            visible={visible}
          />
        )}

        {/* 学生选择器 - 当指定班级 ID 时使用 */}
        {classId && (
          <Picker
            columns={[studentOptions]}
            onCancel={handleStudentCancel}
            onConfirm={handleStudentConfirm}
            title="选择学生"
            visible={studentPickerVisible}
          />
        )}
      </div>
    );
  }
);

StudentPicker.displayName = 'StudentPicker';

export default React.memo(StudentPicker);
