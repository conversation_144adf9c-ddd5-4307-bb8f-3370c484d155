import { Mask, SpinLoading } from 'antd-mobile';

const Load = (props: any) => {
  return (
    <Mask
      opacity="thick"
      style={props.style}
      visible={props.visible}
      z-index="9999"
    >
      <div className="flex h-screen w-screen flex-col items-center justify-center">
        <SpinLoading
          color={props.color || '#17c5a6'}
          style={{ '--size': '32px' }}
        />
        <div className="mt-4 text-white">{props.text || '请稍后...'}</div>
      </div>
    </Mask>
  );
};
export default Load;
