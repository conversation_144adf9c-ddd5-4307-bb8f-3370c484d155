'use client';
import {
  CascadePicker,
  type CascadePickerOption,
  ErrorBlock,
  Image,
  Popup,
  Radio,
  Toast,
} from 'antd-mobile';
import { ChevronRight, Plus } from 'lucide-react';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { getStudentListByClass } from '@/api/common';
import { getMyClassList } from '@/api/invite';
import StudentAvatar from '@/components/StudentPicker/components/StudentAvatar';

const StudentSelector = ({
  defaultStudents = [],
  onMultiSelect = (value) => {},
  hideUI = false,
  hideTitle = false,
  title = '关联的学生',
  multiple = false,
  disabled = false,
}) => {
  const [selectedStudents, setSelectedStudents] =
    useState<any>(defaultStudents);
  const [sureSelectedStudents, setSureSelectedStudents] =
    useState<any>(defaultStudents);
  const [visible, setVisible] = useState(false);
  const [classListData, setClassListData] = useState<CascadePickerOption[]>([]);
  const [classPickerVisible, setClassPickerVisible] = useState(false);

  const [tempSelectedClassName, setTempSelectedClassName] = useState<
    string | null
  >(null);
  const [students, setStudents] = useState<any>([]);
  const toggleStudent = useRef<any>(null);
  const getStudentListByClassId = async (
    classId: string,
    className: string
  ) => {
    const response = await getStudentListByClass(classId);
    const data = response?.data || response;
    const resStudents = data.list.map((student: any) => ({
      studentId: student.id || '',
      studentName: student.name || '',
      avatar: student.avatar || '',
      gender: student.gender || 0,
      classId,
      className,
    }));
    setStudents(resStudents);
  };
  const handleClassConfirm = async (
    val: string[],
    extend: { items: CascadePickerOption[] }
  ) => {
    if (extend.items.length > 0) {
      const selected = extend.items[extend.items.length - 1];
      if (!selected) return;
      setTempSelectedClassName(selected.label);
      setClassPickerVisible(false);
      getStudentListByClassId(selected.value, selected.label);
    } else {
      Toast.show('请选择一个具体的班级');
    }
  };
  const convertToCascadePickerOptions = (
    treeArray: any[]
  ): CascadePickerOption[] => {
    if (!Array.isArray(treeArray)) {
      console.warn(
        'convertToCascadePickerOptions 接收到的输入不是数组:',
        treeArray
      );
      return [];
    }
    return treeArray.map((item) => {
      const option: CascadePickerOption = {
        label: item.name,
        value: item.id.toString(),
      };
      if (item.children && item.children.length > 0) {
        option.children = convertToCascadePickerOptions(item.children);
      }
      return option;
    });
  };
  toggleStudent.current = (student) => {
    if (!multiple) {
      setSelectedStudents([student]);
      setStudents((prev) => {
        return prev.map((s) => {
          return {
            ...s,
            isSelected: s.studentId === student.studentId,
          };
        });
      });
      return;
    }
    setStudents((prev) => {
      return prev.map((s) => {
        if (s.studentId === student.studentId) {
          return {
            ...s,
            isSelected: !s.isSelected,
          };
        }
        return s;
      });
    });
    const isSelected = selectedStudents.find(
      (s) => s.studentId === student.studentId
    );
    let newSelected;
    if (isSelected) {
      newSelected = selectedStudents.filter(
        (s) => s.studentId !== student.studentId
      );
    } else {
      newSelected = [...selectedStudents, student];
    }
    setSelectedStudents(newSelected);
  };

  const handleRemoveStudent = (studentId: string) => {
    const newSelectedStudents = selectedStudents.filter(
      (s) => s.studentId !== studentId
    );
    setSureSelectedStudents(selectedStudents);
    setSelectedStudents(newSelectedStudents);
  };
  const handlePickerOpen = () => {
    if (disabled) return;
    setVisible(true);
  };
  const handleConfirm = () => {
    setSureSelectedStudents(selectedStudents);
    onMultiSelect(selectedStudents);
    setVisible(false);
  };
  useEffect(() => {
    getMyClassList().then((res: any) => {
      if (res.children && Array.isArray(res.children)) {
        const options = convertToCascadePickerOptions(res.children);
        setClassListData(options);
        if (
          options.length > 0 &&
          options[0]?.children &&
          options[0].children?.length > 0
        ) {
          setTempSelectedClassName(options[0]?.children[0]?.label as string);
          getStudentListByClassId(
            options[0]?.children[0]?.value as string,
            options[0]?.children[0]?.label as string
          );
        }
      }
    });
  }, []);

  return (
    <>
      {!hideUI && (
        <div className="space-y-3">
          {!hideTitle && (
            <div className="mb-1 font-medium text-gray-700 text-sm">
              {title}
            </div>
          )}
          <div className="flex flex-wrap items-center gap-2">
            {sureSelectedStudents.map((student) => (
              <React.Suspense
                fallback={<div className="h-20 w-16" />}
                key={student.studentId}
              >
                <StudentAvatar
                  onRemove={handleRemoveStudent}
                  size="sm"
                  student={student}
                />
              </React.Suspense>
            ))}
            {(multiple || (!multiple && sureSelectedStudents.length === 0)) && (
              <button
                className="flex items-center text-primary text-sm hover:text-primary/80"
                onClick={handlePickerOpen}
                type="button"
              >
                <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-full border-2 border-gray-400">
                  <Plus className="h-4 w-4" />
                </div>
              </button>
            )}
          </div>
        </div>
      )}

      <Popup
        bodyStyle={{ width: '80vw', height: '100vh' }}
        onMaskClick={() => setVisible(false)}
        position="right"
        visible={visible}
      >
        <div className="flex h-full flex-col bg-gray-50">
          <div className="flex items-center justify-between border-b bg-white p-4">
            <button
              className="text-gray-600"
              onClick={() => setVisible(false)}
              type="button"
            >
              取消
            </button>
            <h1 className="font-medium text-lg">选择学生</h1>
            <button
              className="text-blue-600"
              onClick={handleConfirm}
              type="button"
            >
              确定
            </button>
          </div>

          <div className="flex overflow-x-auto bg-white px-4">
            {selectedStudents.map((student) => (
              <div
                className="relative mx-2 my-4 h-20 w-16 flex-shrink-0"
                key={student.studentId}
              >
                <Image
                  height={50}
                  src={student.avatar}
                  style={{ borderRadius: 25 }}
                  width={50}
                />
                <button
                  className="-top-2 absolute right-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-white"
                  onClick={() => toggleStudent.current(student)}
                  type="button"
                >
                  -
                </button>
                <span className="mt-1 block text-center text-xs">
                  {student.studentName}
                </span>
              </div>
            ))}
          </div>

          <div
            className="mt-2 flex items-center justify-between bg-white p-4"
            onClick={() => setClassPickerVisible(true)}
          >
            <span>
              {tempSelectedClassName ? tempSelectedClassName : '请选择班级'}
            </span>
            <ChevronRight className="text-gray-400" size={20} />
          </div>
          <div className="mt-2 flex-1 overflow-y-auto bg-white p-4">
            {students.length > 0 ? (
              <div className="grid grid-cols-4 gap-4">
                {students.map((student) => (
                  <ItemStudent
                    key={student.studentId}
                    student={student}
                    toggleStudent={toggleStudent}
                  />
                ))}
              </div>
            ) : (
              <ErrorBlock description="" status="empty" title="暂无数据" />
            )}
          </div>
        </div>
      </Popup>

      <CascadePicker
        onClose={() => setClassPickerVisible(false)}
        onConfirm={handleClassConfirm}
        options={classListData}
        title="选择班级"
        visible={classPickerVisible}
      />
    </>
  );
};
export const ItemStudent = memo(
  ({ student, toggleStudent }: any) => {
    return (
      <div
        className="flex flex-col items-center"
        onClick={() => toggleStudent.current(student)}
      >
        <div className="relative flex h-16 w-16 items-center justify-center rounded-full">
          <Image
            height={50}
            src={student.avatar}
            style={{ borderRadius: 25 }}
            width={50}
          />
          {student.isSelected && (
            <div className="absolute top-0 right-0">
              <Radio
                checked
                style={{
                  '--icon-size': '18px',
                  '--font-size': '14px',
                  '--gap': '6px',
                }}
                value="small"
              />
            </div>
          )}
        </div>
        <span className="text-sm">{student.studentName}</span>
      </div>
    );
  },
  (prevProps, nextProps) =>
    prevProps.student.isSelected === nextProps.student.isSelected
);
export default StudentSelector;
