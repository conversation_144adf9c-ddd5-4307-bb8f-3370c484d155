'use client';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import { Button } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { isPalmBaby } from '@/lib/utils';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'wx-open-launch-app': React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement> & {
          appid?: string;
          extinfo?: string;
        },
        HTMLElement
      >;
    }
  }
}

const data = [
  {
    platform: 'Android',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.parents',
        qrcode: '/images/qrcode/android-parents-app-download.png',
        downloadUrl32:
          'https://unicorn-media.ancda.com/production/app/apk/ZhangXinZhiXiao_parents-release-32.apk',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.teacher',
        qrcode: '/images/qrcode/android-teacher-app-download.png',
      },
    ],
  },
  {
    platform: 'iOS',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.parents',
        qrcode: '/images/qrcode/ios-parents-app-download.png',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.teacher',
        qrcode: '/images/qrcode/ios-teacher-app-download.png',
      },
    ],
  },
];

const babyData = [
  {
    platform: 'Android',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents',
        qrcode: '/images/qrcode/android-parents-baby-download.png',
        downloadUrl32:
          'https://unicorn-media.ancda.com/production/app/apk/babyParents-release-32.apk',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents.teacher',
        qrcode: '/images/qrcode/android-teacher-baby-download.png',
      },
    ],
  },
  {
    platform: 'iOS',
    data: [
      {
        name: '家长端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents',
        qrcode: '/images/qrcode/ios-parents-baby-download.png',
      },
      {
        name: '园丁端',
        downloadUrl:
          'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents.teacher',
        qrcode: '/images/qrcode/ios-teacher-baby-download.png',
      },
    ],
  },
];

const LaunchWeApp = (props: any) => {
  const [currentUrl, setCurrentUrl] = useState('');
  const hostname = typeof window !== 'undefined' ? window.location.host : '';
  const isPalmBabyApp = isPalmBaby(hostname);
  let APPID = process.env.NEXT_PUBLIC_WECHAT_APPID;
  if (isPalmBabyApp) {
    const userAgent =
      typeof window !== 'undefined' ? window.navigator.userAgent : '';
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    APPID = isIOS
      ? process.env.NEXT_PUBLIC_WECHAT_BABY_IOS_APPID
      : process.env.NEXT_PUBLIC_WECHAT_BABY_ANDROID_APPID;
  }

  useEffect(() => {
    console.log('APPID: ', APPID);
    console.log('isPalmBabyApp: ', isPalmBabyApp);
    console.log(
      'babyData[0]?.data[0]?.downloadUrl: ',
      babyData[0]?.data[0]?.downloadUrl
    );
    console.log(
      'data[0]?.data[0]?.downloadUrl: ',
      data[0]?.data[0]?.downloadUrl
    );
    setCurrentUrl(window.location.href);
    const btns = document.querySelectorAll('.wx-open-app');
    for (const btn of btns) {
      btn.addEventListener('ready', (e) => {
        console.log('ready: ');
      });
      btn.addEventListener('launch', (e) => {
        console.log('success');
        props.onClick?.();
      });
      btn.addEventListener('error', (e) => {
        if (isPalmBabyApp) {
          window.open(babyData[0]?.data[0]?.downloadUrl, '_blank');
        } else {
          window.open(data[0]?.data[0]?.downloadUrl, '_blank');
        }
        // 唤醒失败的情况下，可用于降级处理比如在此处跳转到应用宝
      });
    }
  }, []);
  return (
    <div>
      <wx-open-launch-app
        appid={APPID}
        class="wx-open-app"
        extinfo={props.extinfo || currentUrl}
        id="wx-open-app"
      >
        <script type="text/wxtag-template">
          <Button
            block
            shape="rounded"
            style={{
              width: props.size === 'small' ? '120px' : '164px',
              paddingTop: '10px',
              paddingBottom: '10px',
              background: 'linear-gradient(to right, #FDDF5D, #FFCA2C)',
              borderRadius: '50px',
              ...props.style,
            }}
          >
            <span style={{ color: '#333', ...props.textStyle }}>
              {props.text || 'APP内打开'}
            </span>
          </Button>
        </script>
      </wx-open-launch-app>
    </div>
  );
};
export default LaunchWeApp;
