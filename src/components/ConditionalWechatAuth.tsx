'use client';

import { usePathname } from 'next/navigation';

import { WechatAuthProvider } from './WechatAuthProvider';

interface ConditionalWechatAuthProps {
  children: React.ReactNode;
  repeatApiFn?: () => void;
  appId?: string;
  // 需要微信认证的页面路径列表
  includePaths?: string[];
}

export const ConditionalWechatAuth: React.FC<ConditionalWechatAuthProps> = ({
  children,
  repeatApiFn,
  appId,
  includePaths = []
}) => {
  const pathname = usePathname();
  // 检查当前路径是否在需要认证的列表中
  const shouldEnableAuth = includePaths.some((path) => {
    // 支持精确匹配和前缀匹配
    return pathname === path || pathname.startsWith(`${path}`);
  });
  return (
    <WechatAuthProvider
      repeatApiFn={repeatApiFn}
      appId={appId}
      disabled={!shouldEnableAuth}
    >
      {children}
    </WechatAuthProvider>
  );
};
