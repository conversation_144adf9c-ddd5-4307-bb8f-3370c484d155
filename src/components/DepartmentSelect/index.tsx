import { Checkbox, Toast } from 'antd-mobile';
import React, { useEffect, useState } from 'react';

import { getDepartmentList } from '@/api/invite';

type Item = {
  id: string;
  name: string;
};

interface TreeNode {
  id: string;
  name: string;
  children?: TreeNode[];
}

interface Props {
  instId: string;
  onSelect: (selected: Item[]) => void;
}

export default function DepartmentSelect({ instId, onSelect }: Props) {
  const [selectedKeys, setSelectedKeys] = useState<Item[]>([]);
  const [data, setData] = useState<TreeNode[]>([]);

  useEffect(() => {
    if (instId) {
      getDepartment();
    }
  }, [instId]);

  useEffect(() => {
    console.log('🚀 ~ file: index.tsx:28 ~ selectedKeys:', selectedKeys);
    onSelect(selectedKeys);
  }, [selectedKeys]);

  const getDepartment = () => {
    getDepartmentList({
      fetchChild: 1,
      instId,
    }).then((res: any) => {
      if (res) {
        setData([res]);
      } else {
        Toast.show('暂无部门数据');
      }
    });
  };

  const handleSelect = (selected: Item[]) => {
    setSelectedKeys(selected);
  };

  const handleCheck = (node: TreeNode, checked: boolean) => {
    const selected = checked
      ? [...selectedKeys, node, ...getAllChildIds(node)]
      : selectedKeys.filter(
          (item) => item.id !== node.id && !isChildOf(node, item.id)
        );
    // 判断是否

    handleSelect(selected);
  };

  const getAllChildIds = (node: TreeNode): Item[] => {
    let ids: Item[] = [];

    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids.push(child);
        ids = [...ids, ...getAllChildIds(child)];
      });
    }

    return ids;
  };

  // 判断一个节点是否是另一个节点的子节点
  const isChildOf = (parent: TreeNode, childId: string): boolean => {
    if (parent.children && parent.children.length > 0) {
      return parent.children.some((child) => {
        if (child.id === childId) {
          return true;
        }
        return isChildOf(child, childId);
      });
    }

    return false;
  };

  const renderTreeNode = (node: TreeNode) => {
    const hasChildren = node.children && node.children.length > 0;
    const isChecked = selectedKeys.some((item) => item.id === node.id);
    const isIndeterminate =
      !isChecked &&
      node.children &&
      node.children.every((child) =>
        selectedKeys.some((item) => item.id === child.id)
      );

    return (
      <li className="mt-4" key={node.id}>
        <Checkbox
          checked={isChecked}
          onChange={(checked) => handleCheck(node, checked)}
        >
          <div className="font-semibold">
            {/* {isIndeterminate ? '111' : '222'} */}
            {node.name}
          </div>
        </Checkbox>

        {hasChildren && (
          <ul className="mt-2 ml-8">
            {node.children?.map((child) => renderTreeNode(child))}
          </ul>
        )}
      </li>
    );
  };

  return <ul>{data.map((node) => renderTreeNode(node))}</ul>;
}
