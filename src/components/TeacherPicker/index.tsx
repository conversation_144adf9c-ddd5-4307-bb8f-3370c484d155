import {
  Button,
  Checkbox,
  Image,
  List,
  Popup,
  SearchBar,
  Toast,
} from 'antd-mobile';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { useImmer } from 'use-immer';

import { getDeptUser, searchTeacher } from '@/api/common';
import { PiCaretRightBold, PiMinusCircleFill } from '@/components/Icons';
import { defaultAvatar, departmentAvatar } from '@/constant/config';

export interface TeacherPickerRef {
  toggle(): void;
  initData(data: any[], isSelectDept: boolean): void;
}
interface Props {
  title: string;
  onConfirm: (data: any[]) => void;
}

type TreeNode = {
  id: string;
  name: string;
  level: number;
  children?: TreeNode[];
  staffList?: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
};

const TeacherPicker = forwardRef<TeacherPickerRef, Props>((props, ref) => {
  const { onConfirm, title } = props;
  const [data, setData] = useState<any[]>([]); // 所有数据
  const [value, setValue] = useState<TreeNode>(); // 当前页面显示的数据
  const [popupVisible, setPopupVisible] = useState(false);
  const [selectedData, setSelectedData] = useState<any[]>([]);

  const [breadcrumb, setBreadcrumb] = useImmer<TreeNode[]>([]);
  const [canSelectDept, setCanSelectDept] = useState<boolean>(false);
  const [keyword, setKeyword] = useState('');
  const [searchList, setSearchList] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    toggle() {
      setPopupVisible((prev) => !prev);
    },
    initData(data, isSelectDept) {
      setSelectedData(data);
      setCanSelectDept(isSelectDept);
    },
  }));

  const initData = useCallback(() => {
    getDeptUser({ parentId: 0, fetchChild: 1 }).then((res: any) => {
      setData(res);
      setValue(res);
      setBreadcrumb([{ ...res }]);
    });
  }, [setBreadcrumb]);

  useEffect(() => {
    initData();
  }, [initData]);

  const search = () => {
    if (keyword !== '') {
      searchTeacher(keyword).then((res: any) => {
        if (Array.isArray(res.list) && res.list.length) {
          setSearchList(res.list);
        } else {
          Toast.show('暂无你搜索的教师');
        }
      });
    }
  };

  const getTreeNodeData = (id: string) => {
    const newData = JSON.parse(JSON.stringify(data));
    const result = findInTree(newData, (node) => node.id === id);
    if (result) {
      setValue(result);
    }
  };

  const findInTree = (
    tree: TreeNode,
    predicate: (data: TreeNode) => boolean
  ): any => {
    // 如果当前节点满足条件，返回当前节点
    if (predicate(tree)) {
      setBreadcrumb((draft) => {
        const index = draft.findIndex((item) => item.id === tree.id);
        if (index === -1) {
          draft.push(tree);
        } else {
          draft.splice(index + 1);
        }
      });
      return tree;
    }

    // 如果当前节点有子节点，递归查找子节点
    if (Array.isArray(tree.children) && tree.children.length > 0) {
      for (const child of tree.children) {
        const result = findInTree(child, predicate);
        if (result) {
          return result;
        }
      }
    }
  };

  const submit = () => {
    setPopupVisible(false);
    onConfirm(selectedData);
    setSelectedData([]);
  };

  return (
    <Popup
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        minHeight: '56vh',
        maxHeight: '100vh',
        paddingTop: '10px',
      }}
      onMaskClick={() => {
        setPopupVisible(false);
      }}
      visible={popupVisible}
    >
      <div className="p-4 pt-2">
        <div className="mb-4 flex items-center justify-between text-base">
          <button
            className="text-stone-400"
            onClick={() => setPopupVisible(false)}
            type="button"
          >
            取消
          </button>
          <div className="text-lg text-stone-900">{title}</div>
          <button
            className="text-[#3B82F7]"
            onClick={() => submit()}
            type="button"
          >
            确定
          </button>
        </div>
        <div className="mb-4 flex items-center">
          <div className="flex-1">
            <SearchBar
              onChange={setKeyword}
              onClear={() => {
                setKeyword('');
                setSearchList([]);
              }}
              style={{
                '--border-radius': '100px',
              }}
            />
          </div>
          {keyword !== '' && (
            <div className="ml-2">
              <Button
                color="primary"
                // shape="rounded"
                onClick={() => {
                  search();
                }}
                size="small"
              >
                搜索
              </Button>
            </div>
          )}
        </div>
        <div className="flex flex-wrap">
          {selectedData.map((item) => (
            <div
              className="relative mb-4 flex w-1/5 flex-col items-center px-2"
              key={item.id}
            >
              <Image
                alt=""
                className="rounded-full object-cover"
                fit="fill"
                height={50}
                src={
                  item.avatar ||
                  (item.type === 'user' ? defaultAvatar : departmentAvatar)
                }
                width={50}
              />
              <span className="mt-1 w-[120px] truncate text-center">
                {item.name || '无名字'}
              </span>
              <button
                className="-top-2 absolute right-1 rounded-full bg-white"
                onClick={() => {
                  setSelectedData((prev) =>
                    prev.filter((selectedItem) => selectedItem.id !== item.id)
                  );
                }}
                type="button"
              >
                <PiMinusCircleFill color="#FF6767" size="18px" />
              </button>
            </div>
          ))}
        </div>
        {searchList.length ? (
          <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
            <List
              style={{
                // '--border-bottom': 'none',
                '--border-top': 'none',
                '--border-inner': 'solid 1px #F1F1F1',
              }}
            >
              {searchList?.map((item) => {
                return (
                  <List.Item
                    arrow={false}
                    key={item.id}
                    prefix={
                      <button
                        onClick={(e) => e.stopPropagation()}
                        type="button"
                      >
                        <Checkbox
                          checked={selectedData.some(
                            (selectedItem) => selectedItem.id === item.id
                          )}
                          key={item.id}
                          onChange={(checked) => {
                            if (checked) {
                              setSelectedData((prev) => [
                                ...prev,
                                {
                                  id: item.id,
                                  name: item.name,
                                  avatar: item.avatar || defaultAvatar,
                                  type: 'user',
                                },
                              ]);
                            } else {
                              setSelectedData((prev) =>
                                prev.filter(
                                  (selectedItem) => selectedItem.id !== item.id
                                )
                              );
                            }
                          }}
                        />
                      </button>
                    }
                  >
                    <div className="flex flex-1 justify-between">
                      <div>{item.name}</div>
                    </div>
                  </List.Item>
                );
              })}
            </List>
          </div>
        ) : (
          <>
            <div className="mb-3 flex items-center">
              {breadcrumb.map((item, _index) => {
                return (
                  <button
                    className="flex items-center"
                    key={item.id}
                    onClick={() => {
                      getTreeNodeData(item.id);
                    }}
                    type="button"
                  >
                    <span className="mr-1 text-base">{item.name}</span>
                    <PiCaretRightBold color="#D8D8D8" size="18px" />
                  </button>
                );
              })}
            </div>
            <div className="flex max-h-[50vh] flex-col overflow-y-scroll">
              <List
                style={{
                  // '--border-bottom': 'none',
                  '--border-top': 'none',
                  '--border-inner': 'solid 1px #F1F1F1',
                }}
              >
                {value &&
                  Array.isArray(value.children) &&
                  value.children?.map((item) => {
                    return (
                      <List.Item
                        arrow={false}
                        key={item.id}
                        prefix={
                          canSelectDept ? (
                            <button
                              onClick={(e) => e.stopPropagation()}
                              type="button"
                            >
                              <Checkbox
                                checked={selectedData.some(
                                  (selectedItem) => selectedItem.id === item.id
                                )}
                                key={item.id}
                                onChange={(checked) => {
                                  if (checked) {
                                    setSelectedData((prev) => [
                                      ...prev,
                                      {
                                        id: item.id,
                                        name: item.name,
                                        avatar: departmentAvatar,
                                        type: 'dept',
                                      },
                                    ]);
                                  } else {
                                    setSelectedData((prev) =>
                                      prev.filter(
                                        (selectedItem) =>
                                          selectedItem.id !== item.id
                                      )
                                    );
                                  }
                                }}
                              />
                            </button>
                          ) : null
                        }
                      >
                        <div className="flex flex-1 justify-between">
                          <div>{item.name}</div>
                          {((item.children && item.children.length > 0) ||
                            (item.staffList && item.staffList.length > 0)) && (
                            <button
                              className="flex items-center"
                              onClick={() => {
                                getTreeNodeData(item.id);
                              }}
                              type="button"
                            >
                              <span className="mr-1 text-[#4E78FF]">下级</span>
                              <PiCaretRightBold color="#D8D8D8" size="18px" />
                            </button>
                          )}
                        </div>
                      </List.Item>
                    );
                  })}
                {value &&
                  Array.isArray(value.staffList) &&
                  value.staffList?.map((item) => {
                    return (
                      <List.Item
                        arrow={false}
                        key={item.id}
                        prefix={
                          <button
                            onClick={(e) => e.stopPropagation()}
                            type="button"
                          >
                            <Checkbox
                              checked={selectedData.some(
                                (selectedItem) => selectedItem.id === item.id
                              )}
                              key={item.id}
                              onChange={(checked) => {
                                console.log('item', item);
                                if (checked) {
                                  setSelectedData((prev) => [
                                    ...prev,
                                    {
                                      id: item.id,
                                      name: item.name,
                                      avatar: item.avatar,
                                      type: 'user',
                                    },
                                  ]);
                                } else {
                                  setSelectedData((prev) =>
                                    prev.filter(
                                      (selectedItem) =>
                                        selectedItem.id !== item.id
                                    )
                                  );
                                }
                              }}
                            />
                          </button>
                        }
                      >
                        <div className="flex flex-1 justify-between">
                          <div>{item.name}</div>
                        </div>
                      </List.Item>
                    );
                  })}
              </List>
            </div>
          </>
        )}
      </div>
    </Popup>
  );
});

TeacherPicker.displayName = 'TeacherPicker';

export default TeacherPicker;
