'use client';

import { Toast } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import api from '@/lib/api';
import { useWechatStore } from '@/store/useWechatStore';
import { getBrowser } from '@/utils';

interface UseWechatAuthOptions {
  repeatApiFn?: () => void;
  appId?: string;
}

export const useWechatAuth = (options: UseWechatAuthOptions = {}) => {
  const { repeatApiFn, appId = 'wx6a94897f8449fdea' } = options;
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setUnionId, setOpenId } = useWechatStore();
  useEffect(() => {
    if (typeof window === 'undefined') return;
    const handleWechatAuth = async () => {
      if (getBrowser() === 'wechat') {
        const unionId = Cookies.get('unionId');
        const openId = Cookies.get('openId');
        const code = searchParams.get('code');
        if (unionId || openId) {
          setUnionId(unionId);
          setOpenId(openId);
          repeatApiFn?.();
        } else if (code) {
          try {
            const response: any = await api.get(
              `${process.env.NEXT_APP_API_HOST}/v1/auth/wx/public/openid`,
              {
                params: {
                  code,
                  platform: 1
                }
              }
            );
            console.log('code', code);
            if (response.unionId) {
              Cookies.set('unionId', response.unionId);
              Cookies.set('openId', response.openId);
              setUnionId(response.unionId);
              setOpenId(response.openId);
              window.location.reload();
              setTimeout(() => {
                repeatApiFn?.();
              }, 100);
            } else {
              Toast.show({ content: '微信授权失败' });
              throw new Error(response.message || '授权失败');
            }
          } catch (error) {
            console.error('微信授权失败:', error);
            Toast.show({ content: `微信授权失败:${error.message}` });
            // router.push('/404');
          }
        } else {
          const redirectUrl = encodeURIComponent(window.location.href);
          const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_userinfo#wechat_redirect`;
          console.log('authUrl: ', authUrl);
          window.location.href = authUrl;
        }
      }
    };

    handleWechatAuth();
  }, [searchParams, router, setUnionId, repeatApiFn, appId]);
};
