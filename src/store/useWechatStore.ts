import { create } from 'zustand';

interface WechatState {
  unionId: string | null;
  openId: string | null;
  setUnionId: (unionId: string) => void;
  setOpenId: (openId: string) => void;
  clearUnionId: () => void;
  clearOpenId: () => void;
}

export const useWechatStore = create<WechatState>((set) => ({
  unionId: null,
  openId: null,
  setUnionId: (unionId: string) => set({ unionId }),
  setOpenId: (openId: string) => set({ openId }),
  clearUnionId: () => set({ unionId: null }),
  clearOpenId: () => set({ openId: null })
}));
