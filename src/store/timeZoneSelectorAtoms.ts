import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

// 导入类型定义
export interface SemesterOption {
  id: string;
  name: string;
  start: string;
  end: string;
}

export interface MonthOption {
  id: string;
  name: string;
  start: string;
  end: string;
}

export interface CustomDateRange {
  startDate: Date | null;
  endDate: Date | null;
}

// 区域选择相关类型定义
export interface RegionCategory {
  zoneId: string;
  zoneName: string;
  parentId: string;
  children: RegionCategory[];
}

// 默认值定义
const defaultSemesterOption: SemesterOption = {
  id: '',
  name: '请选择学期',
  start: '',
  end: '',
};

const defaultMonthOption: MonthOption = {
  id: '',
  name: '请选择月份',
  start: '',
  end: '',
};

const defaultCustomDateRange: CustomDateRange = {
  startDate: null,
  endDate: null,
};

const defaultSelectedRegions: RegionCategory[] = [];

// 使用 atomWithStorage 实现持久化存储
export const selectedSemesterAtom = atomWithStorage<SemesterOption>(
  'timeSelector_selectedSemester',
  defaultSemesterOption
);

export const selectedMonthAtom = atomWithStorage<MonthOption>(
  'timeSelector_selectedMonth',
  defaultMonthOption
);

export const customDateRangeAtom = atomWithStorage<CustomDateRange>(
  'timeSelector_customDateRange',
  defaultCustomDateRange,
  {
    // 自定义序列化和反序列化，处理 Date 对象
    getItem: (key: string, initialValue: CustomDateRange) => {
      try {
        const storedValue = localStorage.getItem(key);
        if (storedValue === null) {
          return initialValue;
        }

        const parsed = JSON.parse(storedValue);
        return {
          startDate: parsed.startDate ? new Date(parsed.startDate) : null,
          endDate: parsed.endDate ? new Date(parsed.endDate) : null,
        };
      } catch {
        return initialValue;
      }
    },
    setItem: (key: string, value: CustomDateRange) => {
      const serialized = {
        startDate: value.startDate ? value.startDate.toISOString() : null,
        endDate: value.endDate ? value.endDate.toISOString() : null,
      };
      localStorage.setItem(key, JSON.stringify(serialized));
    },
    removeItem: (key: string) => {
      localStorage.removeItem(key);
    },
  }
);

export const activeTabAtom = atomWithStorage<'semester' | 'month' | 'custom'>(
  'timeSelector_activeTab',
  'semester'
);

// 区域选择相关的 atom
export const selectedRegionsAtom = atom<RegionCategory[]>(
  defaultSelectedRegions
);

// 派生 atom：获取当前选中的区域
export const currentSelectedRegionsAtom = atom((get) => {
  return get(selectedRegionsAtom);
});

// 派生 atom：获取当前选中的时间范围
export const currentTimeRangeAtom = atom((get) => {
  const activeTab = get(activeTabAtom);
  const selectedSemester = get(selectedSemesterAtom);
  const selectedMonth = get(selectedMonthAtom);
  const customDateRange = get(customDateRangeAtom);

  if (activeTab === 'semester' && selectedSemester.id) {
    return {
      startTime: selectedSemester.start,
      endTime: selectedSemester.end,
    };
  }

  if (activeTab === 'month' && selectedMonth.id) {
    return {
      startTime: selectedMonth.start,
      endTime: selectedMonth.end,
    };
  }

  if (
    activeTab === 'custom' &&
    customDateRange.startDate &&
    customDateRange.endDate
  ) {
    const formatDate = (date: Date, isEndDate = false) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const time = isEndDate ? '23:59:59' : '00:00:00';
      return `${year}-${month}-${day} ${time}`;
    };

    return {
      startTime: formatDate(customDateRange.startDate, false),
      endTime: formatDate(customDateRange.endDate, true),
    };
  }

  return null;
});

// 重置所有状态的 atom
export const resetTimeSelectorAtom = atom(null, (_get, set) => {
  set(selectedSemesterAtom, defaultSemesterOption);
  set(selectedMonthAtom, defaultMonthOption);
  set(customDateRangeAtom, defaultCustomDateRange);
  set(activeTabAtom, 'semester');
  set(selectedRegionsAtom, defaultSelectedRegions);
});

// 重置区域选择的 atom
export const resetRegionSelectorAtom = atom(null, (_get, set) => {
  set(selectedRegionsAtom, defaultSelectedRegions);
});
