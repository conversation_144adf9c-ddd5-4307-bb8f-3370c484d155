import Cookies from 'js-cookie';
import { create } from 'zustand';

interface State {
  authorization: string;
  setAuthorization: (authorization: string) => void;
  version: string;
  setVersion: (version: string) => void;
  appType: string;
  setAppType: (appType: string) => void;
  brand: string;
  setBrand: (brand: string) => void;
  userId: string;
  setUserId: (userId: string) => void;
  studentId: string;
  setStudentId: (studentId: string) => void;
  studentName: string;
  setStudentName: (studentName: string) => void;
}

export const useCommonStore = create<State>((set) => ({
  authorization: '',
  userId: '',
  studentId: '',
  studentName: '',
  version: '',
  appType: '2',
  brand: '2',
  setAuthorization: (authorization: string) => {
    Cookies.set('Authorization', authorization);
    set(() => ({ authorization }));
  },
  setVersion: (version: string) => set(() => ({ version })),
  setAppType: (appType: string) => set(() => ({ appType })),
  setBrand: (brand: string) => set(() => ({ brand })),
  setUserId: (userId: string) => set(() => ({ userId })),
  setStudentId: (studentId: string) => set(() => ({ studentId })),
  setStudentName: (studentName: string) => set(() => ({ studentName })),
}));
