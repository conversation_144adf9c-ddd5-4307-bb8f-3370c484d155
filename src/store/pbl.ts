import { atom } from 'jotai';
import type { FileType } from '@/components/UploadFile/types';

export const isEditableAtom = atom(false);

export const projectDataAtom = atom({
  deptId: '',
  projectId: '',
  projectName: '',
  description: '',
  ageRange: '',
  learningGoals: '',
  drivingQuestion: '',
  evaluationCriteria: '',
  resources: '',
  startDate: new Date(),
  endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3),
});

export const studentAtom = atom({
  id: '',
  name: '',
  avatar: '',
  deptId: '',
  evaluation: {
    evaluationId: '',
    observationId: '',
    abilities: [],
  },
});

export const mediaAtom = atom({
  observationId: '',
  deptId: '',
  medias: [] as FileType[],
});

// 键是 studentId (string)，值是另一个对象 Record<string, boolean>
// 这个内部对象的键是 abilityId (string)，值是 boolean (表示是否选中)
export const evaluationAtom = atom<Record<string, Record<string, boolean>>>({});
export const markdownAtom = atom<string>('');
