import type { SchemaBase } from 'form-render-mobile';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { nanoid } from '@/utils';

type ItemType = SchemaBase & { id: string; order?: number };

type BaseForm = {
  instanceName: string;
  iconUrl: string;
  remark: string;
  cateId: string;
  cateName?: string;
};

type SettingForm = {
  cycleType: number;
  cycleTime: string[];
  isRepeat: number;
  isHoliday: number;
  endTime: number;
  remindTime: [number, number, number, number];
  isCustom: boolean;
  settings: string | null;
  scope: Array<{
    id: string;
    type: string;
    name?: string;
  }>;
};

type User = {
  id: string;
  name: string;
  avatar: string;
};

type State = {
  baseForm: BaseForm;
  settingForm: SettingForm;
  forms: ItemType[];
  users: Record<string, User>;
  widgetId: string; // 用于存储当前编辑的组件ID
};

type Actions = {
  setBaseForm: (data: BaseForm) => void;
  setSettingForm: (data: SettingForm) => void;
  setForm: (data: ItemType[]) => void;
  addFormItem: (item: ItemType) => void;
  updateFormItem: (id: string, item: ItemType) => void;
  removeFormItem: (id: string) => void;
  addUser: (user: User) => void;
  getUser: (id: string) => User | undefined;
  setWidgetId: (id: string) => void;
  // 分组相关方法
  addGroupChildItem: (groupId: string, item: ItemType) => void;
  removeGroupChildItem: (groupId: string, childId: string) => void;
  updateGroupChildItem: (
    groupId: string,
    childId: string,
    item: ItemType
  ) => void;
  copyGroupItem: (groupId: string) => void;
};

export const useFormStore = create<State & Actions>()(
  immer((set, get) => ({
    baseForm: {
      instanceName: '',
      iconUrl: '',
      remark: '',
      cateId: '',
      cateName: '',
    },
    settingForm: {
      cycleType: 0,
      cycleTime: ['1', '2', '3', '4', '5'],
      isRepeat: 1,
      isHoliday: 0,
      endTime: 0,
      remindTime: [0, 0, 0, 0],
      isCustom: false,
      scope: [],
      settings: null,
    },
    forms: [],
    setBaseForm: (data) =>
      set((state) => {
        state.baseForm = data;
      }),
    setSettingForm: (data) =>
      set((state) => {
        state.settingForm = data;
      }),
    setForm: (data) =>
      set((state) => {
        state.forms = data;
      }),
    addFormItem: (item: ItemType) =>
      set((state) => {
        state.forms.push(item);
      }),
    updateFormItem: (id, item) => {
      set((state) => {
        const index = state.forms.findIndex((form) => form.id === id);
        if (index > -1) {
          state.forms[index] = item;
        }
      });
    },
    removeFormItem: (id) => {
      set((state) => {
        const index = state.forms.findIndex((form) => form.id === id);
        if (index > -1) {
          state.forms.splice(index, 1);
        }
      });
    },
    users: {},
    addUser: (user) =>
      set((state) => {
        state.users[user.id] = user;
      }),
    getUser: (id) => {
      const state = get();
      return state.users[id];
    },
    widgetId: '',
    setWidgetId: (id) => {
      set((state) => {
        state.widgetId = id;
      });
    },
    // 分组相关方法实现
    addGroupChildItem: (groupId, item) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (groupForm && groupForm.widget === 'group') {
            if (!groupForm.properties) {
              groupForm.properties = {};
            }
            // 确保 item 包含 id 属性
            if (!item.id) {
              item.id = `form-${nanoid(12)}`;
            }
            // 计算新的 order 值，基于现有对象长度
            const existingItems = Object.values(groupForm.properties || {});
            const newOrder = existingItems.length;
            item.order = newOrder;
            groupForm.properties[item.id] = item;
          }
        }
      });
    },
    removeGroupChildItem: (groupId, childId) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (
            groupForm &&
            groupForm.widget === 'group' &&
            groupForm.properties
          ) {
            delete groupForm.properties[childId];
          }
        }
      });
    },
    updateGroupChildItem: (groupId, childId, item) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const groupForm = state.forms[groupIndex];
          if (
            groupForm &&
            groupForm.widget === 'group' &&
            groupForm.properties
          ) {
            groupForm.properties[childId] = item;
          }
        }
      });
    },
    copyGroupItem: (groupId) => {
      set((state) => {
        const groupIndex = state.forms.findIndex((form) => form.id === groupId);
        if (groupIndex > -1) {
          const originalGroup = state.forms[groupIndex];
          if (originalGroup && originalGroup.widget === 'group') {
            // 深度复制分组对象
            const copiedGroup = JSON.parse(JSON.stringify(originalGroup));

            // 生成新的分组 ID
            copiedGroup.id = `form-${nanoid(12)}`;

            // 如果有子项，为每个子项生成新的 ID
            if (copiedGroup.properties) {
              const newProperties: Record<string, ItemType> = {};
              for (const child of Object.values(
                copiedGroup.properties
              ) as ItemType[]) {
                const newChildId = `form-${nanoid(12)}`;
                const copiedChild = { ...child, id: newChildId };
                newProperties[newChildId] = copiedChild;
              }
              copiedGroup.properties = newProperties;
            }

            // 将复制的分组添加到表单列表中，紧跟在原分组后面
            state.forms.splice(groupIndex + 1, 0, copiedGroup);
          }
        }
      });
    },
  }))
);
