import { customAlphabet } from 'nanoid';

// 手机号码验证
export function validateChineseMobileNumber(phoneNumber: string): boolean {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phoneNumber);
}

declare global {
  interface Window {
    ReactNativeWebView?: {
      postMessage: (data: any) => void;
    };
    WebViewBridge?: {
      onMessage: (data: any) => void;
    };
    android?: any;
    webkit?: any;
    hinaDataStatistic?: any;
  }
}

// 随机字符串
export function generateString(length: number) {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

// nanoid 随机字符串
export function nanoid(length: number) {
  const alphabet =
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const nanoid = customAlphabet(alphabet, length);
  return nanoid();
}

// 判断是否全面屏
export function isBigScreen() {
  // ，这里根据返回值 true 或false ,返回true的话 则为全面屏
  let result = false;

  if (typeof window !== 'undefined') {
    const rate = window.screen.height / window.screen.width;
    const limit =
      window.screen.height === window.screen.availHeight ? 1.8 : 1.65; // 临界判断值

    if (rate > limit) {
      result = true;
    }
  }

  return result;
}

// 模拟点击事件
export const addClickEvent = (
  node: any,
  isCancelBubble: boolean,
  callback: any
) => {
  const start = {
    // 记录开始的触点
    x: 0,
    y: 0,
  };
  const delta = {
    // 记录滑动距离
    x: 0,
    y: 0,
  };
  let startTime = 0; // 开始时间
  let curTime = 0; // 从开始到现在的事件
  const longTime = 200; // 200ms 触发长按事件
  const longCurTime = 1000; // 1000ms 手指不移开即可触发 通过定时器往上加
  let target: any = null;
  const minDelta = 50; // 最小滑动距离
  let timer: any = null;

  node.on('touchstart', (e: any) => {
    // 阻止点击上层
    if (isCancelBubble) {
      e.cancelBubble = true;
    }
    // if (e.target !== node) {
    //   return;
    // }
    // event.stopPropagation();
    const event = e.evt;

    const touch = event.touches[0];
    // 判断默认行为是否可以被禁用
    if (event.cancelable) {
      // 判断默认行为是否已经被禁用
      if (!event.defaultPrevented) {
        event.preventDefault();
      }
    }
    target = event.target;
    // 获取开始的触点
    start.x = touch.pageX;
    start.y = touch.pageY;
    delta.x = 0;
    delta.y = 0;
    // 当前触摸时的时间
    startTime = new Date().getTime();
    // 持续触摸不动 400ms 则生成 longCurTap 事件
    timer = setInterval(() => {
      curTime += 10;
      if (curTime === longCurTime) {
        // 长按事件
      }
      // console.log(curTime);
    }, 1);
  });

  node.on('touchmove', (e: any) => {
    const event = e.evt;
    const touch = event.touches[0];
    // 计算滑动的距离
    delta.x = touch.pageX - start.x;
    delta.y = touch.pageY - start.y;
  });
  node.on('touchend', (e: any) => {
    const event = e.evt;
    console.log('touchend');
    // 手指移开后先清除定时器（服务于 longCurTap）
    clearInterval(timer);
    curTime = 0;
    // 获取自上次触摸以来发生了什么改变的 touch 对象的数组
    const touch = event.changedTouches[0];
    const deltaTime = new Date().getTime() - startTime;
    // 返回滑动距离的绝对值
    const deltaX = Math.abs(delta.x);
    const deltaY = Math.abs(delta.y);

    // 判断位置是否发生改变
    if (touch.pageX === start.x && touch.pageY === start.y) {
      // 判断触摸时间是否小于 200ms
      if (deltaTime < longTime) {
        callback(); // 触摸事件
      } else if (deltaTime > longTime && deltaTime < longCurTime / 2) {
        // 长按事件
      }
    }
  });
};

// 自定义进制转换
export function customToDecimal(numStr: string) {
  if (numStr === '00') return 0;
  if (numStr === '01') return 1;
  if (numStr === '10') return 2;
  if (numStr === '11') return 3;

  if (numStr.startsWith('2')) {
    let result = 4;
    let power = 0;

    for (let i = 1; i < numStr.length; i++) {
      const digit = numStr[i];
      if (digit === '9') {
        result += 9 * 10 ** power;
        power++;
      } else {
        result += Number.parseInt(digit, 10) * 10 ** power;
      }
    }
    return result;
  }

  throw new Error('Invalid input: must start with 00, 01, 10, 11, or 2');
}
// 发送信息给RN
export const postMessage = (postMessageData: object) => {
  try {
    window.ReactNativeWebView?.postMessage(JSON.stringify(postMessageData));
  } catch (error) {
    // console.log('发送信息给 app 出错')
  }
};

// 获取到 RN 的通知
export const getMessage = (onMessage: (data: any) => void) => {
  console.log('获取 RN 信息', onMessage);
  try {
    // window.WebViewBridge = {
    //   onMessage, // 在 window 上挂载一个 onMessage 方法，RN 会调用
    // };
    // 自定义事件后直接触发：
    window.addEventListener('message', onMessage, false);
    document.addEventListener('message', onMessage, false);
    // const event = new Event('WebViewBridge');
    // window.dispatchEvent(event);
  } catch (error) {
    console.log('getMessage error', error);
  }
};

export const getNativeMessage = (onMessage: (data: any) => void) => {
  console.log('获取 RN 信息', onMessage);
  try {
    window.WebViewBridge = {
      onMessage, // 在 window 上挂载一个 onMessage 方法，RN 会调用
    };
    // 自定义事件后直接触发：
    const event = new Event('WebViewBridge');
    window.dispatchEvent(event);
  } catch (error) {
    console.log('error', error);
  }
};

// 防抖
export const debounce = <T extends (...args: any[]) => any>(
  fn: T,
  ms = 300
) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

export function getMobile() {
  const u = navigator.userAgent;
  const isAndroid = u.includes('Android') || u.includes('Adr'); // android 终端
  const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios 终端
  if (isiOS) {
    return 'ios';
  }
  if (isAndroid) {
    return 'android';
  }
  return 'unknow';
}

// 判断是否微信浏览器环境
export function getBrowser() {
  const browser = navigator.userAgent.toLowerCase();
  if (/micromessenger/.test(browser)) {
    return 'wechat';
  }
  return 'other';
}

// 百度统计
export function baidu(data: any) {
  console.log('百度统计', data);
  try {
    if (typeof window !== 'undefined' && (window as any)._hmt) {
      (window as any)._hmt.push(data);
    }
  } catch (e) {
    console.log(e);
  }
}

// 图片缩略图
export function imageThumbnail(
  cover: string,
  w: number,
  h: number,
  format = 'jpg'
) {
  if (!cover) {
    return '';
  }
  let newUrl;
  const domain = cover.split('/');
  if (domain[2] === 'unicorn-media.ancda.com') {
    newUrl = `${cover}?x-image-process=image/resize,m_fill,w_${w},h_${h},limit_0/format,${format}`;
  }
  return newUrl || cover;
}

// 视频缩略图
export function videoThumbnail(url: string) {
  let newUrl = '';
  if (url && url.indexOf('unicorn-media.ancda.com') > -1) {
    newUrl = `${url}?x-workflow-graph-name=video-thumbnail`;
  } else {
    newUrl = url;
  }
  return newUrl;
}

// 显示原生小红花弹窗
export function showNativeRewardModal(
  taskId: string,
  flowerCount: number,
  signDays: number
) {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.showFlowerReward(taskId, flowerCount, signDays);
    } else if (device === 'ios') {
      window.webkit.messageHandlers.showFlowerReward.postMessage({
        taskId,
        flowerCount,
        signDays,
      });
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}

export const getAge = (birthday: string) => {
  if (birthday === '0' || Number(birthday) <= 0 || !birthday) {
    return '';
  }
  const birthDayTime = new Date(Number(birthday) * 1000);
  const nowTime = new Date();
  let yearStr = Math.floor(nowTime.getFullYear() - birthDayTime.getFullYear());
  let monthStr = Math.floor(nowTime.getMonth() - birthDayTime.getMonth());
  let dayStr = Math.floor(nowTime.getDate() - birthDayTime.getDate());
  if (dayStr <= 0) {
    monthStr -= 1;
    dayStr = 30 + dayStr;
  }
  if (dayStr === 31) {
    monthStr += 1;
    dayStr = 1;
  }
  if (monthStr <= 0) {
    yearStr -= 1;
    monthStr = 12 + monthStr;
  }
  if (monthStr === 12) {
    yearStr += 1;
    monthStr = 0;
  }
  const yearTo = yearStr ? `${yearStr}岁  ` : '';
  const monthTo = monthStr ? `${monthStr}个月  ` : '';
  const dayTo = dayStr ? `${dayStr}天` : '';
  const ageDay = yearTo + monthTo + dayTo;
  if (yearStr < 0) {
    return `${0}天`;
  }
  return ageDay;
};

// 格式化时长,秒格式化为：00:00:00
export const formatDuration = (duration?: string | number): string => {
  if (duration === undefined || duration === null) {
    return '00:00';
  }
  const totalSeconds =
    typeof duration === 'string' ? Number.parseInt(duration, 10) : duration;
  if (Number.isNaN(totalSeconds) || totalSeconds < 0) {
    return '00:00';
  }

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');

  if (hours > 0) {
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  }
  return `${formattedMinutes}:${formattedSeconds}`;
};

export function copyToClipboard(text: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (navigator.clipboard && window.isSecureContext) {
      // 对于现代浏览器，使用 Clipboard API
      navigator.clipboard
        .writeText(text)
        .then(() => resolve(true))
        .catch(() => resolve(false));
    } else {
      // 后备方案：使用较旧的 execCommand 方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        resolve(successful);
      } catch (err) {
        document.body.removeChild(textArea);
        resolve(false);
      }
    }
  });
}

// 是否开启通知权限
export function getNotificationsEnabled() {
  let isEnable = false;
  try {
    const device = getMobile();
    if (device === 'android') {
      isEnable = window.android.areNotificationsEnabled();
    } else if (device === 'ios') {
      isEnable =
        window.webkit.messageHandlers.areNotificationsEnabled.postMessage({});
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
  return isEnable;
}

// 跳转原生页面
export function navigationToNativePage(path: string) {
  console.log('navigationToNativePage', path);
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.launch(path);
    } else if (device === 'ios') {
      window.webkit.messageHandlers.launch.postMessage({
        path,
      });
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e) {
    console.log(e);
  }
}

// 打开系统通知
export function openNotificationsSetting() {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.openSystemSettings(false);
    } else if (device === 'ios') {
      window.webkit.messageHandlers.openSystemSettings.postMessage(false);
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}

export function share(data: any) {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.share(JSON.stringify(data));
    } else if (device === 'ios') {
      window.webkit.messageHandlers.share.postMessage(JSON.stringify(data));
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}
export function canCallAppPay(): boolean {
  try {
    const device = getMobile();
    if (device === 'android') {
      // 检查android bridge是否存在以及是否有pay方法
      if (window.android && typeof window.android.pay === 'function') {
        return true;
      }
    } else if (device === 'ios') {
      // 检查iOS bridge是否存在以及是否有pay方法
      if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.pay
      ) {
        return true;
      }
    }
    // 不支持的设备类型或bridge不可用时返回false，不抛出异常
    return false;
  } catch (e: any) {
    console.log(e.message);
    return false;
  }
}
export function openPicker(options: any) {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.openPicker(JSON.stringify(options));
    } else if (device === 'ios') {
      window.webkit.messageHandlers.openPicker.postMessage({ options });
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}

export function canObserveAddMedia(): boolean {
  try {
    const device = getMobile();
    if (device === 'android' && window.android.observeAddMedia) {
      return true;
    }
    if (
      device === 'ios' &&
      window.webkit &&
      window.webkit.messageHandlers &&
      window.webkit.messageHandlers.observeAddMedia
    ) {
      return true;
    }
    return false;
  } catch (e: any) {
    console.log(e.message);
    return false;
  }
}
export function observeAddMedia(data: any) {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.observeAddMedia(JSON.stringify(data));
    } else if (device === 'ios') {
      window.webkit.messageHandlers.observeAddMedia.postMessage({ data });
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}

export function appPay(data: any) {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.pay(Number(data.payMethod), data.orderNo);
    } else if (device === 'ios') {
      window.webkit.messageHandlers.pay.postMessage(data);
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}
export function onBackPressed() {
  try {
    const device = getMobile();
    if (device === 'android') {
      window.android.onBackPressed();
    } else if (device === 'ios') {
      window.webkit.messageHandlers.onBackPressed.postMessage({});
    } else {
      throw new Error('方法执行出错');
    }
  } catch (e: any) {
    console.log(e.message);
  }
}

export function hinaTrack(name: string, data?: any) {
  setTimeout(() => {
    if (typeof window.hinaDataStatistic !== 'undefined') {
      if (data) {
        console.log('window.hinaDataStatistic: ', window.hinaDataStatistic);
        window.hinaDataStatistic.track(name, data);
      } else {
        console.log('window.hinaDataStatistic: ', window.hinaDataStatistic);
        window.hinaDataStatistic.track(name);
      }
    }
  }, 1000);
}

export const handelGoBack = (router: any) => {
  const device = getMobile();
  if (device === 'android') {
    window.android?.onBackPressed();
  } else if (device === 'ios') {
    window.webkit?.messageHandlers?.onBackPressed?.postMessage({
      animated: true,
    });
  } else {
    router.back();
  }
};

export function hinaQuick(name: string) {
  setTimeout(() => {
    if (typeof window.hinaDataStatistic !== 'undefined') {
      window.hinaDataStatistic.quick(name);
    }
  }, 1000);
}

export function hinaRegisterCommonProperties(data: any) {
  setTimeout(() => {
    if (typeof window.hinaDataStatistic !== 'undefined') {
      window.hinaDataStatistic.registerCommonProperties({
        ...data,
      });
    }
  }, 1000);
}

/**
 * 上传视频
 * @param needCompress 是否需要压缩
 * @param uploadDir 上传目录
 * @returns Promise<unknown> 上传结果
 */
export function uploadVideo(
  maxCount: number,
  maxVideoDuration: number,
  needCompress: boolean,
  uploadDir: string,
  videoCompressQuality: number,
  videoCompressBitrate: number
): Promise<unknown> {
  return new Promise((resolve, reject) => {
    try {
      const device = getMobile();
      if (device === 'android') {
        if (window.android.selectVideoAndUploadV2) {
          window.android.selectVideoAndUploadV2(
            JSON.stringify({
              maxCount,
              maxVideoDuration,
              needCompress,
              uploadDir,
              videoCompressQuality: 3,
              videoCompressBitrate,
            })
          );
        } else {
          window.android.selectVideoAndUpload(
            maxCount,
            maxVideoDuration,
            needCompress,
            uploadDir,
            videoCompressQuality
          );
        }
      } else if (device === 'ios') {
        window.webkit.messageHandlers.selectVideoAndUpload.postMessage(
          JSON.stringify({
            maxCount,
            maxVideoDuration,
            needCompress,
            uploadDir,
            videoCompressQuality,
            videoCompressBitrate,
          })
        );
      } else {
        reject(new Error('不支持的设备类型，只能在 iOS 或 Android 设备上使用'));
      }
    } catch (e: unknown) {
      reject(
        new Error(`上传视频失败：${e instanceof Error ? e.message : String(e)}`)
      );
    }
  });
}
