const ObsClient = require('esdk-obs-nodejs');

export const obsConfig = {
  access_key_id: 'HBXFOYBA43X4KL6BPR6R',
  secret_access_key: '5ulJAro65PgCSclKGpMBqVVAxxVeaxketeOfwW7A',
  server: 'https://obs.cn-north-4.myhuaweicloud.com',
};

export const updatedFile = (fileName: string, file: any) => {
  const obsClient = new ObsClient(obsConfig);
  return new Promise((resolve, reject) => {
    obsClient.putObject(
      {
        Bucket: 'unicorn-media',
        Key: fileName,
        ContentType: 'application/pdf',
        SourceFile: file,
      },
      (err: any, result: any) => {
        if (err) {
          reject();
        } else if (result.CommonMsg.Status < 300 && result.InterfaceResult) {
          resolve(`https://unicorn-media.ancda.com/${fileName}`);
        } else {
          reject();
        }
      }
    );
  });
};
