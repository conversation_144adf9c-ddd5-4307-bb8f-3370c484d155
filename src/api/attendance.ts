/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
import api from '@/lib/api';

// 考勤周报列表
export const getAttendanceReportList = async (params: any) => {
  return await api.get('/app/v1/attendance/weekly-reports', {
    params,
  });
};

// 考勤周报详情
export const getAttendanceReportDetail = async (id: string) => {
  return await api.get(`/app/v1/attendance/weekly-reports/${id}`);
};

// 非会员免费权益计数
export const getVipFreeBenefits = async () => {
  return await api.get('/app/v1/vip/free-benefits');
};

// 考勤周报列表顶部概览数据
export const getAttendanceReportOverviewData = async () => {
  return await api.get('/app/v1/attendance/weekly-reports/month-stats');
};
