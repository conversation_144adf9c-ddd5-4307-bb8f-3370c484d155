import api from '@/lib/api';

export const postCards = async (data: object) => {
  return api.post('/app/v1/vip/gift-cards/collect', data);
};
export const getCardsInfo = async (data: { cardId: any }) => {
  console.log('data: ', data);
  return api.get(`/app/v1/vip/gift-cards/${data.cardId}`, { params: data });
};
export const getMonitorReportList = async () => {
  return api.get(`/app/v1/monitor-highlights`);
};
export const getMonitorReportInfo = async (summaryId: string) => {
  return api.get(`/app/v1/monitor-highlights/${summaryId}`);
};
export const getFreeBenefits = async () => {
  return api.get(`/app/v1/vip/free-benefits`);

};