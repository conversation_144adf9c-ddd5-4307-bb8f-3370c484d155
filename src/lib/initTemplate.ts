export function initData(data: any) {
  return new Promise((resolve, reject) => {
    let templateData = {};
    try {
      const { type } = data;
      if (type === 'myTeacher') {
        const { name, avatar, intro } = data;
        templateData = {
          attrs: {
            width: 2480,
            height: 3508,
          },
          className: 'Stage',
          children: [
            {
              attrs: {},
              className: 'Layer',
              children: [
                {
                  attrs: {
                    source:
                      'https://edu-media.ancda.com/prod/archives/theme/my_teacher.png',
                  },
                  className: 'Image',
                },
                {
                  attrs: {
                    width: 2090,
                    height: 2628,
                    x: 275,
                    y: 300,
                    type: 'content',
                  },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        y: 200,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: '我的老师',
                        fontSize: 146,
                        fill: '#ed6b6b',
                        editId: '2-1',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar,
                        width: 1460,
                        height: 974,
                        y: 446,
                        x: 245,
                        editId: '2-2',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        fontSize: 110,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: name || '',
                        fill: '#ed6b6b',
                        y: 1535,
                        editId: '2-3',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 1800,
                        height: 1000,
                        fontSize: 64,
                        fill: '#333333',
                        fontFamily: 'Alibaba-PuHuiTi,Apple-Color-Emoji',
                        x: 60,
                        y: 1690,
                        lineHeight: 1.5,
                        text: intro || '',
                        editId: '2-4',
                        isEditable: true,
                        maxLength: 250,
                      },
                      className: 'Text',
                    },
                  ],
                },
                {
                  attrs: {
                    width: 130,
                    height: 130,
                    x: 2308,
                    y: 3338,
                    type: 'pageNum',
                    name: 'pageNum',
                  },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        radius: 65,
                        fill: '#fff',
                      },
                      className: 'Circle',
                    },
                    {
                      attrs: {
                        text: '1',
                        fontSize: 55,
                        width: 130,
                        fill: '#333',
                        align: 'center',
                        x: -65,
                        y: -25,
                      },
                      className: 'Text',
                    },
                  ],
                },
              ],
            },
          ],
        };
      } else if (type === 'myTeacher2') {
        const { name, avatar, intro, name2, avatar2, intro2 } = data;
        templateData = {
          attrs: { width: 2480, height: 3508 },
          className: 'Stage',
          children: [
            {
              attrs: {},
              className: 'Layer',
              children: [
                {
                  attrs: {
                    source:
                      'https://edu-media.ancda.com/prod/archives/theme/my_teacher2.png',
                  },
                  className: 'Image',
                },
                {
                  attrs: { width: 2090, height: 2628, x: 275, y: 300 },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        y: 200,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: '我的老师',
                        fontSize: 146,
                        fill: '#ed6868',
                        editId: '3-1',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar,
                        width: 720,
                        height: 720,
                        ratio: 1,
                        y: 600,
                        x: 175,
                        editId: '3-2',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 800,
                        fontSize: 110,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        x: 1015,
                        text: name,
                        fill: '#ed6868',
                        y: 600,
                        editId: '3-3',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 800,
                        height: 600,
                        fontSize: 64,
                        fill: '#666',
                        x: 1015,
                        y: 780,
                        lineHeight: 1.5,
                        text: intro,
                        editId: '3-4',
                        isEditable: true,
                        maxLength: 90,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 780,
                        fontSize: 110,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        x: 175,
                        text: name2,
                        fill: '#ed6868',
                        y: 1550,
                        editId: '3-5',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 780,
                        height: 600,
                        fontSize: 64,
                        fill: '#666',
                        x: 175,
                        y: 1730,
                        lineHeight: 1.5,
                        text: intro2,
                        editId: '3-6',
                        isEditable: true,
                        maxLength: 90,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar2,
                        width: 720,
                        height: 720,
                        y: 1550,
                        x: 1055,
                        ratio: 1,
                        editId: '3-7',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                  ],
                },
              ],
            },
          ],
        };
      } else if (type === 'myTeacher3') {
        const {
          name,
          avatar,
          intro,
          name2,
          avatar2,
          intro2,
          name3,
          avatar3,
          intro3,
        } = data;
        templateData = {
          attrs: { width: 2480, height: 3508 },
          className: 'Stage',
          children: [
            {
              attrs: {},
              className: 'Layer',
              children: [
                {
                  attrs: {
                    source:
                      'https://edu-media.ancda.com/prod/archives/theme/my_teacher3.png',
                  },
                  className: 'Image',
                },
                {
                  attrs: { width: 2090, height: 2628, x: 275, y: 300 },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        y: 200,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: '我的老师',
                        fontSize: 146,
                        fill: '#ed6868',
                        editId: '4-1',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar,
                        width: 600,
                        height: 600,
                        y: 450,
                        x: 175,
                        ratio: 1,
                        editId: '4-2',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 900,
                        fontSize: 90,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        x: 895,
                        text: name,
                        fill: '#ed6868',
                        y: 450,
                        editId: '4-3',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 900,
                        height: 500,
                        fontSize: 64,
                        fill: '#666',
                        x: 895,
                        y: 600,
                        lineHeight: 1.5,
                        text: intro,
                        editId: '4-4',
                        isEditable: true,
                        maxLength: 50,
                      },
                      className: 'Text',
                    },

                    {
                      attrs: {
                        width: 880,
                        fontSize: 90,
                        x: 175,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: name2,
                        fill: '#ed6868',
                        y: 1150,
                        editId: '4-5',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 900,
                        height: 500,
                        fontSize: 64,
                        fill: '#666',
                        x: 175,
                        y: 1300,
                        lineHeight: 1.5,
                        text: intro2,
                        editId: '4-6',
                        isEditable: true,
                        maxLength: 50,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar2,
                        width: 600,
                        height: 600,
                        y: 1150,
                        x: 1175,
                        ratio: 1,
                        editId: '4-7',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        source: avatar3,
                        width: 600,
                        height: 600,
                        y: 1900,
                        x: 175,
                        ratio: 1,
                        editId: '4-8',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 900,
                        fontSize: 90,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        x: 895,
                        text: name3,
                        fill: '#ed6868',
                        y: 1900,
                        editId: '4-9',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 900,
                        height: 500,
                        fontSize: 64,
                        fill: '#666',
                        x: 895,
                        y: 2050,
                        lineHeight: 1.5,
                        text: intro3,
                        editId: '4-10',
                        isEditable: true,
                        maxLength: 50,
                      },
                      className: 'Text',
                    },
                  ],
                },
              ],
            },
          ],
        };
      } else if (type === 'mySchool') {
        const { title, name, describe, image } = data;
        templateData = {
          attrs: {
            width: 2480,
            height: 3508,
          },
          className: 'Stage',
          children: [
            {
              attrs: {},
              className: 'Layer',
              children: [
                {
                  attrs: {
                    source:
                      'https://edu-media.ancda.com/prod/archives/theme/my_school.png',
                  },
                  className: 'Image',
                },
                {
                  attrs: {
                    width: 2090,
                    height: 2628,
                    x: 275,
                    y: 300,
                    type: 'content',
                  },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        width: 1800,
                        x: 75,
                        align: 'center',
                        y: 200,
                        text: title,
                        fontSize: 146,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        type: 'title',
                        fill: '#ca7740',
                        editId: '1-1',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: image,
                        width: 1460,
                        height: 974,
                        y: 446,
                        x: 245,
                        editId: '1-2',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 1800,
                        align: 'center',
                        x: 75,
                        fontSize: 110,
                        text: name,
                        fill: '#ca7740',
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        y: 1535,
                        editId: '1-3',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 1800,
                        height: 1000,
                        fontSize: 64,
                        fontFamily: 'Alibaba-PuHuiTi',
                        fill: '#333333',
                        x: 60,
                        y: 1690,
                        lineHeight: 1.5,
                        text: describe,
                        editId: '1-4',
                        isEditable: true,
                        maxLength: 250,
                      },
                      className: 'Text',
                    },
                  ],
                },
              ],
            },
          ],
        };
      } else if (type === 'myMaster') {
        // 我的园长
        const { title, name, avatar, describe } = data;
        templateData = {
          attrs: {
            width: 2480,
            height: 3508,
          },
          className: 'Stage',
          children: [
            {
              attrs: {},
              className: 'Layer',
              children: [
                {
                  attrs: {
                    source:
                      'https://edu-media.ancda.com/prod/archives/theme/my_master.png',
                  },
                  className: 'Image',
                },
                {
                  attrs: {
                    width: 2090,
                    height: 2628,
                    x: 275,
                    y: 300,
                    type: 'content',
                  },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        y: 200,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: title,
                        fontSize: 146,
                        fill: '#afcd77',
                        editId: '8-1',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        source: avatar,
                        width: 1460,
                        height: 974,
                        y: 446,
                        x: 245,
                        editId: '8-2',
                        isEditable: true,
                      },
                      className: 'Image',
                    },
                    {
                      attrs: {
                        width: 1460,
                        align: 'center',
                        x: 245,
                        fontSize: 110,
                        fontFamily: 'liZi,Apple-Color-Emoji',
                        text: name || '',
                        fill: '#afcd77',
                        y: 1535,
                        editId: '8-3',
                        isEditable: true,
                        maxLength: 10,
                      },
                      className: 'Text',
                    },
                    {
                      attrs: {
                        width: 1800,
                        height: 1000,
                        fontSize: 64,
                        fill: '#333333',
                        fontFamily: 'Alibaba-PuHuiTi,Apple-Color-Emoji',
                        x: 60,
                        y: 1690,
                        lineHeight: 1.5,
                        text: describe || '',
                        editId: '8-4',
                        isEditable: true,
                        maxLength: 250,
                      },
                      className: 'Text',
                    },
                  ],
                },
                {
                  attrs: {
                    width: 130,
                    height: 130,
                    x: 2308,
                    y: 3338,
                    type: 'pageNum',
                    name: 'pageNum',
                  },
                  className: 'Group',
                  children: [
                    {
                      attrs: {
                        radius: 65,
                        fill: '#fff',
                      },
                      className: 'Circle',
                    },
                    {
                      attrs: {
                        text: '1',
                        fontSize: 55,
                        width: 130,
                        fill: '#333',
                        align: 'center',
                        x: -65,
                        y: -25,
                      },
                      className: 'Text',
                    },
                  ],
                },
              ],
            },
          ],
        };
      }
      resolve(templateData);
    } catch (e: any) {
      reject(e.message);
    }
  });
}
