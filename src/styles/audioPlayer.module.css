.music {
  position: absolute;
  top: 20px;
  left: 20px;
  height: 60px;
  width: 60px;
  background-size: contain;
  cursor: pointer;
  z-index: 100;
}
.music_icon {
  background: url("https://app-h5.ancda.com/images/common/music_icon.png")
    no-repeat;
  background-size: contain;
  display: block;
  width: 100%;
  height: 100%;
}
.play {
  animation: rotate360 2s linear infinite;
}
.audio,
.music.pause:before {
  display: none;
}
@keyframes rotate360 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
