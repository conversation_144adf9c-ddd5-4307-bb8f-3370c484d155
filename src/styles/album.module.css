.page {
  width: 100vw;
  height: 100vh;
  background-image: url("https://edu-media.ancda.com/prod/archives/album/common/album_bg.png");
  background-size: 100vw 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 5vw;
}

.smallScreen {
  width: 100vw;
  height: 100vh;
  background-image: url("https://edu-media.ancda.com/prod/archives/album/common/album_bg.png");
  background-size: 100vw 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10vw;
}

.sliderContainer {
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pageInfo {
  width: 90px;
  color: #666;
  font-size: 22px;
  text-align: right;
  margin-left: 20px;
}

.notExisting {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22px;
}

.book {
  background-repeat: no-repeat;
  position: relative;
  width: 90vw;
  height: calc(90vw * 1.415);
  background-color: transparent;
  outline: none;
}
.smallBook {
  background-repeat: no-repeat;
  position: relative;
  width: 80vw;
  height: calc(80vw * 1.415);
  background-color: transparent;
  outline: none;
}
.pageflipCanvas {
  position: absolute;
  z-index: 100;
  pointer-events: none;
}
.pageContainer {
  background-repeat: no-repeat;
  display: block;
  width: 100%;
  height: calc(90vw * 1.415);
  /* position: absolute;
  left: 0px;
  top: 0px; */
  overflow: hidden;
  will-change: transform;
}
.smallPageContainer {
  background-repeat: no-repeat;
  display: block;
  width: 100%;
  height: calc(80vw * 1.415);
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: hidden;
  will-change: transform;
}
.catalog {
  padding: 180px 20px 110px;
  height: 100vh;
  overflow-y: scroll;
}
.catalogItem {
  display: flex;
  flex-direction: row;
  margin: 24px 0;
  align-items: center;
}
.catalogItemTitle {
  margin-right: 8px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.catalogItemDesc {
  white-space: nowrap;
}
.catalogItemActive {
  color: #17c5a6;
}
