interface KnowledgeComponentProps {
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  reportDetail: any;
}

export default function KnowledgeComponent({
  reportDetail,
}: KnowledgeComponentProps) {
  if (!(reportDetail?.timeManagement || reportDetail?.weatherKnowledge)) {
    return null;
  }

  return (
    <div className="mb-6 rounded-2xl bg-white/50 p-4 shadow-lg">
      <h2 className="mb-6 font-semibold text-gray-800 text-lg">
        知识拓展小课堂
      </h2>

      {/* 主要内容卡片 */}
      {reportDetail?.weatherKnowledge && (
        <div className="mb-4 rounded-2xl bg-white/50 p-4">
          <div className="flex space-x-4">
            {/* 中间内容 */}
            <div className="flex-1">
              <div className="text-gray-700 text-sm leading-relaxed">
                {reportDetail?.weatherKnowledge}
              </div>
            </div>
          </div>
        </div>
      )}

      {reportDetail?.timeManagement && (
        <div className="rounded-2xl bg-white/50 p-4">
          <div className="flex space-x-4">
            {/* 中间内容 */}
            <div className="flex-1">
              <div className="text-gray-700 text-sm leading-relaxed">
                {reportDetail?.timeManagement}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
