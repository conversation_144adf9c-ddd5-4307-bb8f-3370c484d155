interface LoadingComponentProps {
  dots: string;
}

export default function LoadingComponent({ dots }: LoadingComponentProps) {
  return (
    <div className="absolute inset-0 flex flex-col items-center justify-center">
      <style>
        {`
        @keyframes scale-up-down {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.2); }
        }
        .animate-scale-up-down {
          animation: scale-up-down 2s infinite ease-in-out;
        }
      `}
      </style>
      <picture>
        <img
          alt="loading"
          className="h-auto w-auto animate-scale-up-down"
          src="/images/attendance/generating_report.png"
        />
      </picture>
      <p className="absolute top-[calc(50%+180px)] left-[calc(50%-100px)] text-[#101010] text-lg">
        报告生成中{dots}
      </p>
    </div>
  );
}
