/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
import { Dialog } from 'antd-mobile';

interface AnalysisComponentProps {
  reportDetail: any;
}

export default function AnalysisComponent({
  reportDetail,
}: AnalysisComponentProps) {
  return (
    <div className="mb-6 rounded-2xl bg-white/50 p-4 shadow-lg">
      <h2 className="mb-6 font-semibold text-gray-800 text-lg">
        考勤数据分析与建议
      </h2>

      {/* 入园准时率 */}
      <div className="mb-6">
        <h3 className="mb-4 font-medium text-base text-gray-700">入园准时率</h3>

        <div className="mb-3 flex h-5 items-center">
          <span className="w-14 text-gray-600 text-sm">准时率</span>
          <div className="relative mx-2 flex-1">
            <div
              className="-translate-y-1/2 absolute top-1/2 left-0 h-3 rounded-full bg-gradient-to-r from-sky-200 to-sky-400"
              style={{ width: `${reportDetail?.onTimeRate || '0%'}` }}
            />
            <span
              className="-translate-y-1/2 absolute top-1/2 font-medium text-gray-600 text-sm"
              style={{
                left: `${reportDetail?.onTimeRate || '0%'}`,
                fontSize: 11,
                transform: 'translateX(-100%) translateY(-50%)',
              }}
            >
              {reportDetail?.onTimeRate || '0%'}
            </span>
          </div>
        </div>

        <div className="mb-3 flex h-5 items-center">
          <span className="w-14 text-gray-600 text-sm">迟到率</span>
          <div className="relative mx-2 flex-1">
            <div
              className="-translate-y-1/2 absolute top-1/2 left-0 h-3 rounded-full bg-gradient-to-r from-red-200 to-red-400"
              style={{ width: `${reportDetail?.lateRate || '0%'}` }}
            />
            <span
              className="-translate-y-1/2 absolute top-1/2 font-medium text-gray-600 text-sm"
              style={{
                left: `${reportDetail?.lateRate || '0%'}`,
                fontSize: 11,
                transform: 'translateX(-100%) translateY(-50%)',
              }}
            >
              {reportDetail?.lateRate || '0%'}
            </span>
          </div>
        </div>
      </div>

      {/* 放学准时率 */}
      <div className="mb-6">
        <div className="mb-4 flex items-center">
          <h3 className="font-medium text-base text-gray-700">放学准时率</h3>
          <button
            className="ml-2 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-gray-300"
            onClick={() => {
              Dialog.alert({
                content:
                  '准时：宝贝在放学后的10分钟内被家长接走（已考虑天气、路况等影响因素）\n\n 滞留：宝贝在放学后的10分钟内仍未被家长接走',
              });
            }}
            type="button"
          >
            <span className="text-gray-600 text-xs">?</span>
          </button>
        </div>

        <div className="mb-3 flex h-5 items-center">
          <span className="w-14 text-gray-600 text-sm">准时率</span>
          <div className="relative mx-2 flex-1">
            <div
              className="-translate-y-1/2 absolute top-1/2 left-0 h-3 rounded-full bg-gradient-to-r from-green-200 to-green-400"
              style={{ width: `${reportDetail?.rateAfterSchool || '0%'}` }}
            />
            <span
              className="-translate-y-1/2 absolute top-1/2 font-medium text-gray-600 text-sm"
              style={{
                left: `${reportDetail?.rateAfterSchool || '0%'}`,
                fontSize: 11,
                transform: 'translateX(-100%) translateY(-50%)',
              }}
            >
              {reportDetail?.rateAfterSchool || '0%'}
            </span>
          </div>
        </div>

        <div className="mb-3 flex h-5 items-center">
          <span className="w-14 text-gray-600 text-sm">滞留率</span>
          <div className="relative mx-2 flex-1">
            <div
              className="-translate-y-1/2 absolute top-1/2 left-0 h-3 rounded-full bg-gradient-to-r from-orange-200 to-orange-400"
              style={{ width: `${reportDetail?.retentionRate || '0%'}` }}
            />
            <span
              className="-translate-y-1/2 absolute top-1/2 font-medium text-gray-600 text-sm"
              style={{
                left: `${reportDetail?.retentionRate || '0%'}`,
                fontSize: 11,
                transform: 'translateX(-100%) translateY(-50%)',
              }}
            >
              {reportDetail?.retentionRate || '0%'}
            </span>
          </div>
        </div>
      </div>

      {/* 准时率分析总结 */}
      {reportDetail?.rateSummary && (
        <div className="mb-6 rounded-lg bg-white/50 p-2">
          <p className="text-gray-600 text-sm leading-relaxed">
            {reportDetail.rateSummary}
          </p>
        </div>
      )}

      {(reportDetail?.weatherWarning || reportDetail?.suggestion) && (
        <div className="mb-2 rounded-2xl bg-white/50 p-3 shadow-lg">
          {/* 天气预警 */}
          {reportDetail?.weatherWarning && (
            <div className="mb-4 flex items-center">
              <div className="flex h-14 w-14 flex-shrink-0 flex-col items-center justify-center text-center">
                <svg
                  className="styles__StyledSVGIconPathComponent-sc-i3aj97-0 cOgoBy svg-icon-path-icon fill-none stroke-[#3b82f6]"
                  height="36"
                  viewBox="0 0 48 48"
                  width="36"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>天气预警</title>
                  <g>
                    <path
                      d="M9.45455 30.9942C6.14242 28.461 4 24.4278 4 19.8851C4 12.2166 10.1052 6 17.6364 6C23.9334 6 29.2336 10.3462 30.8015 16.2533C32.0353 15.6159 33.431 15.2567 34.9091 15.2567C39.9299 15.2567 44 19.4011 44 24.5135C44 28.3094 41.7562 31.5716 38.5455 33"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M16 23V27"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M24 27V31"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M32 23V27"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M16 34V38"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M24 38V42"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                    <path
                      d="M32 34V38"
                      stroke="#3b82f6"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="4"
                    />
                  </g>
                </svg>
                <p className="mt-1 w-16 truncate font-medium text-gray-600 text-xs">
                  天气预警
                </p>
              </div>
              <div className="min-w-0 flex-1 rounded-lg bg-white/50 p-2">
                <p className="break-words text-blue-800 text-sm">
                  {reportDetail.weatherWarning}
                </p>
              </div>
            </div>
          )}

          {/* 考勤优化 */}
          {reportDetail?.suggestion && (
            <div className="flex items-center">
              <div className="flex h-14 w-14 flex-shrink-0 flex-col items-center justify-center text-center">
                <svg
                  className="styles__StyledSVGIconPathComponent-sc-i3aj97-0 fEOUvq svg-icon-path-icon fill-none stroke-[#ef4444]"
                  height="32"
                  viewBox="0 0 48 48"
                  width="32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>考勤优化</title>
                  <g>
                    <path
                      d="M7 22H41V10C41 9.44772 40.5523 9 40 9H8C7.44772 9 7 9.44772 7 10V40C7 40.5523 7.44771 41 8 41H26"
                      stroke="#ef4444"
                      strokeWidth="4"
                    />
                    <path d="M34 5V13" stroke="#ef4444" strokeWidth="4" />
                    <path d="M14 5V13" stroke="#ef4444" strokeWidth="4" />
                    <path
                      clipRule="evenodd"
                      d="M36 44C40.9706 44 45 39.9706 45 35C45 30.0294 40.9706 26 36 26C31.0294 26 27 30.0294 27 35C27 39.9706 31.0294 44 36 44ZM37.4997 34.2499V28.9999H34.4997V37.2499H41.9997V34.2499H37.4997Z"
                      fill="#ef4444"
                      fillRule="evenodd"
                      stroke="#ef4444"
                    />
                  </g>
                </svg>
                <p className="mt-1 w-16 truncate font-medium text-gray-600 text-xs">
                  考勤优化
                </p>
              </div>
              <div className="min-w-0 flex-1 rounded-lg bg-white/50 p-2">
                <p className="break-words text-blue-800 text-sm">
                  {reportDetail.suggestion}
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
