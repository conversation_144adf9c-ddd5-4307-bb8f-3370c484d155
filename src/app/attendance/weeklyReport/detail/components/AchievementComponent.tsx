/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
interface AchievementComponentProps {
  reportDetail: any;
}

export default function AchievementComponent({
  reportDetail,
}: AchievementComponentProps) {
  if (!reportDetail?.achievements?.length) {
    return null;
  }

  return (
    <div className="rounded-2xl bg-white/50 p-4 shadow-lg backdrop-blur-sm">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="font-semibold text-gray-800 text-lg">我的成就</h2>
      </div>

      {/* 累计获得统计 */}
      <div className="mb-6 flex items-center">
        <span className="mr-1 font-medium text-gray-700 text-sm">累计获得</span>
        <span className="mx font-bold text-blue-500 text-xl">
          {
            reportDetail?.achievements.filter(
              (achievement: any) =>
                achievement.count === achievement.sucessCount
            ).length
          }
        </span>
        <span className="ml-1 font-medium text-gray-700 text-sm">枚勋章</span>
      </div>

      {/* 成就网格布局 */}
      <div className="grid grid-cols-3 gap-x-2 gap-y-4">
        {reportDetail?.achievements?.map((achievement: any, _index: number) => {
          const islocked = achievement.count === achievement.sucessCount;
          const progress = Math.min(
            (achievement.count / achievement.sucessCount) * 100,
            100
          );

          return (
            <div className="relative rounded-2xl p-2" key={achievement.name}>
              {/* 解锁状态标签 */}
              {islocked && (
                <div className="-top-2 -right-2 absolute z-10 rounded-full bg-red-500 px-2 py-1 font-medium text-white text-xs shadow-md">
                  本周获得
                </div>
              )}

              {!islocked && (
                <div className="-top-2 -right-2 absolute z-10 rounded-full bg-gray-500 px-2 py-1 font-medium text-white text-xs">
                  未解锁
                </div>
              )}

              {/* 成就图标 */}
              <div className="mb-2 flex justify-center">
                <div className="flex h-30 w-30 items-center justify-center rounded-full">
                  <picture>
                    <img
                      alt={achievement.name}
                      className="h-30 w-30 object-contain"
                      src={`/images/attendance/achievement_${achievement.index}_${islocked ? 'on' : 'off'}.png`}
                    />
                  </picture>
                </div>
              </div>

              {/* 成就名称 */}
              <div className="mb-2 text-center">
                <h3
                  className={`font-semibold text-sm ${islocked ? 'text-gray-600' : 'text-gray-500'}`}
                >
                  {achievement.name}
                </h3>
              </div>

              {/* 成就描述 */}
              <div className="mb-2 text-center">
                <p
                  className="px-1 font-medium text-xs text-yellow-600 leading-tight"
                  style={{
                    color: 'rgba(241,186,78,1)',
                    fontSize: '10px',
                    lineHeight: '12px',
                  }}
                >
                  {islocked ? achievement.sucessDesc : achievement.description}
                </p>
              </div>

              {/* 进度条（仅未解锁时显示） */}
              {!islocked && (
                <div className="mt-2">
                  <div className="mb-1 h-2 w-full rounded-full bg-gray-300">
                    <div
                      className="h-2 rounded-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <div
                    className="flex items-center justify-center text-gray-500 text-xs"
                    style={{ fontSize: '10px', lineHeight: '12px' }}
                  >
                    <span>
                      距解锁勋章{achievement.sucessCount - achievement.count}
                      颗⭐
                    </span>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
