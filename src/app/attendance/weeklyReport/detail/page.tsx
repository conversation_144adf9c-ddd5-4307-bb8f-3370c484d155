/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getAttendanceReportDetail } from '@/api/attendance';
import { hinaTrack } from '@/utils';
import AchievementComponent from './components/AchievementComponent';
import AnalysisComponent from './components/AnalysisComponent';
import AttendanceDataComponent from './components/AttendanceDataComponent';
import ErrorComponent from './components/ErrorComponent';
import HeaderComponent from './components/HeaderComponent';
import KnowledgeComponent from './components/KnowledgeComponent';
// 导入组件
import LoadingComponent from './components/LoadingComponent';
import TitleCardComponent from './components/TitleCardComponent';

export default function ReportDetailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const [reportDetail, setReportDetail] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFailed, setIsFailed] = useState(false);
  const [dots, setDots] = useState('');

  const goback = () => {
    router.back();
  };

  // 获取当前周的日期范围或显示文本
  const getCurrentWeekDisplay = (startDate: string, endDate: string) => {
    const now = new Date();
    const currentDay = now.getDay();
    const currentMonday = new Date(now);
    currentMonday.setDate(now.getDate() - currentDay + 1);
    const currentSunday = new Date(currentMonday);
    currentSunday.setDate(currentMonday.getDate() + 6);

    const lastMonday = new Date(currentMonday);
    lastMonday.setDate(currentMonday.getDate() - 7);
    const lastSunday = new Date(lastMonday);
    lastSunday.setDate(lastMonday.getDate() + 6);

    const formatDate = (date: Date) => {
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    };

    const start = new Date(startDate);
    const end = new Date(endDate);

    const formattedStart = formatDate(start);
    const formattedEnd = formatDate(end);

    if (
      formattedStart === formatDate(currentMonday) &&
      formattedEnd === formatDate(currentSunday)
    ) {
      return '本周';
    }
    if (
      formattedStart === formatDate(lastMonday) &&
      formattedEnd === formatDate(lastSunday)
    ) {
      return '上周';
    }
    return `${formattedStart} - ${formattedEnd}`;
  };

  // 切换到上一周
  const handlePreviousWeek = () => {
    // TODO: 实现上一周逻辑
  };

  // 切换到下一周
  const handleNextWeek = () => {
    // TODO: 实现下一周逻辑
  };

  // 背景图片的函数
  const getCardBackground = (keyWord: string) => {
    if (keyWord.includes('守时小标兵')) {
      return '/images/attendance/bg_card_1.png';
    }
    if (keyWord.includes('天气探险家')) {
      return '/images/attendance/bg_card_2.png';
    }
    if (keyWord.includes('时间小尾巴')) {
      return '/images/attendance/bg_card_3.png';
    }
    if (keyWord.includes('全勤小超人预备员')) {
      return '/images/attendance/bg_card_4.png';
    }
    if (keyWord.includes('晨光收藏家')) {
      return '/images/attendance/bg_card_5.png';
    }
    if (keyWord.includes('守时家庭')) {
      return '/images/attendance/bg_card_6.png';
    }
    if (keyWord.includes('快乐早退星人')) {
      return '/images/attendance/bg_card_7.png';
    }
    return '/images/attendance/bg_card_1.png';
  };

  // 在 useEffect 或适当位置处理数据
  const processAttendanceData = () => {
    if (!(reportDetail?.formattedAttendanceData && reportDetail.dayStatData)) {
      return [];
    }

    const data = reportDetail.formattedAttendanceData;
    const weatherData = reportDetail.weatherData;
    const allDates: {
      weekday: string;
      arrivalTime: string;
      leaveTime: string;
      isLeave: number;
      isAbsence: boolean;
      isRestDay: boolean;
      isMissingArrival: boolean;
      isMissingLeave: boolean;
      weather: string;
      isLate: boolean;
      isEarlyLeave: boolean;
    }[] = [];

    // 添加转换函数
    const getWeekdayChinese = (day: number) => {
      const days = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      return days[day] || '未知';
    };

    // 添加天气到 emoji 的映射函数
    const getWeatherEmoji = (description: string) => {
      const weatherEmojiMap: Record<string, string> = {
        晴: '☀️',
        少云: '🌤️',
        多云: '🌤️',
        阴: '☁️',
        阵雨: '🌦️',
        小雨: '🌧️',
        中雨: '🌧️',
        大雨: '🌧️',
        雷阵雨: '⛈️',
        雷雨: '⛈️',
        雨: '🌧️',
        小雪: '🌨️',
        中雪: '🌨️',
        大雪: '🌨️',
        暴雪: '🌨️',
        雪: '🌨️',
        雾: '☁️',
        霾: '☁️',
        风: '💨',
        大风: '💨',
        闪电: '⚡',
        雷电: '🌩️',
        龙卷风: '🌪️',
        冰雹: '🧊',
      };

      // 将 "转"、"至"、"~" 等分隔符统一替换为 "-"，并处理 "转" 后的天气描述
      const normalized = description
        .replace(/[转至~、]/g, '-')
        .replace(/转([^-]+)/g, '-$1')
        .replace(/([^-])转/g, '$1-');

      return normalized
        .split('-')
        .map((word) => weatherEmojiMap[word.trim()] || word.trim())
        .join('');
    };

    reportDetail.dayStatData.forEach((item: any, index: number) => {
      const dateStr = item.attendanceDate;
      const weatherStr = weatherData[dateStr] || '';
      const weatherEmoji = getWeatherEmoji(weatherStr);
      const records: [any] = data[dateStr] || [];
      const weekday = getWeekdayChinese(records[0]?.weekday || index + 1);
      let arrivalTime = '';
      let leaveTime = '';
      let isMissingArrival = true;
      let isMissingLeave = true;
      let isLate = false;
      let isEarlyLeave = false;
      if (records.length >= 2) {
        arrivalTime = (records[0].signTime || records[0].workTime || '').slice(
          0,
          5
        );
        const lastRecord: any = records.at(-1);
        leaveTime = (lastRecord.signTime || lastRecord.workTime || '').slice(
          0,
          5
        );
        isMissingArrival = records[0].status === 4;
        isMissingLeave = lastRecord.status === 4;
        isLate = records[0].status === 2;
        isEarlyLeave = lastRecord.status === 3;
      }

      allDates.push({
        weekday,
        arrivalTime,
        leaveTime,
        isLeave: item.isLeave,
        isAbsence: item.isAbsence === 1,
        isRestDay: item.isWork === 0,
        isMissingArrival,
        isMissingLeave,
        weather: weatherEmoji || weatherStr,
        isLate,
        isEarlyLeave,
      });
    });

    return allDates;
  };

  // 在组件中使用
  const processedData = processAttendanceData();

  useEffect(() => {
    hinaTrack('attendreport_details_pageview');
    getAttendanceReportDetail(id || '')
      .then((res) => {
        setReportDetail(res);
        setIsLoading(false);
        setIsFailed(false);
      })
      .catch(() => {
        setIsLoading(false);
        setIsFailed(true);
      });
  }, []);

  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setDots((prev) => (prev.length >= 6 ? '' : `${prev}.`));
      }, 300);
      return () => {
        clearInterval(interval);
      };
    }
    return () => {
      setDots('');
    };
  }, [isLoading]);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-b from-blue-100 to-blue-50">
      {/* 背景图片 */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: "url('/images/attendance/bg_report.png')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />

      {/* 时钟背景装饰 */}
      {!(isLoading || isFailed) && (
        <div
          className="absolute top-20 right-4 h-90 w-60"
          style={{
            opacity: 0.3,
            transform: 'rotate(300deg)',
            marginRight: -80,
          }}
        >
          <picture>
            <img
              alt="clock"
              className="h-full w-full"
              src="/images/attendance/clock.png"
            />
          </picture>
        </div>
      )}

      {/* 头部导航 */}
      <HeaderComponent
        getCurrentWeekDisplay={getCurrentWeekDisplay}
        handleNextWeek={handleNextWeek}
        handlePreviousWeek={handlePreviousWeek}
        isFailed={isFailed}
        isLoading={isLoading}
        onGoBack={goback}
        reportDetail={reportDetail}
      />

      {(() => {
        if (isLoading) {
          return <LoadingComponent dots={dots} />;
        }
        if (isFailed) {
          return <ErrorComponent />;
        }
        return (
          <div className="relative z-10 rounded-t-2xl px-4 pt-36 pb-8">
            {/* 标题区域 */}
            <TitleCardComponent
              getCardBackground={getCardBackground}
              reportDetail={reportDetail}
            />

            {/* 宝贝本周考勤数据 */}
            <AttendanceDataComponent
              processedData={processedData}
              reportDetail={reportDetail}
            />

            {/* 考勤数据分析与建议 */}
            <AnalysisComponent reportDetail={reportDetail} />

            {/* 知识拓展小课堂 */}
            <KnowledgeComponent reportDetail={reportDetail} />

            {/* 我的成就 */}
            <AchievementComponent reportDetail={reportDetail} />

            <div className="text-center text-gray-700 text-sm">
              本内容为AI生成，仅供参考不代表平台立场
            </div>
          </div>
        );
      })()}
    </div>
  );
}
