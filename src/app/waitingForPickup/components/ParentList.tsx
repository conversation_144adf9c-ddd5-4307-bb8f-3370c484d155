'use client';

import { DotLoading, List, PullToRefresh, Tabs } from 'antd-mobile';
import { format } from 'date-fns';
import { CheckCircle, Clock, Phone, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useState } from 'react';

import { confirmPickups, pickupsListTeacher } from '@/api/waitingForPickup';

const ParentList: React.FC = () => {
  if (typeof window !== 'undefined') {
    document.title = '代接送';
  }

  const [showModal, setShowModal] = useState(false);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [selectedPickup, setSelectedPickup] = useState<string | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('pending');
  const [pickups, setPickups] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageSize] = useState(100);
  // Mock data

  const fetchData = async (pageNum: number, refresh = false) => {
    setLoading(true);
    setError(null);
    try {
      const res = await pickupsListTeacher({
        page: pageNum,
        pageSize,
        isConfirmed: activeTab === 'confirmed' ? 1 : 0,
      });
      if (refresh) {
        setPickups(res.items);
      } else {
        setPickups((prev) => [...prev, ...res.items]);
      }
      setHasMore(res.items.length === pageSize);
    } catch (err) {
      if (refresh) {
        setPickups([]);
      }
      setError('数据加载失败，请稍后重试');
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    setPage(1);
    fetchData(1, true);
  }, [activeTab]);
  useEffect(() => {
    fetchData(1, true);
  }, []);

  const handleConfirmClick = (id: string) => {
    setSelectedPickup(id);
    setShowModal(true);
  };

  const handleConfirm = () => {
    if (selectedPickup) {
      confirmPickups({ proxyId: selectedPickup }).then((res) => {
        setPickups((prev) =>
          prev.filter((pickup) => pickup.id !== selectedPickup)
        );
        setShowModal(false);
        setSelectedPickup(null);
      });
      // Handle confirmation logic
    }
  };

  const handleImageClick = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setShowImagePreview(true);
  };
  const handleCallTeacher = (phoneNumber: string) => {
    // 调起系统拨打电话界面
    window.location.href = `tel:${phoneNumber}`;
  };
  const onRefresh = async () => {
    setPage(1);
    await fetchData(1, true);
  };

  const loadMore = async () => {
    if (loading || !hasMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
    await fetchData(nextPage);
  };
  const handelConfirmPickups = () => {};
  return (
    <div className="flex h-full flex-col">
      <div className="bg-white shadow-sm">
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key)}
          style={{ '--title-font-size': '16px' }}
        >
          <Tabs.Tab
            key="pending"
            title={
              <div className="flex items-center gap-2">
                <Clock size={18} />
                <span>待确认</span>
              </div>
            }
          />
          <Tabs.Tab
            key="confirmed"
            title={
              <div className="flex items-center gap-2">
                <CheckCircle size={18} />
                <span>已确认</span>
              </div>
            }
          />
        </Tabs>
      </div>
      <PullToRefresh onRefresh={onRefresh}>
        <List
          className="min-h-screen"
          mode="card"
          style={{
            '--border-inner': 'none',
            backgroundColor: '#fff',
          }}
        >
          {pickups.map((pickup) => (
            <List.Item
              className="mb-2 border-[#f2f2f2] border-b py-3"
              key={pickup.id}
            >
              <div className="overflow-hidden rounded-lg bg-white ">
                <div className="flex flex-row items-center">
                  {pickup.pictureUrl ? (
                    <div
                      className="relative size-20 shrink-0 cursor-pointer"
                      onClick={() => handleImageClick(pickup.pictureUrl)}
                    >
                      <img
                        alt="代接送人照片"
                        className="size-full object-cover"
                        src={pickup.pictureUrl}
                      />
                    </div>
                  ) : (
                    <div className="flex size-20 flex-col items-center justify-center rounded-lg bg-gray-100 py-12">
                      <span className="text-gray-500 text-sm">未传图片</span>
                    </div>
                  )}
                  <div className="ml-4 flex-1 ">
                    <div className="mb-2 flex items-center">
                      <h3 className="font-medium text-gray-900 text-sm">
                        {pickup.name}来接{pickup.studentName}
                      </h3>
                      <span className="text-gray-900 text-sm">
                        ({pickup.phone})
                      </span>
                    </div>
                    {pickup.msg && (
                      <p className="mt-1 text-gray-600 text-sm">{pickup.msg}</p>
                    )}
                    <p className="mt-1 flex flex-row justify-end text-gray-600 text-sm">
                      {pickup.isConfirmed === 1 && (
                        <span className="flex-1 text-green-600">已确认</span>
                      )}
                      {format(new Date(pickup.createTime * 1000), 'yyyy-MM-dd')}
                    </p>
                  </div>
                </div>
                {pickup.isConfirmed === 0 && (
                  <div className="mt-4 flex justify-end gap-3 ">
                    <button
                      className="flex items-center justify-center gap-2 rounded-lg border border-[var(--adm-color-primary)] bg-white p-2 text-[var(--adm-color-primary)] transition-colors"
                      onClick={() => handleCallTeacher(pickup.phone)}
                      type="button"
                    >
                      <Phone size={18} />
                      <span className="text-sm">联系家长</span>
                    </button>
                    <button
                      className="flex items-center justify-center gap-2 rounded-lg bg-[var(--adm-color-primary)] p-2 text-white transition-colors "
                      onClick={() => handleConfirmClick(pickup.id)}
                      type="button"
                    >
                      <CheckCircle size={18} />
                      <span className="text-sm">确认接送</span>
                    </button>
                  </div>
                )}
              </div>
            </List.Item>
          ))}
          {error && (
            <div className="py-4 text-center">
              <span className="text-red-500 text-sm">{error}</span>
            </div>
          )}
          {loading && (
            <div className="py-4 text-center">
              <span className="flex items-center justify-center text-gray-500 text-sm">
                加载中 <DotLoading />
              </span>
            </div>
          )}
          {!hasMore && (
            <div className="py-4 text-center">
              <span className="text-gray-500 text-sm">没有更多数据了</span>
            </div>
          )}
        </List>
        {hasMore && (
          <div className="py-4 text-center">
            <button
              className="text-blue-600 text-sm"
              disabled={loading}
              onClick={loadMore}
              type="button"
            >
              {loading ? '加载中...' : '点击加载更多'}
            </button>
          </div>
        )}
      </PullToRefresh>
      {/* Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-sm rounded-lg bg-white p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="font-semibold text-gray-900 text-lg">确认接送</h3>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setShowModal(false)}
                type="button"
              >
                <X size={20} />
              </button>
            </div>
            <p className="mb-6 text-gray-600">
              确认由照片中人代接送
              {pickups.find((p) => p.id === selectedPickup)?.studentName}宝贝？
            </p>
            <div className="grid grid-cols-2 gap-3">
              <button
                className="rounded-lg border border-gray-300 px-4 py-2.5 text-gray-700 transition-colors hover:bg-gray-50"
                onClick={() => setShowModal(false)}
                type="button"
              >
                取消
              </button>
              <button
                className="rounded-lg bg-[var(--adm-color-primary)] px-4 py-2.5 text-white transition-colors "
                onClick={handleConfirm}
                type="button"
              >
                确认
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {showImagePreview && previewImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
          onClick={() => setShowImagePreview(false)}
        >
          <button
            className="absolute top-4 right-4 text-white"
            onClick={() => setShowImagePreview(false)}
          >
            <X size={24} />
          </button>
          <img
            alt="预览图片"
            className="max-h-full max-w-full object-contain p-4"
            onClick={(e) => e.stopPropagation()}
            src={previewImage}
          />
        </div>
      )}
    </div>
  );
};

export default ParentList;
