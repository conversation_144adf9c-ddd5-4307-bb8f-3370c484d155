'use client';

import {
  DotLoading,
  ImageViewer,
  List,
  PullToRefresh,
  Tabs,
} from 'antd-mobile';
import { format } from 'date-fns';
import { CheckCircle, Clock } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { useEffect, useState } from 'react';

import { pickupsListParent } from '@/api/waitingForPickup';

const TeacherList: React.FC = () => {
  if (typeof window !== 'undefined') {
    document.title = '代接送';
  }
  const searchParams = useSearchParams();

  const tabIndex = Number(searchParams.get('tabIndex') || 0); // 1
  const [activeTab, setActiveTab] = useState(
    tabIndex === 1 ? 'confirmed' : 'pending'
  );
  const [pickups, setPickups] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(100);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [currentImage, setCurrentImage] = useState('');
  const fetchData = async (pageNum: number, refresh = false) => {
    setLoading(true);
    setError(null);
    try {
      const res = await pickupsListParent({
        page: pageNum,
        pageSize,
        isConfirmed: activeTab === 'confirmed' ? 1 : 0,
      });
      console.log('res.items', res.items);
      if (refresh) {
        setPickups(res.items);
      } else {
        setPickups((prev) => [...prev, ...res.items]);
      }
      setHasMore(res.items.length === pageSize);
    } catch (err) {
      if (refresh) {
        setPickups([]);
      }
      setError('数据加载失败，请稍后重试');
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setPage(1);
    fetchData(1, true);
  }, [activeTab]);

  useEffect(() => {
    fetchData(1, true);
  }, []);

  const onRefresh = async () => {
    setPage(1);
    await fetchData(1, true);
  };

  const loadMore = async () => {
    if (loading || !hasMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
    await fetchData(nextPage);
  };

  return (
    <div className="flex h-full flex-col">
      <div className="bg-white shadow-sm">
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key)}
          style={{ '--title-font-size': '16px' }}
        >
          <Tabs.Tab
            key="pending"
            title={
              <div className="flex items-center gap-2">
                <Clock size={18} />
                <span>待确认</span>
              </div>
            }
          />
          <Tabs.Tab
            key="confirmed"
            title={
              <div className="flex items-center gap-2">
                <CheckCircle size={18} />
                <span>已确认</span>
              </div>
            }
          />
        </Tabs>
      </div>

      <PullToRefresh onRefresh={onRefresh}>
        <List
          className="min-h-screen"
          mode="card"
          style={{
            '--border-inner': 'none',
            backgroundColor: '#fff',
          }}
        >
          {pickups.length === 0 && !loading && !error ? (
            <div className="flex flex-col items-center justify-center py-12">
              <span className="text-gray-500 text-sm">暂无数据</span>
            </div>
          ) : (
            <>
              {pickups.map((pickup) => (
                <List.Item
                  className="mb-2 border-[#f2f2f2] border-b py-1"
                  key={pickup.id}
                >
                  <div className="flex">
                    {pickup.pictureUrl ? (
                      <div className="mr-4 size-20 shrink-0">
                        <img
                          alt="接送人照片"
                          className="size-full rounded-lg object-cover"
                          onClick={() => {
                            setCurrentImage(pickup.pictureUrl);
                            setShowImageViewer(true);
                          }}
                          src={pickup.pictureUrl}
                        />
                      </div>
                    ) : (
                      <div className="mr-4 flex size-20 shrink-0 items-center justify-center rounded-lg border-2 border-gray-300 border-dashed">
                        <span className="text-gray-600 text-sm">未传图片</span>
                      </div>
                    )}
                    <div className="flex flex-1 flex-col justify-center">
                      <div className="mb-2 flex items-center justify-between">
                        <span className="text-gray-900 text-sm">
                          {pickup.name}去接{pickup.studentName}({pickup.phone})
                        </span>
                      </div>
                      {pickup.msg && (
                        <p className="mt-1 text-gray-600 text-sm">
                          {pickup.msg}
                        </p>
                      )}
                      <p className="mt-1 flex flex-row justify-between text-green-600 text-sm">
                        {pickup.isConfirmed === 1 && (
                          <span className="text-green-600">老师已确认</span>
                        )}
                        {pickup.isConfirmed === 0 && (
                          <span className="text-red-500">待老师确认</span>
                        )}
                        <span className="text-gray-500 text-sm">
                          {format(
                            new Date(pickup.createTime * 1000),
                            'yyyy-MM-dd'
                          )}
                        </span>
                      </p>
                    </div>
                  </div>
                </List.Item>
              ))}
            </>
          )}
          {error && (
            <div className="py-4 text-center">
              <span className="text-red-500 text-sm">{error}</span>
            </div>
          )}
          {loading && (
            <div className="py-4 text-center">
              <span className="flex items-center justify-center text-gray-500 text-sm">
                加载中 <DotLoading />
              </span>
            </div>
          )}
          {!hasMore && pickups.length > 0 && (
            <div className="py-4 text-center">
              <span className="text-gray-500 text-sm">没有更多数据了</span>
            </div>
          )}
        </List>
        {hasMore && pickups.length > 0 && (
          <div className="py-4 text-center">
            <button
              className="text-blue-600 text-sm"
              disabled={loading}
              onClick={loadMore}
              type="button"
            >
              {loading ? '加载中...' : '点击加载更多'}
            </button>
          </div>
        )}
      </PullToRefresh>
      <ImageViewer
        image={currentImage}
        onClose={() => {
          setShowImageViewer(false);
        }}
        visible={showImageViewer}
      />
    </div>
  );
};

export default TeacherList;
