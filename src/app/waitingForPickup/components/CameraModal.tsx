'use client';

import { format } from 'date-fns';
import { Camera, ImageUp, SwitchCamera, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import { generateString, getEnv, uploadObs } from '@/utils/obs';

interface CameraModalProps {
  onCapture: (imageData: string) => void;
  onClose: () => void;
}

const CameraModal: React.FC<CameraModalProps> = ({ onCapture, onClose }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [cameras, setCameras] = useState<MediaDeviceInfo[]>([]);
  const [currentCameraIndex, setCurrentCameraIndex] = useState(0);
  const [facingMode, setFacingMode] = useState<'environment' | 'user'>(
    'environment'
  );
  useEffect(() => {
    startCamera();
    return () => {
      stopCamera();
    };
  }, [facingMode]);
  useEffect(() => {
    getCameraDevices();
  }, []);
  // 获取可用的摄像头列表
  const getCameraDevices = async () => {
    try {
      // 请求权限
      await navigator.mediaDevices.getUserMedia({ video: true });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(
        (device) => device.kind === 'videoinput'
      );
      setCameras(videoDevices);

      // 尝试找到后置摄像头
      const backCamera = videoDevices.find(
        (device) =>
          device.label.toLowerCase().includes('back') ||
          device.label.toLowerCase().includes('后置') ||
          device.label.toLowerCase().includes('环境')
      );

      if (backCamera) {
        const backCameraIndex = videoDevices.indexOf(backCamera);
        setCurrentCameraIndex(backCameraIndex);
      }
    } catch (err) {
      console.error('获取摄像头列表失败:', err);
      alert('获取摄像头列表失败:');
    }
  };
  const startCamera = async () => {
    try {
      const constraints: MediaTrackConstraints = {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
      };

      // 如果有可用的摄像头设备
      if (cameras.length > 0) {
        constraints.deviceId = { exact: cameras[currentCameraIndex].deviceId };
      } else {
        // 降级使用 facingMode
        constraints.facingMode = facingMode;
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: constraints,
        audio: false,
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play().catch((e) => {
            console.error('视频播放失败:', e);
            handleGalleryClick();
          });
        };
        streamRef.current = stream;
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      alert('无法访问相机，请确保已授予相机权限。');
      // onClose();
    }
  };
  const switchCamera = () => {
    stopCamera();
    if (cameras.length > 1) {
      // 如果有多个摄像头，循环切换
      setCurrentCameraIndex((prevIndex) => (prevIndex + 1) % cameras.length);
    } else {
      // 降级使用 facingMode 切换
      setFacingMode(facingMode === 'environment' ? 'user' : 'environment');
    }
  };
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
  };

  const capturePhoto = () => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        ctx.drawImage(videoRef.current, 0, 0);
        const imageData = canvas.toDataURL('image/jpeg');
        const env = getEnv();
        const date = format(new Date(), 'yyyy-MM-dd');
        const key = `${env}/waitingForPickup/${date}/${generateString(8)}.png`;
        uploadObs(imageData, key, true)
          .then((url) => {
            onCapture(url);
            stopCamera();
          })
          .catch((err) => {
            console.log('upload error', err);
          })
          .finally(() => {});
      }
    }
  };
  const handleGalleryClick = () => {
    // 触发隐藏的文件输入框点击
    document.getElementById('gallery-picker')?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const env = getEnv();
      const date = format(new Date(), 'yyyy-MM-dd');
      const key = `${env}/waitingForPickup/${date}/${generateString(8)}.png`;
      uploadObs(file, key, false)
        .then((url) => {
          onCapture(url);
          stopCamera();
        })
        .catch((err) => {
          console.log('upload error', err);
        })
        .finally(() => {});
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white">
      <video
        autoPlay
        className="size-full object-cover"
        onLoadedMetadata={() => {
          if (videoRef.current) {
            videoRef.current.play();
          }
        }}
        playsInline
        ref={videoRef}
      />
      <div className="absolute bottom-8 flex w-full justify-center space-x-6">
        <button
          className="rounded-full bg-white p-4 text-red-600 shadow-lg"
          onClick={onClose}
        >
          <X size={32} />
        </button>
        <button
          className="rounded-full bg-white p-4 shadow-lg"
          onClick={capturePhoto}
        >
          <Camera className="text-blue-600" size={32} />
        </button>
        <button
          className="rounded-full bg-white p-4 shadow-lg"
          onClick={switchCamera}
        >
          <SwitchCamera className="text-gray-600" size={32} />
        </button>
        <input
          accept="image/*"
          className="hidden"
          id="gallery-picker"
          onChange={handleFileSelect}
          type="file"
        />
        <button
          className="rounded-full bg-white p-4 shadow-lg"
          onClick={handleGalleryClick}
        >
          <ImageUp className="text-gray-600" size={32} />
        </button>
      </div>
    </div>
  );
};

export default CameraModal;
