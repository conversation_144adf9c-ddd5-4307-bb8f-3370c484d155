import Cookies from 'js-cookie';

import { navigationToNativePage } from '@/utils/index';

const parentId = Cookies.get('parentId');
const studentId = Cookies.get('studentId');
export const faqContentList = {
  account: [
    {
      question: '1. 登录时忘记密码怎么办？',
      answer: `<div>
          <h5>①重置密码并使用新密码登录</h5>
          <p>APP登录界面→点击"忘记密码"→获取验证码→输入验证码→设置新的密码→确认</p>
          <h5>②使用微信授权登录</h5>
          <p>APP登录界面→点击"微信登录"→输入手机号码→获取验证码→输入验证码→确认</p>
        </div>`,
    },
    {
      question: '2. 家长首次登录APP时的默认初始密码是多少？',
      answer: `<div>
          <h5>①请优先在短信中搜索"掌心"查找邀请短信，短信内容中会附上登录账号和对应的默认初始密码</h5>
          <h5>②其次，您可以尝试使用手机号的后6位数字作为默认初始密码登录（例如，手机号-137**283154，则默认初始密码为283154）</h5>
          <h5>③若以上方式都无效，有可能是您所在的学校在后台设置过园所特定的默认初始密码，请联系老师获取默认初始密码</h5>
        </div>`,
    },
    {
      question: '3. 登录时突然提示"账号不存在，请先联系幼儿园添加！"怎么办？',
      answer: `<div>
          <h5>出现这种情况有两种可能：</h5>
          <h5>①若该学生的第一联系人可以正常登录APP，则是因为第一联系人将您从亲友团中移出</h5>
          <p>请您联系该学生的第一联系人将您重新添加进入亲友团即可</p>
          <h5>若该学生的第一联系人也不能登录，则是因为园所将该学生从系统中删除</h5>
          <p>请您联系该学生所在学校的老师/负责人将该学生重新添加进入系统中，并让第一联系人将您再次邀请进入亲友团中，即可继续正常登录APP</p>
        </div>`,
    },
    {
      question: '4. 登录后突然看不了班级圈怎么回事？',
      answer: `<div>
          <h5>出现可以正常登录，其他功能可以使用，但是班级圈显示"网络连接错误，请稍候重试"的情况，往往是园所在系统中误操作将学生离园导致。请您联系学校的老师/负责人将您的孩子重新入园即可。</h5>
        </div>`,
    },
    {
      question: '5. 如何换绑手机号？',
      answer: () => {
        return (
          <div>
            <h5>您好，您可按照以下步骤换绑手机号：</h5>
            <p>
              【登录家长端APP→点击底部导航栏"我的"→点击"设置"→点击"账号与安全"→更换手机号】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/my/changePhone')
                }
              >
                我要换绑手机号
              </span>
            </p>
          </div>
        );
      },
    },
    {
      question: '6.如何修改登录密码？',
      answer: () => {
        return (
          <div>
            <h5>①若您已经登录账号，请按照以下步骤操作修改登录密码</h5>
            <p>
              【点击底部导航栏"我的"→点击"设置"→点击"账号与安全"→修改登录密码】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/login/ForgotPassword')
                }
              >
                我要修改登录密码
              </span>
            </p>
            <p>②若您尚未登录账号，请按照以下步骤操作修改登录密码</p>
            <p>
              【APP登录界面→点击"忘记密码"→获取验证码→输入验证码→设置新的密码→确认】
            </p>
          </div>
        );
      },
    },
    {
      question: '7. 如何绑定或解绑第三方账号（微信）？',
      answer: () => {
        return (
          <div>
            <h5>您好，您可按照以下步骤操作绑定/解绑第三方账号：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"我的"→点击"设置"→点击"账号与安全"→点击"社交账号绑定"→选择"绑定"或者"解绑"】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/my/accountSecurity')
                }
              >
                蓝色字跳转去绑定/解绑
              </span>
            </p>
            <p>
              温馨提示：解绑成功后页面显示可能会有延迟，您可以退出重进或者重新登录账号查看哦~
            </p>
          </div>
        );
      },
    },
    {
      question: '8. 一个账号允许几台设备登录？',
      answer: `<div>
          <h5>同一个账号在同一时间内只能在一台设备上保持登录状态，若在另一台设备上登录，当前已登录的设备会被自动退出。</h5>
          <p>比如：您的账号登录在手机A上，此时若在另一台手机B上登录同一个账号，手机A上会自动退出登录并提示 "您的登录信息已过期，请重新登录" ，请您确保同一时间只有一台设备能使用该账号。</p>
        </div>`,
    },
    {
      question:
        '9. APP上的宝贝个人信息（如性别、出生日期等）填错了，在哪里修改？',
      answer: () => {
        return (
          <div>
            <h5>您好，您可按照以下步骤操作修改宝贝个人信息：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"我的"→点击"宝贝信息"→按照需求修改即可】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    `rn://ContactStack?initialRoute=EditStudentInfoScreen&parentId=${parentId}&studentId=${studentId}`
                  )
                }
              >
                修改宝贝个人信息
              </span>
            </p>
          </div>
        );
      },
    },
    {
      question:
        '10. 有多个孩子在同一个学校，在APP内已经关联了一个孩子，怎么关联其他孩子？',
      answer: `<div>
          <h5>①若其他孩子已经被添加进学校系统：</h5>
          <p>即您的家庭成员中有任一账号绑定了该学生，且可登录APP。您可联系这位家庭成员让他将您邀请进入该学生的亲友团，邀请成功即关联成功；</p>
          <h5>②若其他孩子尚未被添加进学校系统：</h5>
          <p>您可联系学校老师/负责人将该学生在系统后台添加入园，或将您的手机号填为"第一联系人"，或让"第一联系人"将您邀请进入该学生的亲友团，即关联成功。</p>
        </div>`,
    },
    {
      question: '11. 同一个手机号下绑定有多个宝贝，APP上在哪切换学生？',
      answer: () => {
        return (
          <div>
            <h5>您好，您可按照以下步骤操作切换学生：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击屏幕左上角的学生头像/姓名→点击对应的孩子切换学生】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/my/switchBaby')
                }
              >
                跳转去切换学生
              </span>
            </p>
          </div>
        );
      },
    },
  ],
  attendance: [
    {
      question: '1. 如何绑定考勤卡？',
      answer: () => {
        return (
          <div>
            <h5>
              快捷操作：您可将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=BindCardScreen'
                  )
                }
              >
                绑定考勤卡
              </span>
            </h5>
            <h5>详细步骤：您也可按照以下步骤操作去绑定/换绑考勤卡</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击"设置"→点击"绑定考勤卡"→输入考勤卡背面的卡号→点击"确定"】
            </p>
          </div>
        );
      },
    },
    {
      question: '2. 如何在APP上查看宝贝考勤记录？',
      answer: () => {
        return (
          <div>
            <h5>
              快捷操作：您可将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://AttendanceTab?initialRoute=CheckInScreen'
                  )
                }
              >
                去看宝贝考勤记录
              </span>
            </h5>
            <h5>详细步骤：您也可按照以下步骤操作去查看宝贝的考勤记录</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→进去的首页就是宝贝考勤记录页面（底部导航显示"签到"）】
            </p>
          </div>
        );
      },
    },
    {
      question: '3. 如何通过微信公众号获得考勤通知？',
      answer: () => {
        return (
          <div>
            <h5>第一步：在APP上绑定微信</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"我的"→点击"设置"→点击"账号与安全"→点击"社交账号绑定"→跳转微信绑定】
            </p>
            <p>
              快捷操作➡️更新App至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/my/accountSecurity')
                }
              >
                绑定微信
              </span>
            </p>
            <h5>第二步：在微信关注公众号[掌心平台]</h5>
            <p>打开微信扫一扫考勤卡背面的二维码关注[掌心平台]公众服务号，</p>
            <p>或者在微信搜索"掌心平台"并关注。</p>
            <p>
              温馨提示：您在App上绑定过考勤卡后才可通过公众号收到考勤信息哦~
            </p>
          </div>
        );
      },
    },
    {
      question: '4. 关注了微信公众号但没收到考勤通知？',
      answer: `<div>
          <h5>如果您已经在App上绑定了微信，并关注了公众号，但打卡后仍未在微信上收到考勤消息通知，可能是以下情况导致，请您逐一排查：</p>
          <h5>①请确认是否关注了正确的公众号</h5>
          <p>确保您关注的公众号中有一个名为"掌心平台"的服务号</p>
          <h5>②请检查微信账号是否一致</h5>
          <p>确保您在App上绑定的微信账号和关注公众号的微信账号是同一个</p>
          <h5>③请在微信的"服务号"功能中查看考勤信息通知</h5>
          <p>在聊天列表寻找/在顶部搜索框输入"服务号"查找→找到红色头像的"服务号"并点击进入→在[常用]中找到"掌心平台"服务号并点击进入→即可看到近期所有的考勤通知消息</p>
          <p>温馨提示：公众号中只能记录您绑定微信并关注"掌心平台"后的考勤通知，此前的历史考勤记录请您在App中查看</p>
        </div>`,
    },
    {
      question: '5. 如何通过刷脸考勤打卡？',
      answer: () => {
        return (
          <div>
            <h5>
              您好，按照以下步骤操作完成即可通过刷脸考勤打卡，忘记带考勤卡也不用担心啦！
            </h5>
            <h5>一、若您宝贝所在的幼儿园在使用考勤卡</h5>
            <p> ①绑定考勤卡（已绑卡可忽略）</p>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击"设置"→点击"绑定考勤卡"→输入考勤卡背面的卡号→点击"确定"】
            </p>
            <p>
              快捷操作➡️更新App至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=BindCardScreen'
                  )
                }
              >
                去绑卡
              </span>
            </p>
            <p>②录入考勤人脸</p>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击底部"设置"→点击"我的人脸录入"→分别录入宝贝和家长的正面人脸照】
            </p>
            <p>
              快捷操作➡️更新App至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=ParentFaceScreen'
                  )
                }
              >
                录入人脸
              </span>
            </p>
            <h5>二、若您宝贝所在的幼儿园没使用考勤卡</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击底部"设置"→点击"我的人脸录入"→分别录入宝贝和家长的正面人脸照】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=ParentFaceScreen'
                  )
                }
              >
                录入人脸
              </span>
            </p>
            <p>
              温馨提示：只有幼儿园使用的考勤设备带摄像头，才支持刷脸考勤打卡哦~
            </p>
          </div>
        );
      },
    },
    {
      question: '6. 如何通过APP[扫一扫]考勤打卡？',
      answer: () => {
        return (
          <div>
            <h5>您好，按照以下步骤操作完成即可通过手机App扫码打卡~</h5>
            <h5>第一步：绑定考勤卡（已绑卡可忽略）</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击"设置"→点击"绑定考勤卡"→输入考勤卡背面的卡号→点击"确定"】
            </p>
            <p>
              快捷操作➡️更新App至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=BindCardScreen'
                  )
                }
              >
                蓝色字去绑卡
              </span>
            </p>
            <h5>第二步：打开APP扫码打卡</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击屏幕右上角"+"号→点击"扫一扫"→扫描考勤设备屏幕上的二维码】
            </p>
          </div>
        );
      },
    },
    {
      question: '7. 绑定时显示考勤卡已被占用/学生已离园？',
      answer: `<div>
          <h5>您好，遇到这种情况请您联系学校老师/负责人处理~</h5>
        </div>`,
    },
    {
      question: '8. 在哪录入人脸？',
      answer: () => {
        return (
          <div>
            <h5>您好，您可按照以下步骤操作录入人脸：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击底部"设置"→点击"我的人脸录入"→分别录入宝贝和家长的正面人脸照】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://FaceStack?initialRoute=ParentFaceScreen'
                  )
                }
              >
                录入人脸
              </span>
            </p>
          </div>
        );
      },
    },
    {
      question: '9. [人脸录入]最多可以录入多少个人脸？',
      answer: `<div>
          <p>每个学生对应的最多可录入人脸数是9个，包含1个学生本人的人脸，8个亲友团家长的人脸</p>
        </div>`,
    },
    {
      question: '10. 忘记考勤打卡怎么办？',
      answer: () => {
        return (
          <div>
            <h5>
              若您在接送宝宝的时候忘了考勤打卡，可以在家长端App上申请补卡，按照以下步骤操作即可申请补卡：
            </h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"校园签到"图标→点击底部"申请"→点击"补卡"→填写并提交→等待审批人审核通过】
            </p>
            <p>
              或将App更新至最新版，点击
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://AttendanceStack?initialRoute=FillCardCreateScreen'
                  )
                }
              >
                申请补卡
              </span>
            </p>
          </div>
        );
      },
    },
  ],
  baobei: [
    {
      question: '1. 怎么给本人开通宝贝在线看视频服务？',
      answer: `<div>
            <h5>您好，你可通过以下步骤给本人账号开通宝贝在线看视频服务：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"宝贝在线"图标→点击"开通视频服务"→选择套餐→选择开通人员（即您本人的手机号）→点击"立即支付开通"】
            </p>
            <p>
              温馨提示：当园所开通此服务并授权班级观看后您才可购买并正常使用此服务
            </p>
          </div>`,
    },
    {
      question: '2. 怎么帮其他家庭成员开通宝贝在线看视频服务？',
      answer: `<div>
            <h5>
              您好，在帮其他家庭成员开通此服务之前，您需要确保该家庭成员已经被邀请进入APP的亲友团中。
            </h5>
            <h5>邀请流程：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击右上角"+"号→点击"邀请"→选择邀请方式】
            </p>
            <p>
              - 您可通过[手机号邀请]直接将家庭成员添加到亲友团，而无需对方操作
            </p>
            <p>
              - [微信邀请]和[面对面邀请]则需要被邀请人同时操作才可添加到亲友团
            </p>
            <h5>开通流程：</h5>
            <p>
              【登录家长端APP→点击底部导航栏的"校园"→点击"宝贝在线"图标→点击"开通视频服务"→选择套餐→选择开通人员（即其他家庭成员的手机号）→点击"立即支付开通"】
            </p>
            <p>
              温馨提示：当园所开通此服务并授权班级观看后您才可购买并正常使用此服务
            </p>
          </div>`,
    },
    {
      question: '3. 宝贝在线看视频服务可以同时给多个人开通吗？',
      answer: `<div>
          <p>可以的，您可以在选择开通人员时勾选多个亲友团成员，支付成功后，被勾选的亲友团成员皆可正常使用此服务。</p>
        </div>`,
    },
    {
      question:
        '4. 购买了宝贝在线看视频服务，但是页面提示"学校还未对接宝贝在线，需要对接后才能看"怎么办？',
      answer: `<div>
            <h5>
              请先确认您宝贝所在的园所是否已经对接了监控设备并开通了此服务
            </h5>
            <p>
              是：建议您联系园所确认宝贝所在班级是否已被授权可观看宝贝在线视频，若未被授权则暂时观看不了视频
            </p>
            <p>
              否：暂时看不了视频哦~您可联系园方开通此服务后继续观看或者联系掌心客服操作退款
            </p>
          </div>`,
    },
    {
      question:
        '5. 苹果手机（iOS）看宝贝在线视频时提示"非观看时间段"怎么处理？',
      answer: `<div>
          <h5>①请先在手机的设置→通用→日期与时间→打开[24小时制]→打开[自动设置]</h5>
          <h5>②若仍未解决，请联系老师确认园所设置的观看时间段是多少，并在园所规定的时间段内观看</h5>
        </div>`,
    },
  ],
  flower: [
    {
      question: '1. 小红花是什么？有什么用？',
      answer: `<div>
          <h5>小红花是什么：</h5>
          <p>小红花是掌心平台的虚拟资产，可用于兑换多种权益。用户能通过做任务、参加活动等多种方式获得小红花，在小红花任务页面【点击底部导航"我的"→点击"小红花"→小红花任务页面】可查看自己的小红花明细信息。</p>
          <p>小红花又分为[我的小红花]和[宝贝小红花]：</p>
          <p>[我的小红花]是用户当前登录账号名下积攒的小红花，[宝贝小红花]是宝贝所属家庭亲友团所有用户账号名下积攒的小红花之和。</p>
          <h5>小红花有什么用：</h5>
          <p>①兑换商品：用户在小红花商城可使用小红花兑换虚拟或实物商品，包含纯积分兑换商品和积分抵扣现金商品等，具体兑换规则见商城详情页；</p>
          <p>②参与活动：使用小红花参与活动，获得活动限定奖励；</p>
          <p>③宝贝之星排行：宝贝名下积累的小红花数量越多宝贝之星排名越高。</p>
        </div>`,
    },
    {
      question: '2. 如何获得小红花？',
      answer: `<div>
          <h5>①通过掌心平台专属任务获得小红花</h5>
          <p>通过【点击底部导航"我的"→点击"小红花"→小红花任务页面】即可去做任务，任务类型、任务对应的小红花数量以页面实际展示为准。</p>
          <h5>②通过掌心平台相关玩法获得小红花</h5>
          <p>掌心平台会不定时发布发放小红花的玩法，具体发放规则以玩法奖励说明为准。</p>
          <h5>③通过掌心平台相关活动获得小红花</h5>
          <p>小红花会在各类活动中作为奖励出现，比如发帖活动、打卡活动等等，发放使用规则以活动说明为准。</p>
        </div>`,
    },
    {
      question: '3. 获取的小红花有效期是多久？',
      answer: `<div>
          <p>小红花有效期是自用户领取后的12个月，如用户未在小红花有效期内使用，该小红花将在到期后自动清零。</p>
          <h5>例如：</h5>
          <p>①您在2025年1月1日领取一笔小红花，该小红花有效期为2025年1月1日至2026年1月；</p>
          <p>②您在2025年2月15日领取一笔小红花，该小红花有效期为2025年2月15日至2026年3月。</p>
          <p>温馨提示：针对新用户的特殊小红花奖励和平台活动发放的小红花奖励，使用有效期为包含领取之日在内的7个自然日。</p>
        </div>`,
    },
    {
      question: '4. 在哪里用小红花兑换商品？',
      answer: `<div>
          <h5>您可通过以下步骤抵达积分商城并兑换/购买心仪的商品:</h5>
          <p>【登录家长端APP→点击底部导航"我的"→点击推荐中心的"小红花兑换"图片→积分商城】</p>
          <p>您兑换/购买的商品订单详情可在【积分商城，点击底部"我的"→点击"全部订单"】查看</p>
        </div>`,
    },
    {
      question: '5. 如果想注销账号，已经获取的小红花会怎么样？',
      answer: `<div>
          <h5>掌心APP账号注销时系统将和您确认清空小红花，如重新注册需要重新积攒小红花，回收的小红花将不予退回，请谨慎操作。</h5>
        </div>`,
    },
  ],
  member: [
    {
      question: '1. 掌心会员有哪些权益？',
      answer: () => {
        return (
          <div>
            <h5>感谢您对掌心会员的关注！掌心会员用户可尊享以下9大特权：</h5>
            <p>
              ①每日积分（即小红花）加速：您可享受积分2倍加速特权，在掌心APP内做任务时可获得翻倍的积分奖励；每日还会额外享受加赠的200积分。您可以在使用平台的过程中更快地积累积分，用于参与不定期的平台活动和兑换心仪的商品或服务。
            </p>
            <p>
              ②免费领取会员礼品卡：您每月可免费领取1张掌心会员礼品周卡（7
              天），并将其赠送给亲友团的成员使用。与亲友一同分享掌心会员的优质体验，一起守护宝贝茁壮成长。
            </p>
            <p>
              ③自定义APP启动图：您可以选择将本地相册中的心仪图片设置为APP启动图。让宝贝的美/帅照霸屏吧！美好的一天，从打开掌心APP就能看到TA开始~
            </p>
            <p>
              ④定制实体成长档案：若您购买了掌心会员[年卡]套餐，则可免费定制打印一本精美实体成长档案（后续会新增宝贝相册），记录宝贝成长的每一个独特瞬间，将TA的童年永久珍藏。
            </p>
            <p>
              ⑤一键导出所有照片：无需繁琐操作，一键即可导出宝贝的所有照片，方便您整理、保存和分享这些珍贵的瞬间，让回忆触手可及。
            </p>
            <p>
              ⑥会员专属客服：一对一帮您解决APP使用中的任何问题，提供更快捷更专业的服务，让您的使用体验更加顺畅。
            </p>
            <p>
              ⑦上传下载高清原图：您可上传/下载高清图片，原生态记录宝贝的每一步脚印，不错过任何细节，保留最真实的美好。
            </p>
            <p>
              ⑧纯净无广告：畅享APP纯净模式，免除广告打扰，让您专注于有关宝贝的一切。
            </p>
            <p>⑨尊贵头像标识：拥有专属头像标识，彰显独特身份。</p>
            <p>更多惊喜特权持续更新中...</p>
            <h5>
              我们正在不断孵化更多新功能和服务，掌心会员用户可优先免费体验！现在就开通会员，解锁全部权益吧~
            </h5>
            <h5>
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://MemberStack?initialRoute=ParentsIndexScreen'
                  )
                }
              >
                立即去开通
              </span>
            </h5>
            <p>如有任何使用问题，欢迎随时联系我们的专属客服！</p>
          </div>
        );
      },
    },
    {
      question: '2. 联合会员是什么？有哪些权益？',
      answer: () => {
        return (
          <div>
            <h5>
              感谢您对联合会员的关注！联合会员旨在用更优惠的价格为您提供更便捷、优质、流畅的使用体验，购买开通后可畅享"在线看宝贝视频"功能和掌心会员全系权益，共计10项专属权益，详情见下文：
            </h5>
            <p>
              ①在线看宝贝视频：您可通过掌心APP在授权范围内实时查看已连接的监控设备所拍摄的视频画面，以充分了解孩子在学校的学习/生活/安全/健康状况。
            </p>
            <p>
              ②每日积分（即小红花）加速：您可享受积分2倍加速特权，在掌心APP内做任务时可获得翻倍的积分奖励；每日还会额外享受加赠的200积分。您可以在使用平台的过程中更快地积累积分，用于参与不定期的平台活动和兑换心仪的商品或服务。
            </p>
            <p>
              ③免费领取会员礼品卡：您每月可免费领取1张掌心会员礼品周卡（7
              天），并将其赠送给亲友团的成员使用。与亲友一同分享掌心会员的优质体验，一起守护宝贝茁壮成长。
            </p>
            <p>
              ④自定义APP启动图：您可以选择将本地相册中的心仪图片设置为APP启动图。让宝贝的美/帅照霸屏吧！美好的一天，从打开掌心APP就能看到TA开始~
            </p>
            <p>
              ⑤定制实体成长档案：若您购买了掌心会员[年卡]套餐，则可免费定制打印一本精美实体成长档案（后续会新增宝贝相册），记录宝贝成长的每一个独特瞬间，将TA的童年永久珍藏。
            </p>
            <p>
              ⑥一键导出所有照片：无需繁琐操作，一键即可导出宝贝的所有照片，方便您整理、保存和分享这些珍贵的瞬间，让回忆触手可及。
            </p>
            <p>
              ⑦会员专属客服：一对一帮您解决APP使用中的任何问题，提供更快捷更专业的服务，让您的使用体验更加顺畅。
            </p>
            <p>
              ⑧上传下载高清原图：您可上传/下载高清图片，原生态记录宝贝的每一步脚印，不错过任何细节，保留最真实的美好。
            </p>
            <p>
              ⑨纯净无广告：畅享APP纯净模式，免除广告打扰，让您专注于有关宝贝的一切。
            </p>
            <p>⑩尊贵头像标识：拥有专属头像标识，彰显独特身份。</p>
            <p>更多惊喜特权持续更新中...</p>
            <p>
              更新最新版APP，现在开通，立享以上10大特权，还可免费解锁体验后续新增的功能和服务！
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/babyOnline/videoPlay')
                }
              >
                立即去开通
              </span>
            </p>
            <p>如有任何使用问题，欢迎随时联系我们的专属客服！</p>
          </div>
        );
      },
    },
    {
      question: '3. APP里面现在一共有哪些付费会员套餐？有什么区别？',
      answer: `<div>
          <h5>您好，平台内目前一共有3类付费套餐，主要在权益和价格上有区别，详情见下表：</h5>
          <p><img src="https://unicorn-media.ancda.com/production/app/member/menberTable.png" alt="" /></p>
        </div>`,
    },
    {
      question: '4. 亲友赠送的会员礼品周卡在哪领取？',
      answer: `<div>
          <h5>您好，您可通过以下两种方式领取会员礼品周卡：</h5>
          <h5>①通过通知栏领取</h5>
          <p>从手机屏幕左侧顶部下滑打开通知栏找到领取通知，点击直接跳转领取页面</p>
          <h5>②通过系统消息领取</h5>
          <p>【登录家长端APP→点击底部导航栏的"消息"→点击"系统消息"】找到会员礼品周卡领取通知，点击跳转领取页面</p>
        </div>`,
    },
    {
      question: '5. 免费会员礼品周卡有什么赠送和领取的限制吗？',
      answer: `<div>
          <h5>赠送限制：</h5>
          <p>只有付费开通过会员服务的用户可以在会员有效期内，每月免费赠送1张会员礼品周卡给亲友团的成员</p>
          <h5>领取限制：</h5>
          <p>①每张礼品卡仅限非会员用户领取，且每人每月最多仅能领取1张</p>
          <p>②单个用户每月仅可领取1次，不随着赠送者数量增加而变化</p>
        </div>`,
    },
    {
      question: '6. 会员礼品周卡赠送错人怎么办？',
      answer: `<div>
          <h5>会员礼品周卡赠送后不支持撤回，请在赠送的时候选择正确的领取人哦~</h5>
          <p>您可在次月领取新的会员礼品周卡，再赠送给您想要赠送的亲友。</p>
        </div>`,
    },
    {
      question: '7. 怎么看会员礼品周卡有没有送出去？',
      answer: `<div>
          <h5>查看路径：【点击底部导航"我的"→点击[掌心会员]→会员中心→"免费领取会员"权益】</h5>
          <p>显示[已送出0份/共1份]：会员礼品周卡尚未赠送</p>
          <p>显示[已送出1份/共1份]：会员礼品周卡已赠送</p>
        </div>`,
    },
    {
      question: '8. 已有会员再买会员，时长会顺延吗?',
      answer: `<div>
          <h5>是的，同一个账号若当前已是会员，又领取或购买了新的会员，新获取的会员时长会自动累加到您的账户内。</h5>
          <p>举例说明：您已经开通了掌心会员包月自动续费服务，当前是会员身份，有效期截止2025-05-18。</p>
          <p>①若额外购买了掌心会员月卡，则会员时长自动顺延1个月，有效期截止2025-06-18</p>
          <p>②若领取了平台活动赠送的5天会员，则会员时长自动顺延5天，有效期截止2025-05-23</p>
        </div>`,
    },
    {
      question: '9. 为什么会员还没到期就扣钱了？',
      answer: `<div>
            <h5>会员没到期就扣钱是因为您购买会员时选择了连续包的会员类型。</h5>
            <p>
              若不需要连续包月/季/半年的会员类型，您可以点击关闭自动续费，关闭后下次就不会自动扣钱了。
            </p>
            <p>
              温馨提示：若您在订阅服务期内未主动取消自动续费服务，则第三方支付渠道将自付费服务到期前1天开始从您开通本自动续费服务时绑定的第三方支付账户（包括但不限于支付宝账户、微信钱包账户）等账户余额中自动代扣下一个订阅服务期费用，从而延长该订阅期对应的付费服务有效期。
            </p>
          </div>`,
    },
    {
      question: '10. 如何取消会员自动续费？',
      answer: () => {
        return (
          <div>
            <h5>您好 ，快捷操作点此取消自动续费</h5>
            <p>或按照以下步骤操作：</p>
            <p>
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://MemberStack?initialRoute=AutoRenewalScreen'
                  )
                }
              >
                点击这里取消自动续费
              </span>
            </p>
            <p>
              您也可直接在微信、支付宝等第三方支付客户端内取消自动扣款设置（具体操作方式以各产品实际功能为准）。
            </p>
            <p>
              温馨提示：若页面显示「当前未开通」说明已成功取消自动续费，取消成功后下个月开始就不会再次续费，请您放心
              ~
            </p>
          </div>
        );
      },
    },
    {
      question: '11.开通的会员可以退款吗？',
      answer: () => {
        return (
          <div>
            <h5>
              尊敬的家长，您好！感谢您选择我们的会员服务。根据平台规则及会员协议，虚拟类会员产品在开通并享受权益后（例如APP纯净模式/已领取的加速积分/已使用的周卡等），暂不支持无理由退款。您可继续在已经开通的会员有效期内放心使用各项权益和服务。
              若您遇到权益未到账、功能异常或误购等情况，请随时联系我们核查处理，我们将为您协调补发权益或提供其他补偿方案。您可通过【登录家长端APP→点击底部导航栏的“我的”→点击“掌心会员”→滑动屏幕至最底点击“专属客服”】提交凭证，我们会全力协助您解决❤️
              温馨提示：您可通过【登录家长端APP→点击底部导航栏“我的”→点击“我的客服”→点击自助服务的“管理自动续费”】查看是否开通了会员自动续费服务。
              快捷操作：
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://MemberStack?initialRoute=AutoRenewalScreen'
                  )
                }
              >
                点击查看是否开通自动续费
              </span>
              若显示「当前未开通」则说明您未开通自动续费，若显示「取消自动续费」则说明您当前开通了自动续费，您可按照需要选择是否取消自动续费服务。取消后，系统将不会自动扣费帮您延续会员服务有效期。
            </h5>
          </div>
        );
      },
    },
    {
      question: '12.为什么我的APP上没有联合会员？',
      answer: () => {
        return (
          <div>
            <h5>
              联合会员套餐当前仅针对部分用户开放，您感兴趣的话可以持续关注，后续会逐步覆盖更多用户~
            </h5>
            <p>套餐展示页面：宝贝在线页面、掌心会员开通页面。</p>
          </div>
        );
      },
    },
  ],
  ad: [
    {
      question: '1. 为什么会有广告？',
      answer: `<div>
          <h5>感谢您选择使用我们的APP！我们非常理解广告可能会影响您的使用体验，在此向您真诚致歉。</h5>
          <p>目前广告是支撑APP免费运营、持续优化功能及服务的重要方式，它帮助我们让更多用户无需付费即可享受核心功能。但我们始终将用户体验放在首位，已采取以下措施平衡这一问题：</p>
          <h5>①严格筛选广告内容，确保不打扰核心操作</h5>
          <h5>②提供广告"跳过/关闭"按钮</h5>
          <p>您可点击广告页面右上角的"跳过"或者"关闭"按钮，跳过/关闭当前广告</p>
          <h5>③提供会员服务</h5>
          <p>您可开通会员服务去除所有广告，并解锁更多高级权益。</p>
          <p>您的反馈对我们至关重要，若遇到低质或频繁广告，欢迎随时通过【我的-我的客服-我要反馈】提交具体场景，我们将针对性优化。</p>
        </div>`,
    },
    {
      question: '2. 如何去除广告？',
      answer: () => {
        return (
          <div>
            <h5>
              您好，我们非常理解广告可能会对您的使用体验造成影响，在此向您真诚致歉，为您提供两种去除广告的方式，您可根据需求选择~
            </h5>
            <h5>①免费快捷操作</h5>
            <p>
              手动关闭广告：大部分广告右上角有【跳过/关闭】按钮（点击后3秒内生效）
            </p>
            <h5>②深度去除方案</h5>
            <p>
              开通「掌心会员/联合会员」即可在会员有效期内长期关闭所有广告，同时解锁更多VIP专属功能（如高清图片、一键导出、积分加速等等）
            </p>
            <p>
              首次开通低至9.9元/月，
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage(
                    'rn://MemberStack?initialRoute=ParentsIndexScreen'
                  )
                }
              >
                点击去开通
              </span>
            </p>
            <p>
              若您遇到无法关闭的广告或异常弹窗，请随时
              <span
                className="text-[blue]"
                onClick={() => navigationToNativePage('app://app/my/feedback')}
              >
                点击这里反馈
              </span>
              ，我们将优先为您处理！
            </p>
          </div>
        );
      },
    },
    {
      question: '3. 如何清理APP的广告缓存？',
      answer: () => {
        return (
          <div>
            <h5>您可按照以下步骤操作清理APP缓存（包括广告缓存）：</h5>
            <p>
              【登录家长端APP→点击底部导航栏"我的"→点击"设置"→点击"清理缓存"→选择要清理的缓存类型】
            </p>
            <p>
              <span
                className="text-[blue]"
                onClick={() =>
                  navigationToNativePage('app://app/my/clearCache')
                }
              >
                快捷操作：点击蓝色字跳转去清理缓存
              </span>
            </p>
          </div>
        );
      },
    },
    {
      question: '4. 点广告买的商品该如何联系售后或退款？',
      answer: `<div>
          <h5>您好，广告内的商品并非出自掌心，相关款项也并非我司接收。若要退款，请优先联系收款商家（退款速度更快），操作步骤如下：</h5>
          <p>第一步：请您确认购买广告商品时使用的支付方式（比如支付宝、微信、苹果支付等）</p>
          <p>第二步：在对应的支付渠道查找此广告商品的支付订单，并在订单详情页找到收款商家，联系退款</p>
          <h5>①支付宝支付</h5>
          <p若使用支付宝支付，请通过以下方式【支付宝APP→我的→账单→找到对应扣款金额的订单→点进订单详情页→找到"收款方全称"】找到广告商品的收款方，联系该收款方的客服询问退款事项；</p>
          <h5>②微信支付</h5>
          <p>使用微信支付，请通过以下方式【微信APP→在顶部搜索框输入并搜索"微信支付"→点击进入[微信支付]服务号→找到对应扣款金额的订单→点进订单详情页→找到"商户全称"】找到广告商品的收款方，然后将页面滑到最底，点击【联系商家】处的商家电话即可联系收款方询问退款事项</p>
          <h5>③苹果支付</h5>
          <p>使用苹果支付，请点击<a href="https://support.apple.com/zh-cn/118223" target="_blank" rel="noreferrer">退款说明</a>获取详情</p>
          <p>如果以上链接打不开，您可以通过以下方式【苹果手机"设置"→Apple账户→媒体与购买项目→查看账户→购买记录→点击广告商品订单→订单详细信息页面→点击"报告问题"】抵达申请退款页面</p>
          <p>或致电苹果官方客服400-666-8800申请退款</p>
          <p>当然，如果您需要我们的帮助，请致电400-887-9121，我们会尽可能地协助您联系扣款商家退款。</p>
          <p>为帮助我们更快地定位到商家，请您在联系客服时提供以下两个截图：</p>
          <p>①广告+展示广告页面的全屏截图</p>
          <p>②点击广告后进入的页面</p>
        </div>`,
    },
    {
      question:
        '5. 为什么点了看视频的小红花任务没反应？要么就是提示"请求过于频繁，请稍后。。。"？',
      answer: () => {
        return (
          <div>
            <h5>小红花任务的视频广告加载需要一定的时间，请您：</h5>
            <p>①点击后耐心等待3~5秒，广告会在最多10秒内加载展示出来</p>
            <p>②若未展示出来，请您再重新多尝试几次</p>
            <p>
              ③如果视频始终没有展示出来，您可以
              <span
                className="text-[blue]"
                onClick={() => navigationToNativePage('app://app/my/feedback')}
              >
                点击这里反馈
              </span>
              ，我们会第一时间介入处理
            </p>
          </div>
        );
      },
    },
    {
      question: '6. 弹窗广告"关不掉"或者找不到关闭按钮？',
      answer: () => {
        return (
          <div>
            <h5>①避免误触广告内容，仔细查找关闭按钮</h5>
            <p>
              检查弹窗四角、边缘或顶部，部分广告的关闭按钮会比较小或颜色较淡（如"×"或"关闭"字样）
            </p>
            <p>部分广告需等待倒计时结束（如5秒）后才会显示关闭按钮</p>
            <h5>②若您确实找不到关闭按钮，请点击弹窗广告任意位置</h5>
            <p>
              针对部分特别难找到关闭按钮或者因为加载问题没能显示关闭按钮的广告，我们已经做了自动关闭处理，您需要点击广告触发自动关闭。
            </p>
            <p>
              （点击广告后您可能会在APP内打开新页面或者跳转第三方APP，请您不用担心，直接关闭页面或者重新返回掌心APP即可，原有的弹窗广告会自动关闭，您可继续正常使用APP）
            </p>
            <p>
              注：本APP内的广告样式并非本司开发，若您遇到异常的广告样式，可以截图或录屏后，
              <span
                className="text-[blue]"
                onClick={() => navigationToNativePage('app://app/my/feedback')}
              >
                点击这里反馈
              </span>
              ，我们将针对您的问题联系广告供应方整改。
            </p>
          </div>
        );
      },
    },
  ],
  other: [
    {
      question: '1. 对APP的问题/建议反馈在哪里提交？',
      answer: () => (
        <div>
          <h5>
            【登录家长端APP→点击底部导航栏的"我的"→点击"我的客服"→点击屏幕底部的"我要反馈"→根据页面指引填写并提交】
          </h5>
          <h5>
            快捷操作：
            <span
              className="text-[blue]"
              onClick={() => navigationToNativePage('app://app/my/feedback')}
            >
              点击蓝色字跳转去反馈问题/建议&gt;&gt;&gt;
            </span>
          </h5>
          <h5>
            温馨提示：我们将在反馈页面默认为您勾选上传日志选项，上传日志有助于我们更快更精准地定位到您遇到过的问题，请您在提交时尽可能不要取消勾选~
          </h5>
        </div>
      ),
    },
    {
      question: '2. 怎么邀请家庭成员加入亲友团？',
      answer: `<div>
        <h5>您好，您可通过以下方法邀请他人加入宝贝的亲友团：</h5>
        <p>【登录家长端APP→点击底部导航栏的"校园"→点击右上角"+"号→点击"邀请"→选择邀请方式】</p>
        <p>- 您可通过[手机号邀请]直接将家庭成员添加到亲友团，而无需对方操作</p>
        <p>- [微信邀请]和[面对面邀请]则需要被邀请人同时操作才可添加到亲友团</p>
      </div>`,
    },
    {
      question: '3. 怎么将指定成员移出宝贝亲友团？',
      answer: () => (
        <div>
          <h5>您好，您可以按照以下操作将指定成员移出宝贝的亲友团：</h5>
          <h5>
            【登录家长端APP→点击底部导航的"我的"→点击"邀请亲友"→在「已邀请的亲友」处找到您想要移出亲友团的成员→点击其手机号后对应的"删除"按钮】
          </h5>
          <h5>
            快捷操作：
            <span
              className="text-[blue]"
              onClick={() =>
                navigationToNativePage(
                  'rn://FriendsStack?initialRoute=IndexScreen&friendsOpen=1'
                )
              }
            >
              或点击蓝色字跳转去移除亲友&gt;&gt;&gt;页面
            </span>
          </h5>
        </div>
      ),
    },
    {
      question:
        '4. 老师发布的亲子任务（班级作业），我完成提交后会被其他家长看到吗？',
      answer: `<div>
        <h5>您好，为增强家校间的沟通和透明度、促进家长间的经验共享和学习等，您完成提交的亲子任务动态会在班级范围内公开可见，其他家长也是能看到的哦~</h5>
        <h5>（1）增强家校沟通与透明度</h5>
        <p>目标一致性：家长通过观察其他家庭的实践，能更直观地理解教师的教育意图，明确亲子任务的目标（如培养动手能力、情感互动等），从而在家庭中延续学校的教育理念。</p>
        <p>教育过程透明化：教师能展示教学成果，家长也能看到集体努力的方向，减少信息不对称，建立信任。</p>
        <h5>（2）促进家长间的经验共享与学习</h5>
        <p>灵感激发：家长可借鉴他人的创意方法（如手工技巧、互动游戏），丰富自己的育儿策略，避免单一化；</p>
        <p>缓解教育焦虑：新手家长通过观察其他家庭的实践，获得参考案例，减少因"不会教"而产生的压力；</p>
        <p>多元化视角：不同家庭的文化背景和教育方式得以展现，促进包容性教育环境的形成。</p>
        <h5>（3）提升家长育儿参与积极性</h5>
        <p>正向激励：公开环境能无形中激励家长更认真地引导宝贝完成任务，形成"榜样效应"或良性竞争，避免敷衍了事；</p>
        <p>社会认同感：家长的付出被班级社群看见，获得认可（如点赞、评论），增强参与教育的成就感。</p>
        <h5>（4）构建家园共育的支持网络</h5>
        <p>互助社群形成：家长可通过评论互动，互相提供建议或资源（如推荐绘本、活动场地），形成非正式支持系统；</p>
        <p>问题协作解决：若某家庭在任务中遇到困难（如材料短缺），其他家长或教师可及时提供帮助，体现协作精神。</p>
        <p>总之，亲子任务班级内公开可以通过"展示-互动-反馈"的闭环，将家庭与学校的教育力量凝聚为动态合作的共同体。它不仅优化了信息流通效率，更通过社群的联结作用，让教育从"孤岛"走向"生态"，实现儿童成长过程中家庭与学校的深度协同。</p>
      </div>`,
    },
    {
      question: '5. "聊聊"功能相关问答',
      answer: `<div>
        <h5>（1）家长可以自己建群或私聊其他家长吗？</h5>
        <p>目前暂不支持家长单独建群以及和其他家长私聊哦~有问题您可联系学校老师协助处理。</p>
        <h5>（2）家长怎么在[聊聊]联系老师？</h5>
        <p>【点击底部导航栏的"消息"→点击"聊聊"→点击右上角"+"→点击"发起聊天"→选择对应的老师→跳转聊天页面→发送消息】即可，发送过消息的聊天后续会在[聊聊]主页的聊天列表呈现。</p>
        <p>温馨提示：只有您宝贝所在学校开通了聊聊功能您才可使用哦~</p>
      </div>`,
    },
    {
      question: '6. 新学期，家长怎么扫码入班？',
      answer: `<div>
        <h5>您好，您需要使用微信扫描学校老师提供的园所二维码或者班级二维码，按照页面指引填写宝贝姓名、性别、出生日期、班级（班级二维码不需要填写）、监护人联系方式等，提交后即可入班。</h5>
      </div>`,
    },
    {
      question: '7. 发布动态的时候支持最多放多少张照片和多长的视频？',
      answer: `<div>
        <h5>您好，发布班级动态的时候最多可以发40张照片，视频最大时长为6分钟，文字限制上限为1000字</h5>
      </div>`,
    },
    {
      question: '8. 发布动态时无法选择图片怎么办？',
      answer: `<div>
        <h5>安卓手机：设置→应用管理→授权管理-应用权限管理，找到掌心APP，开启相机和手机存储的权限；</h5>
        <h5>苹果手机：设置→下滑找到"App"→搜索"掌心"找到掌心宝贝/掌心智校家长端APP→点击"照片"→选择"完全访问"即可</h5>
      </div>`,
    },
    {
      question: '9. 发布动态时发不成功要如何处理?',
      answer: () => (
        <div>
          <h5>您好，您可尝试以下几种方式解决发布不了动态的问题：</h5>
          <p>①切换网络。WiFi切换成数据或者数据切换成WiFi，重新发布；</p>
          <p>②若在发布时出现进度条反复跳动的情况，请您耐心等待；</p>
          <p>③清除APP缓存或者卸载重装APP后再次发布。</p>
          <p>
            以上三种情况都尝试了但仍然发布不成功，请您点击
            <span
              className="text-[blue]"
              onClick={() => navigationToNativePage('app://app/my/feedback')}
            >
              蓝色字部分去提交反馈和日志
            </span>
            ，并联系客服提供所用手机型号、软件版本、APP版本等，我们将马上为您解决问题。
          </p>
        </div>
      ),
    },
  ],
};
export const tabs = [
  { key: 'account', label: '账号' },
  { key: 'attendance', label: '考勤' },
  { key: 'baobei', label: '宝贝在线' },
  { key: 'member', label: '掌心会员' },
  { key: 'ad', label: '广告' },
  { key: 'flower', label: '小红花' },
  { key: 'other', label: '其他' },
];
export const tabsObj = {
  account: '账号',
  attendance: '考勤',
  baobei: '宝贝在线',
  member: '掌心会员',
  ad: '广告',
  flower: '小红花',
  other: '其他',
};
export const faqList = {
  account: [
    '1. 登录时忘记密码怎么办？',
    '2. 家长首次登录APP时的默认初始密码是多少？',
    '3. 登录时突然提示"账号不存在，请先联系幼儿园添加！"怎么办？',
    '4. 登录后突然看不了班级圈怎么办？',
    '5. 如何换绑手机号？',
    '6. 如何修改登录密码？',
    '7. 如何绑定或解绑第三方账号（微信）？',
    '8. 一个账号允许几台设备登录?',
    '9. APP上的宝贝个人信息(如性别、出生日期等)填错了，在哪里修改?',
    '10. 有多个孩子在同一个学校，在APP内已经关联了一个孩子，怎么关联其他孩子？',
    '11. 同一个手机号下绑定有多个宝贝，APP上在哪切换学生?',
  ],
  attendance: [
    '1. 如何绑定考勤卡?',
    '2. 如何在APP上查看宝贝考勤记录？',
    '3. 如何通过微信公众号获得考勤通知?',
    '4. 关注了微信公众号但没收到考勤通知?',
    '5. 如何通过刷脸考勤打卡?',
    '6. 如何通过APP[扫一扫]考勤打卡?',
    '7. 绑定时显示考勤卡已被占用?',
    '8. 在哪录入人脸?',
    '9. [人脸录入]最多可以录入多少个人脸?',
    '10. 忘记考勤打卡怎么办?',
  ],
  baobei: [
    '1. 怎么给本人开通宝贝在线看视频服务?',
    '2. 怎么帮其他家庭成员开通宝贝在线看视频服务?',
    '3. 宝贝在线看视频服务可以同时给多个人开通吗?',
    '4. 购买了宝贝在线看视频服务，但是页面提示"学校还未对接宝贝在线，需要对接后才能看"怎么办?',
    '5. 苹果手机(iOS)看宝贝在线视频时提示"非观看时间段"怎么处理?',
  ],
  member: [
    '1. 掌心会员有哪些权益?',
    '2. 联合会员是什么?有哪些权益?',
    '3. APP里面现在一共有哪些付费会员套餐?有什么区别?',
    '4. 亲友赠送的会员礼品周卡在哪领取?',
    '5. 免费会员礼品周卡有什么赠送和领取的限制吗?',
    '6. 会员礼品周卡赠送错人怎么办?',
    '7. 怎么看会员礼品周卡有没有送出去?',
    '8. 已有会员再买会员，时长会顺延吗?',
    '9. 为什么会员还没到期就扣钱了?',
    '10. 如何取消会员自动续费?',
    '11. 开通的会员可以退款吗?',
    '12. 为什么我的APP上没有联合会员?',
  ],
  ad: [
    '1. 为什么会有广告?',
    '2. 如何去除广告?',
    '3. 如何清理APP的广告缓存?',
    '4. 点广告买的商品该如何联系售后或退款?',
    '5. 为什么点了看视频的小红花任务没反应?要么就是提示"请求过于频繁,请稍后。。。"?',
    '6. 弹窗广告"关不掉"或者找不到关闭按钮?',
  ],
  flower: [
    '1. 小红花是什么?有什么用?',
    '2. 如何获得小红花?',
    '3. 获取的小红花有效期是多久?',
    '4. 在哪里用小红花兑换商品?',
    '5. 如果想注销账号，已经获取的小红花会怎么样?',
  ],
  other: [
    '1. 对APP的问题/建议反馈在哪里提交?',
    '2. 怎么邀请家庭成员加入亲友团?',
    '3. 怎么将指定成员移出宝贝亲友团?',
    '4. 老师发布的亲子任务(班级作业)，我完成提交后会被其他家长看到吗?',
    '5. "聊聊"功能相关问答',
    '6. 新学期，家长怎么扫码入班?',
    '7. 发布动态的时候支持最多放多少张照片和多长的视频?',
    '8. 发布动态时无法选择图片怎么办?',
    '9. 发布动态时发不成功要如何处理?',
  ],
};
