'use client';

import { Divider } from 'antd-mobile';
import Cookies from 'js-cookie';
import { ThumbsDown, ThumbsUp } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { hinaTrack } from '@/utils/index';

import { faqContentList, tabsObj } from '../components/content';

const QaContent = () => {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('activeTab');
  const questionIndex = searchParams.get('questionIndex') as string;
  const parentId = Cookies.get('parentId');
  const status = Cookies.get(`${parentId}_${activeTab}_${questionIndex}`);
  const [feedback, setFeedback] = useState<'up' | 'down' | null>(
    status as 'up' | 'down' | null
  );
  const qaContent = faqContentList[activeTab as keyof typeof faqContentList][
    Number(questionIndex)
  ] as (typeof faqContentList)[keyof typeof faqContentList][number];
  const handleFeedback = (type: 'up' | 'down') => {
    if (status) return;
    hinaTrack('QAdetails_useful_click', {
      CSRQA_specific: qaContent.question,
      CSRQA_useful: type === 'up' ? '是' : '否',
    });
    if (parentId) {
      Cookies.set(`${parentId}_${activeTab}_${questionIndex}`, type);
      setFeedback(type);
    }
  };
  useEffect(() => {
    if (activeTab && questionIndex) {
      hinaTrack('CSR_selfservice_click', {
        CSRQA_type: tabsObj[activeTab as keyof typeof tabsObj],
        CSRQA_specific: qaContent.question,
      });
    }
  }, [questionIndex, activeTab, qaContent.question]);
  return (
    <div className="mx-auto max-w-4xl overflow-hidden rounded-xl bg-white transition-all ">
      <div className="p-8">
        <div className="mb-8">
          <div className="mb-6 flex items-start">
            <div className="relative mr-3 size-6 shrink-0">
              <div className="absolute inset-0 rounded-full border-[1.5px] border-primary" />
              <span className="absolute inset-0 flex items-center justify-center font-normal text-primary text-sm">
                问
              </span>
            </div>
            <h1 className="font-medium text-gray-900 text-lg leading-relaxed">
              {qaContent.question}
            </h1>
          </div>
          <div className="flex items-start">
            <div className="relative mr-3 size-6 shrink-0">
              <div className="absolute inset-0 rounded-full border-[1.5px] border-primary" />
              <span className="absolute inset-0 flex items-center justify-center font-normal text-primary text-sm">
                答
              </span>
            </div>
            <div className="prose max-w-none text-base text-gray-700 leading-relaxed">
              {typeof qaContent.answer === 'function' ? (
                qaContent.answer()
              ) : (
                <div dangerouslySetInnerHTML={{ __html: qaContent.answer }} />
              )}
            </div>
          </div>
        </div>
      </div>

      <div className=" bg-white px-8 py-6">
        <Divider className="text-gray-500 text-sm" contentPosition="center">
          以上回答对你有帮助吗
        </Divider>
        <div className="mt-6 flex justify-center gap-6">
          <button
            className={`flex min-w-[100px] items-center justify-center gap-2 rounded-full border px-4 py-2.5 text-sm transition-all ${
              feedback === 'up'
                ? 'border-[#00C6A2] bg-[#00C6A21A] text-[#00C6A2]'
                : 'border-gray-200 bg-white text-gray-600'
            }`}
            onClick={() => handleFeedback('up')}
            type="button"
          >
            <ThumbsUp size={18} /> 有用
          </button>
          <button
            className={`flex min-w-[100px] items-center justify-center gap-2 rounded-full border px-4 py-2.5 text-sm transition-all ${
              feedback === 'down'
                ? 'border-[#00C6A2] bg-[#00C6A21A] text-[#00C6A2]'
                : 'border-gray-200 bg-white text-gray-600'
            }`}
            onClick={() => handleFeedback('down')}
            type="button"
          >
            <ThumbsDown size={18} /> 没用
          </button>
        </div>
      </div>
    </div>
  );
};

export default QaContent;
