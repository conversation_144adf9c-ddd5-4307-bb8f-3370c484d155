'use client';

import { Button } from 'antd-mobile';
import * as React from 'react';

import { isPalmBaby } from '@/lib/utils';

import styles from './index.module.css';

export default function Download() {
  const isPalmBabyApp =
    typeof window !== 'undefined'
      ? isPalmBaby(window.location.hostname)
      : false;

  const downloadApp = () => {
    const url = isPalmBabyApp
      ? 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.parents.teacher'
      : 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ancda.app.teacher';
    window.location.href = url;
  };

  return (
    <div className={`mt-8 w-1/2 ${styles.buttonAnimation}`}>
      <Button
        block
        color="primary"
        onClick={() => downloadApp()}
        shape="rounded"
        type="submit"
      >
        立即下载 APP
      </Button>
    </div>
  );
}
