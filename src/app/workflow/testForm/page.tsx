'use client';

import { Button } from 'antd-mobile';
import FormRender, { useForm } from 'form-render-mobile';
import { useState } from 'react';

import { schema as testSchema } from '../create/components/data';
import address from '../create/components/widgets/Address';
import attachment from '../create/components/widgets/Attachment';
import checkbox from '../create/components/widgets/Checkbox';
import checkboxes from '../create/components/widgets/Checkboxes';
import classPicker from '../create/components/widgets/ClassPicker';
import departmentPicker from '../create/components/widgets/DepartmentPicker';
import image from '../create/components/widgets/Image';
import richText from '../create/components/widgets/RichText';
import signature from '../create/components/widgets/Signature';
import studentPicker from '../create/components/widgets/StudentPicker';
import table from '../create/components/widgets/Table';
import teacherPicker from '../create/components/widgets/TeacherPicker';
import video from '../create/components/widgets/Video';

export default function Index() {
  const form = useForm();

  const [_isMounted, setIsMounted] = useState(false);

  const onFinish = (formData: any) => {
    console.log('getSchema', form.getSchema());
    console.log('formData', JSON.stringify(formData, null, 2));
  };

  const onMount = () => {
    console.log('onMount');
    // setLoading(false);
    setIsMounted(true);

    form.setValues({
      input: 'erwerew',
      textArea: 'werewrew',
      inputNumber: '34343',
      amountNumber: '3343',
      phoneNumber: '13233334444',
      signature:
        'https://unicorn-media.ancda.com/test/workflow/2023-12-08/Uoyv4A35.jpg',
      image: [
        {
          thumbnailUrl:
            'https://unicorn-media.ancda.com/test/workflow/2023-12-08/Uoyv4A35.jpg?x-image-process=image/resize,m_fill,w_300,h_300,limit_0/format,jpg',
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-08/Uoyv4A35.jpg',
          type: 'image',
        },
      ],
      video: [
        {
          thumbnailUrl:
            'https://unicorn-media.ancda.com/test/workflow/2023-12-08/VltPA3zx.mp4?x-workflow-graph-name=video-thumbnail',
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-08/VltPA3zx.mp4',
          type: 'video',
        },
      ],
      attachment: [
        {
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-09/x9zFVt1q.jpg',
          name: '51bV5sFSOuS._AC_SL1500_.jpg',
          type: 'image/jpeg',
          size: 70_327,
        },
        {
          url: 'https://unicorn-media.ancda.com/test/workflow/2023-12-09/EJvj8p1B.jpg',
          name: 'c22f9aa5-b899-4bde-8039-b7b2992a9e7b.jpg',
          type: 'image/jpeg',
          size: 8_606_715,
        },
      ],
      picker: ['1'],
      checkboxes: ['1'],
      date: '2023-12',
      dateTime: '2023-12-08 03:34',
      slider: [0, 35],
      switch: true,
      address: {
        area: [
          {
            value: '130000000000',
            label: '河北省',
          },
          {
            value: '130200000000',
            label: '唐山市',
          },
          {
            value: '130203000000',
            label: '路北区',
          },
        ],
        detail: 'ererer',
      },
      rate: 4,
      selector: ['2'],
      selector2: ['2', '1'],
      stepper: 3,
      cascader: [1, 2],
      radio: '1',
      checkbox: '1',
      group1: {
        input: '233',
        input2: '444',
      },
      group2: {
        input1: '3434',
        input2: '3434',
      },
      card: {
        input1: '34',
        input2: '343',
        input3: '5535',
      },
      list: [
        {
          input1: '343',
          input2: '3434',
          input3: '3434',
        },
      ],
      studentPicker: [
        {
          studentId: 'student001',
          studentName: '张小明',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          classId: 'class001',
        },
        {
          studentId: 'student002',
          studentName: '李小红',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
          classId: 'class001',
        },
      ],
      teacherPicker: [
        {
          id: 'teacher001',
          name: '王老师',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
        },
        {
          id: 'teacher002',
          name: '赵老师',
          avatar:
            'https://unicorn-media.ancda.com/production/app/avatar/app_avatar_student_girl.png',
        },
      ],
      classPicker: [
        {
          id: '81663448080973830',
          name: '小一班',
          gradeId: '20626911856165578',
          gradeName: '小班',
        },
      ],
      classMultiple: [
        {
          id: '81663448080973830',
          name: '小一班',
          gradeId: '20626911856165578',
          gradeName: '小班',
        },
        {
          id: 'class002',
          name: '小二班',
          gradeId: '22772076523815015',
          gradeName: '中班',
        },
      ],
      departmentPicker: [
        {
          id: 'dept001',
          name: '教务处',
        },
      ],
      departmentMultiple: [
        {
          id: 'dept001',
          name: '教务处',
        },
        {
          id: 'dept002',
          name: '财务部',
        },
      ],
    });
  };

  return (
    <div className="flex flex-col bg-[#F7F9FF]">
      <FormRender
        footer={
          <Button block color="primary" size="large" type="submit">
            提交
          </Button>
        }
        // requiredMarkStyle="text-required"
        // displayType="column"
        form={form}
        mode="card"
        onFinish={onFinish}
        onMount={onMount}
        schema={testSchema}
        widgets={{
          checkbox,
          checkboxes,
          richText,
          signature,
          image,
          video,
          attachment,
          address,
          table,
          student: studentPicker,
          teacher: teacherPicker,
          class: classPicker,
          department: departmentPicker,
        }}
      />
    </div>
  );
}
