'use client';

import React, { useState } from 'react';
import ClassPickerWidget from '../create/components/widgets/ClassPicker';

interface ClassInfo {
  id: string;
  name: string;
  gradeName: string;
  fullName: string;
}

export default function TestClassPickerPage() {
  const [singleValue, setSingleValue] = useState<ClassInfo | null>(null);
  const [multipleValue, setMultipleValue] = useState<ClassInfo[]>([]);

  return (
    <div className="mx-auto max-w-2xl space-y-8 p-6">
      <h1 className="font-bold text-2xl text-gray-900">班级选择组件测试</h1>

      {/* 单选模式测试 */}
      <div className="space-y-4">
        <h2 className="font-semibold text-gray-800 text-lg">单选模式</h2>
        <div className="rounded-lg border border-gray-200 p-4">
          <ClassPickerWidget
            multiple={false}
            onChange={(value) => setSingleValue(value as ClassInfo)}
            placeholder="请选择一个班级"
            value={singleValue || undefined}
          />
        </div>
        <div className="text-gray-600 text-sm">
          <strong>选中值：</strong>
          <pre className="mt-2 rounded bg-gray-100 p-2 text-xs">
            {JSON.stringify(singleValue, null, 2)}
          </pre>
        </div>
      </div>

      {/* 多选模式测试 */}
      <div className="space-y-4">
        <h2 className="font-semibold text-gray-800 text-lg">多选模式</h2>
        <div className="rounded-lg border border-gray-200 p-4">
          <ClassPickerWidget
            multiple={true}
            onChange={(value) => setMultipleValue(value as ClassInfo[])}
            placeholder="请选择班级"
            value={multipleValue}
          />
        </div>
        <div className="text-gray-600 text-sm">
          <strong>选中值：</strong>
          <pre className="mt-2 rounded bg-gray-100 p-2 text-xs">
            {JSON.stringify(multipleValue, null, 2)}
          </pre>
        </div>
      </div>

      {/* 只读模式测试 */}
      <div className="space-y-4">
        <h2 className="font-semibold text-gray-800 text-lg">只读模式</h2>

        <div className="space-y-2">
          <h3 className="font-medium text-gray-700 text-md">单选只读</h3>
          <div className="rounded-lg border border-gray-200 p-4">
            <ClassPickerWidget
              multiple={false}
              readOnly={true}
              value={singleValue || undefined}
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium text-gray-700 text-md">多选只读</h3>
          <div className="rounded-lg border border-gray-200 p-4">
            <ClassPickerWidget
              multiple={true}
              readOnly={true}
              value={multipleValue}
            />
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-4">
        <button
          className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
          onClick={() => setSingleValue(null)}
          type="button"
        >
          清空单选
        </button>
        <button
          className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
          onClick={() => setMultipleValue([])}
          type="button"
        >
          清空多选
        </button>
      </div>
    </div>
  );
}
