import Image from 'next/image';
import React, { memo, useEffect, useState } from 'react';

import { batchGetUserInfo } from '@/api/common';
import { defaultAvatar } from '@/constant/config';
import { useWorkflowStore } from '@/store/useWorkflowStore';

type Props = {
  userId: string;
  withBackground?: boolean;
};

function User({ userId, withBackground }: Props) {
  if (!userId) {
    return null;
  }
  const [userInfo, setUserInfo] = useState<any>({
    name: '',
    avatar: '',
  });
  const users = useWorkflowStore((state) => state.users);
  const getUser = useWorkflowStore((state) => state.getUser);
  const addUser = useWorkflowStore((state) => state.addUser);

  useEffect(() => {
    console.log('🚀 ~ file: User.tsx:22 ~ users:', users);
    const user = users[userId];
    console.log('🚀 ~ file: User.tsx:27 ~ user:', user);
    if (user) {
      setUserInfo(user);
    } else {
      // 如果用户信息不存在，从接口获取并保存到 Zustand 中
      batchGetUserInfo({ staffIds: [userId] }).then((res: any) => {
        if (Array.isArray(res.list) && res.list.length) {
          addUser(res.list[0]);
          setUserInfo(res.list[0]);
        }
      });
    }
  }, []);

  if (withBackground) {
    return (
      <div className="relative flex h-[60px] items-center rounded-[30px] bg-slate-100 pr-3 pl-[8px]">
        <Image
          alt=""
          className="size-[48px] rounded-[24px] object-cover"
          height="0"
          sizes="24px"
          src={userInfo.avatar || defaultAvatar}
          width="0"
        />
        <div>
          <span className="ml-2 text-sm">{userInfo.name}</span>
        </div>
        {userInfo.isLeave === 1 && (
          <span className="-translate-y-1/2 absolute top-0 right-0 translate-x-1/2 rounded-full bg-rose-500 p-1 font-medium text-[12px] text-white leading-none">
            离职
          </span>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <Image
        alt=""
        className="mr-2 size-10 object-cover"
        height="0"
        sizes="100px"
        src={userInfo.avatar || defaultAvatar}
        width="0"
      />
      <span className="text-base">{userInfo.name}</span>
    </div>
  );
}

export default memo(User);
