'use client';

import { Button, NavBar } from 'antd-mobile';
import React, { useState } from 'react';
import StudentPickerWidget from '../create/components/widgets/StudentPicker';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
}

export default function TestStudentPicker() {
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [readOnly, setReadOnly] = useState(false);

  const handleStudentChange = (students: Student[]) => {
    console.log('Selected students:', students);
    setSelectedStudents(students);
  };

  const toggleReadOnly = () => {
    setReadOnly(!readOnly);
  };

  const clearSelection = () => {
    setSelectedStudents([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar>学生选择器测试</NavBar>

      <div className="space-y-4 p-4">
        <div className="rounded-lg bg-white p-4">
          <h3 className="mb-4 font-semibold text-lg">学生选择器组件测试</h3>

          <div className="mb-4">
            <StudentPickerWidget
              onChange={handleStudentChange}
              placeholder="请选择参与工作流程的学生"
              readOnly={readOnly}
              value={selectedStudents}
            />
          </div>

          <div className="mb-4 flex gap-2">
            <Button color="primary" onClick={toggleReadOnly} size="small">
              {readOnly ? '切换到编辑模式' : '切换到只读模式'}
            </Button>
            <Button color="danger" onClick={clearSelection} size="small">
              清空选择
            </Button>
          </div>

          <div className="rounded bg-gray-100 p-3">
            <h4 className="mb-2 font-medium">当前选择的学生：</h4>
            <pre className="overflow-auto text-sm">
              {JSON.stringify(selectedStudents, null, 2)}
            </pre>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4">
          <h3 className="mb-4 font-semibold text-lg">组件状态</h3>
          <div className="space-y-2 text-sm">
            <div>模式: {readOnly ? '只读' : '编辑'}</div>
            <div>已选择学生数量: {selectedStudents.length}</div>
            <div>组件状态: 正常</div>
          </div>
        </div>
      </div>
    </div>
  );
}
