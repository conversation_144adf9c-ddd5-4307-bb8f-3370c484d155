'use client';

import { Button } from 'antd-mobile';
import React, { useState } from 'react';
import TeacherPicker from '../create/components/widgets/TeacherPicker';

interface Teacher {
  id: string;
  name: string;
  avatar: string;
  type: 'user';
}

export default function TestTeacherPicker() {
  const [selectedTeachers, setSelectedTeachers] = useState<Teacher[]>([]);

  const handleTeachersChange = (teachers: Teacher[]) => {
    setSelectedTeachers(teachers);
    console.log('Selected teachers:', teachers);
  };

  const clearSelection = () => {
    setSelectedTeachers([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="mb-4 rounded-lg bg-white p-4">
        <h1 className="mb-4 font-bold text-lg">TeacherPicker 组件测试</h1>

        <div className="mb-4">
          <h2 className="mb-2 font-semibold text-base">选择老师：</h2>
          <TeacherPicker
            onChange={handleTeachersChange}
            placeholder="请选择老师"
            value={selectedTeachers}
          />
        </div>

        <div className="mb-4">
          <h2 className="mb-2 font-semibold text-base">已选择的老师：</h2>
          {selectedTeachers.length > 0 ? (
            <div className="space-y-2">
              {selectedTeachers.map((teacher) => (
                <div
                  className="flex items-center space-x-2 rounded bg-gray-100 p-2"
                  key={teacher.id}
                >
                  <span className="font-medium">{teacher.name}</span>
                  <span className="text-gray-500 text-sm">
                    ID: {teacher.id}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">暂未选择老师</p>
          )}
        </div>

        <div className="mb-4">
          <h2 className="mb-2 font-semibold text-base">只读模式：</h2>
          <TeacherPicker readOnly={true} value={selectedTeachers} />
        </div>

        <div className="flex space-x-2">
          <Button
            color="danger"
            disabled={selectedTeachers.length === 0}
            onClick={clearSelection}
          >
            清空选择
          </Button>
          <Button
            color="primary"
            onClick={() => console.log('当前选择:', selectedTeachers)}
          >
            打印选择结果
          </Button>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4">
        <h2 className="mb-2 font-semibold text-base">JSON 输出：</h2>
        <pre className="overflow-auto rounded bg-gray-100 p-2 text-xs">
          {JSON.stringify(selectedTeachers, null, 2)}
        </pre>
      </div>
    </div>
  );
}
