import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

import { defaultAvatar } from '@/constant/config';

import { modeType } from '../../../create/components/data';
import Style from './index.module.scss';

function isEmpty(obj: any) {
  return (
    obj !== null && typeof obj === 'object' && Object.keys(obj).length === 0
  );
}

const NodeBox = ({ currentData }: any) => {
  console.log('🚀 ~ file: nodeBox.tsx:32 ~ currentData:', currentData);

  const renderUser = (prop) => {
    if (Array.isArray(prop?.assignedUser)) {
      if (prop.assignedUser.length) {
        return prop.assignedUser.map((item: any) => (
          <div
            className="mr-2 mb-2 flex h-[60px] items-center rounded-[30px] bg-slate-100 pr-3 pl-[8px]"
            key={item.id}
          >
            <Image
              alt=""
              className="size-[48px] rounded-[24px] object-cover"
              height="0"
              sizes="24px"
              src={item.avatar || defaultAvatar}
              width="0"
            />
            <span className="ml-2 text-sm">{item.name}</span>
          </div>
        ));
      }
    } else {
      return (
        <div className="mr-2 mb-2 flex h-[60px] items-center rounded-[30px] bg-slate-100 pr-3 pl-[8px]">
          <span className="ml-2 text-sm">全员</span>
        </div>
      );
    }
  };

  // 渲染一般节点(非路由节点)
  const renderNormalNode = (normalNodeData: any) => {
    console.log('🚀 ~ file: index.tsx:46 ~ normalNodeData:', normalNodeData);
    return (
      <div className="relative flex items-baseline gap-8 pb-5">
        <div
          className={clsx(
            'before:absolute before:left-[18px] before:h-full before:bg-neutral-200',
            normalNodeData.children === null ? 'before:w-0' : 'before:w-[4px]'
          )}
        >
          <div className="absolute top-0 z-10 flex size-[40px] items-center justify-center overflow-hidden rounded-full border-8 border-white bg-neutral-300" />
        </div>
        <div>
          <div className="mb-2 text-black leading-tight">
            {normalNodeData.name}
          </div>
          <div className="text-gray-500 text-xs">
            {normalNodeData.props.mode
              ? modeType[normalNodeData.props.mode] || ''
              : null}
          </div>
          <div className="mt-3 flex flex-wrap">
            {renderUser(normalNodeData.props)}
          </div>
        </div>
      </div>
    );
  };

  // 渲染遮盖线条
  const renderLineDom = (index: number) => {
    // 如果是渲染的第一个节点，则遮盖住左上与左下两条边线
    if (index === 0) {
      return (
        <>
          <div className={Style['top-left-cover-line']} />
          <div className={Style['bottom-left-cover-line']} />
        </>
      );
    }
    // 如果渲染的是最后一个节点，则遮盖住右上与右下两条边线
    if (index === currentData.branchs.length - 1) {
      return (
        <>
          <div className={Style['top-right-cover-line']} />
          <div className={Style['bottom-right-cover-line']} />
        </>
      );
    }
    return null;
  };

  // 渲染路由节点
  const renderRouteNode = () => (
    <div className={Style['route-node-wrap']}>
      {/* 条件分支节点 */}
      <div className={Style['branch-wrap']}>
        {/* 渲染多列条件节点 */}
        {currentData.branchs.map((item: any, index: number) => (
          // 路由节点整个包裹dom元素
          <div className={Style['col-box']} key={item.id}>
            {/* 条件节点 */}
            <div className={Style['condition-node']}>
              {/* 每一个条件 */}
              <div className={Style['condition-node-card']}>
                {/* 条件盒子里面的节点 */}
                {renderNormalNode(item)}
              </div>
            </div>
            {/* 条件节后面可以是任意节点，所以自调用本组件 */}
            {item.children ? <NodeBox currentData={item.children} /> : null}
            {/* 渲染遮盖线条，需要遮盖住四个角的边线 */}
            {renderLineDom(index)}
          </div>
        ))}
      </div>
      {/* 添加子节点 */}
    </div>
  );
  if (currentData === null) return null;

  return (
    <>
      {/* 渲染一般节点或者路由节点 */}
      {currentData.type === 'CONDITIONS'
        ? renderRouteNode()
        : renderNormalNode(currentData)}
      {/* 如果有子节点，继续递归调用本组件 */}
      {isEmpty(currentData.children) ? null : (
        <NodeBox currentData={currentData.children} />
      )}
    </>
  );
};

export default NodeBox;
