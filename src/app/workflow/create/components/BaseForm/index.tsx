import { ActionSheet, Form, Input, Toast } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import type { FormInstance } from 'antd-mobile/es/components/form';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

import { getGroup } from '@/api/approval';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { debounce } from '@/utils';
import Emitter from '@/utils/emitter';

import type { Ref } from '../IconPicker';
import IconPicker from '../IconPicker';

const icons = Array.from({ length: 20 }).map(
  (_, index) =>
    `https://unicorn-media.ancda.com/production/app/workflow/icon/${
      index + 1
    }.png`
);

export default function BaseForm() {
  const baseForm = useWorkflowStore((state) => state.baseForm);
  const setBaseForm = useWorkflowStore((state) => state.setBaseForm);

  const [form] = Form.useForm();
  const formRef = useRef<FormInstance>(null);
  const iconPickerRef = useRef<Ref>(null);

  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actions, setActions] = useState<Action[]>([]);
  const [groupName, setGroupName] = useState<string>('');

  const onChange = debounce((value: any) => {
    setBaseForm({ ...baseForm, name: value });
  }, 500);

  useEffect(() => {
    form.setFieldsValue(baseForm);
  }, [baseForm]);

  useEffect(() => {
    Emitter.on('validateFields', validateFields);
    return () => {
      Emitter.off('validateFields', validateFields);
    };
  }, []);

  // 初始化加载分组数据
  useEffect(() => {
    if (actions.length === 0) {
      getGroup().then((res: any) => {
        if (Array.isArray(res.list)) {
          const actionList = res.list.map((item: any) => ({
            key: item.groupId,
            text: item.groupName,
          }));
          setActions(actionList);
        }
      });
    }
  }, []);

  // 根据 groupId 更新显示的分组名称
  useEffect(() => {
    if (baseForm.groupId && actions.length > 0) {
      const action = actions.find((item) => item.key === baseForm.groupId);
      setGroupName(String(action?.text || ''));
    }
  }, [baseForm.groupId, actions]);

  const validateFields = () => {
    return new Promise((resolve, reject) => {
      form
        .validateFields()
        .then((values) => {
          resolve(values);
        })
        .catch((err) => {
          if (Array.isArray(err.errorFields)) {
            Toast.show({
              content: err.errorFields[0].errors[0],
            });
          }
          reject(err);
        });
    });
  };

  return (
    <>
      <Form
        form={form}
        layout="horizontal"
        mode="card"
        ref={formRef}
        // requiredMarkStyle="text-required"
      >
        <Form.Header>审批基础信息</Form.Header>
        <Form.Item
          className="border-none"
          label="名称"
          name="name"
          rules={[{ required: true }]}
        >
          <Input
            maxLength={20}
            minLength={2}
            onChange={onChange}
            // defaultValue={baseForm.name}
            // value={baseForm.name}
            placeholder="请输入审批名称"
            style={{ '--text-align': 'right' }}
          />
        </Form.Item>
        <Form.Item
          arrow
          clickable
          extra={
            <div>
              {baseForm.iconUrl ? (
                <Image
                  alt=""
                  className="size-[64px] rounded object-cover"
                  height="0"
                  sizes="64px"
                  src={baseForm.iconUrl}
                  width="0"
                />
              ) : null}
            </div>
          }
          label="图标"
          name="iconUrl"
          onClick={() => {
            iconPickerRef.current?.toggle();
          }}
          rules={[{ required: true, message: '请选择一个图标' }]}
        />
        <Form.Item
          arrow
          extra={<div>{groupName}</div>}
          label="分组"
          name="groupId"
          onClick={() => {
            setActionSheetVisible(true);
          }}
          rules={[{ required: true, message: '请选择分组' }]}
        />
      </Form>
      <IconPicker
        icons={icons}
        onSelect={(value) => {
          setBaseForm({ ...baseForm, iconUrl: value });
        }}
        ref={iconPickerRef}
      />
      <ActionSheet
        actions={actions}
        cancelText="取消"
        extra="请选择"
        onAction={(action) => {
          if (action.key) {
            setBaseForm({ ...baseForm, groupId: String(action.key) });
          }
          setActionSheetVisible(false);
        }}
        onClose={() => setActionSheetVisible(false)}
        visible={actionSheetVisible}
      />
    </>
  );
}
