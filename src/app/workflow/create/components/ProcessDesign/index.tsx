import { memo } from 'react';

import { useWorkflowStore } from '@/store/useWorkflowStore';

import NodeBox from './components/nodeBox';
import Style from './index.module.scss';

function Index() {
  const process = useWorkflowStore((state) => state.process);

  return (
    <div className="overflow-scroll bg-[#F7F9FF] px-4 pb-2">
      <div className={Style['page-wrap']}>
        <div
          className={Style['workflow-wrap']}
          style={{ transform: `scale(${100 / 100})` }}
        >
          {typeof process === 'object' && Object.keys(process).length > 0 && (
            <NodeBox currentData={process} />
          )}
          <div className="rounded-md bg-white px-4 py-2 text-base shadow-sm">
            流程结束
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(Index);
