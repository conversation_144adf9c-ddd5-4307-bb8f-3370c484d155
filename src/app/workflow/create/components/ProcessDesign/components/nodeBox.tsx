'use client';

import { Toast } from 'antd-mobile';
import clsx from 'clsx';
import React, { useRef } from 'react';

import {
  AiOutlineFileDone,
  PiCaretRight,
  PiPaperPlaneTiltFill,
  PiStampFill,
  PiTrash,
  PiUserFill,
} from '@/components/Icons';
import { useWorkflowStore } from '@/store/useWorkflowStore';

import AddNode from './addNode';
import Style from './index.module.scss';
import type { Ref } from './NodeSetting';
import NodeSetting from './NodeSetting';

function isEmpty(obj: any) {
  return (
    obj !== null && typeof obj === 'object' && Object.keys(obj).length === 0
  );
}

// 至少有一个审批节点
function checkEmptyApprovalNode(node: any, value: string) {
  if (node.id !== value && node.type === 'APPROVAL') {
    return true;
  }

  if (node.children && checkEmptyApprovalNode(node.children, value)) {
    return true;
  }

  return false;
}

const NodeBox = ({ currentData }: any) => {
  console.log('🚀 ~ file: nodeBox.tsx:32 ~ currentData:', currentData);
  const process = useWorkflowStore((state) => state.process);

  const setProcess = useWorkflowStore((state) => state.setProcess);

  const normalid = useRef(0);
  const nodeSettingRef = useRef<Ref>(null);

  const renderUser = (node) => {
    const { props } = node;
    let users = '请选择';
    if (Array.isArray(props?.assignedUser) && props.assignedUser.length) {
      users = props.assignedUser.map((item) => item.name).join(',');
    } else if (node.type === 'ROOT') {
      users = '全员';
    }

    return (
      <div className="flex items-center justify-between">
        <div>{users}</div>
        <div>
          <PiCaretRight color="#666" size="12px" />
        </div>
      </div>
    );
  };

  // 找到普通节点并把它删除掉(非条件节点)
  const removeNormalNodeFn = (arr: any, id: any) => {
    console.log('🚀 ~ file: nodeBox.tsx:94 ~ arr:', arr);
    arr.forEach((item: any) => {
      // 找到了这个节点就开始添加新的子节点
      // item的子节点id匹配上了，那么就是要删除这个子节点，item的child指向子节点的子节点
      if (item.children && item.children.id === id) {
        item.children = item.children.children;
      } else if (item.children) {
        // 当前节点不是目标节点，但是有子节点，则继续遍历子节点
        removeNormalNodeFn([item.children], id);
      }
      // 当前节点不是目标节点，但是有条件节点，则继续遍历条件节点
      if (
        (item.children && item.children.id) !== id &&
        item.branchs &&
        item.branchs.length > 0
      ) {
        removeNormalNodeFn(item.branchs, id);
      }
    });
    return arr;
  };

  // 找到条件节点把它删除
  const removeConditionNodeFn = (arr: any, id: any) => {
    arr.forEach((item: any) => {
      let flag = true;
      if (item.branchs && item.branchs.length > 0 && flag) {
        // 在条件列表中找到目标条件节点的索引
        const findIndex = item.branchs.findIndex(
          (conditionItem: any) => conditionItem.id === id
        );
        // 如果找到了这个节点，则不需要再往下遍历了
        if (findIndex !== -1) {
          flag = false;
          // 如果条件节点只有2个，那么删除掉一个，则要删除的不光是条件节点，而是整个路由节点
          if (item.branchs.length === 2) {
            normalid.current = item.id;
          } else {
            // 条件有多个，直接删除这一个
            item.branchs.splice(findIndex, 1);
          }
        }
      }
      if (item.children && flag) {
        removeConditionNodeFn([item.children], id);
      }
      // 需要删除的条件节点，可能在条件节点下
      if (item.branchs?.length && flag) {
        removeConditionNodeFn(item.branchs, id);
      }
    });
    return arr;
  };

  // 点击添加条件按钮
  const onAddCondition = () => {
    const originalData = JSON.parse(JSON.stringify(process));

    const { id } = currentData;
    const addConditionFn = (arr: any, id: number) => {
      arr.forEach((item: any) => {
        // 找到对应路由节点
        console.log(item.id);
        if (item.id === id) {
          return item.branchs.push({
            name: '条件N',
            type: 'CONDITIONS',
            id: +new Date(),
            conditionList: [],
            nodeUserList: [],
            branchs: [],
            children: null,
          });
        }
        if (item.children) {
          addConditionFn([item.children], id);
        }
        // 条件节点下可能直接还是条件节点
        if (item.branchs?.length) {
          addConditionFn(item.branchs, id);
        }
      });
      return arr;
    };
    const [result] = addConditionFn([originalData], id);
    // 刷新state
    setProcess(JSON.parse(JSON.stringify(result)));
  };

  // 删除节点
  const onDeleteCard = (normalNodeData: any) => {
    const { id, type } = normalNodeData;
    if (type === 'APPROVAL' && !checkEmptyApprovalNode(process, id)) {
      Toast.show('至少保留一个审批节点');
      return;
    }
    const originalData = JSON.parse(JSON.stringify(process));

    // 最终需要渲染的结果数据
    let result = {};
    // 如果是删除的是条件节点，则需要从它的父节点的conditionList中删除它
    // 并且判断如果只有2个条件，删除了一个，那么父节点的conditionList直接置空
    if (type === 'CONDITIONS') {
      // 删除条件节点并得到最终删除后的数据
      [result] = removeConditionNodeFn([originalData], id);
      // normalid.current 如果有值，则说明需要删除的是路由节点(没有值则说明是删除的是路由节点下的某一个条件节点)
      if (normalid.current) {
        [result] = removeNormalNodeFn([originalData], normalid.current);
        normalid.current = 0;
      }
    } else {
      // 删除的是普通节点并得到最终删除后的数据
      [result] = removeNormalNodeFn([originalData], id);
    }
    // 刷新state
    setProcess(JSON.parse(JSON.stringify(result)));
  };

  // 渲染一般节点(非路由节点)
  const renderNormalNode = (normalNodeData: any) => {
    const icon = (iconName: string) =>
      React.createElement(iconName, { size: 14, color: '#FFF' });

    const renderIcon = (type: string) => {
      const data: any = {
        ROOT: {
          // 发起人
          color: 'bg-slate-400',
          iconName: PiUserFill,
        },
        APPROVAL: {
          color: 'bg-yellow-500',
          iconName: PiStampFill,
        },
        CC: {
          color: 'bg-teal-400',
          iconName: PiPaperPlaneTiltFill,
        },
        TASK: {
          color: 'bg-green-500',
          iconName: AiOutlineFileDone,
        },
      };
      if (type in data) {
        return (
          <div
            className={clsx(data[type].color, 'center h-6 w-6 rounded-full')}
          >
            {icon(data[type].iconName)}
          </div>
        );
      }
    };

    return (
      <div className={clsx(Style['node-wrap'], 'start')}>
        <div
          className={clsx(
            Style['node-card'],
            normalNodeData.type === 'ROOT' ? Style['start-node'] : null
          )}
          onClick={() => {
            setTimeout(() => {}, 500);
            nodeSettingRef.current?.initData(normalNodeData);
            nodeSettingRef.current?.toggle();
          }}
        >
          <div className="mb-3 flex justify-between">
            <div className="flex items-center">
              {renderIcon(normalNodeData.type)}
              <span className="ml-2 text-base">{normalNodeData.name}</span>
            </div>
            {normalNodeData.type !== 'ROOT' ? (
              <div
                onClick={(event) => {
                  event.stopPropagation();
                  onDeleteCard(normalNodeData);
                }}
              >
                <PiTrash color="#666" fontSize={18} />
              </div>
            ) : null}
          </div>
          <div className="rounded-md bg-gray-50 p-3 ">
            {renderUser(normalNodeData)}
          </div>
        </div>
        {/* 添加子节点 */}
        <AddNode parentNodeData={normalNodeData} />
      </div>
    );
  };

  // 渲染遮盖线条
  const renderLineDom = (index: number) => {
    // 如果是渲染的第一个节点，则遮盖住左上与左下两条边线
    if (index === 0) {
      return (
        <>
          <div className={Style['top-left-cover-line']} />
          <div className={Style['bottom-left-cover-line']} />
        </>
      );
    }
    // 如果渲染的是最后一个节点，则遮盖住右上与右下两条边线
    if (index === currentData.branchs.length - 1) {
      return (
        <>
          <div className={Style['top-right-cover-line']} />
          <div className={Style['bottom-right-cover-line']} />
        </>
      );
    }
    return null;
  };

  // 渲染路由节点
  const renderRouteNode = () => (
    <div className={Style['route-node-wrap']}>
      {/* 条件分支节点 */}
      <div className={Style['branch-wrap']}>
        <div
          className={Style['add-branch-btn']}
          onClick={onAddCondition}
          role="none"
        >
          添加条件
        </div>
        {/* 渲染多列条件节点 */}
        {currentData.branchs.map((item: any, index: number) => (
          // 路由节点整个包裹dom元素
          <div className={Style['col-box']} key={item.id}>
            {/* 条件节点 */}
            <div className={Style['condition-node']}>
              {/* 每一个条件 */}
              <div className={Style['condition-node-card']}>
                {/* 条件盒子里面的节点 */}
                {renderNormalNode(item)}
              </div>
            </div>
            {/* 条件节后面可以是任意节点，所以自调用本组件 */}
            {item.children ? <NodeBox currentData={item.children} /> : null}
            {/* 渲染遮盖线条，需要遮盖住四个角的边线 */}
            {renderLineDom(index)}
          </div>
        ))}
      </div>
      {/* 添加子节点 */}
      <AddNode parentNodeData={currentData} />
    </div>
  );
  if (currentData === null) return null;

  return (
    <>
      {/* 渲染一般节点或者路由节点 */}
      {currentData.type === 'CONDITIONS'
        ? renderRouteNode()
        : renderNormalNode(currentData)}
      {/* 如果有子节点，继续递归调用本组件 */}
      {isEmpty(currentData.children) ? null : (
        <NodeBox currentData={currentData.children} />
      )}
      <NodeSetting ref={nodeSettingRef} />
    </>
  );
};

export default NodeBox;
