'use client';

import {
  ActionSheet,
  Button,
  Form,
  Input,
  NavBar,
  Popup,
  Switch,
} from 'antd-mobile';
import type {
  Action,
  ActionSheetShowHandler,
} from 'antd-mobile/es/components/action-sheet';
import type { FormInstance } from 'antd-mobile/es/components/form';
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useImmer } from 'use-immer';

import type { TeacherPickerRef } from '@/components/TeacherPicker';
import TeacherPicker from '@/components/TeacherPicker';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { debounce } from '@/utils';

import { modeType, typeMap } from '../../data';

export interface Ref {
  toggle(): void;
  initData: (data: NodeType) => void;
}

type NodeType = {
  id: string;
  type: string;
  name: string;
  props: {
    assignedUser: any[];
    mode: 'AND' | 'OR' | 'NEXT'; // 多人审批方式
    sign: boolean; // 审批同意时是否需要签字
  };
  children?: NodeType;
};

interface Props {
  currentData?: NodeType;
}

const NodeSetting = forwardRef<Ref, Props>(function NodeSetting(props, ref) {
  console.log('🚀 ~ file: nodeSetting.tsx:35 ~ props:', props);

  const process = useWorkflowStore((state) => state.process);
  const setProcess = useWorkflowStore((state) => state.setProcess);
  const [currentData, setCurrentData] = useImmer<NodeType>({
    id: '',
    type: '',
    name: '',
    props: {
      assignedUser: [],
      mode: 'NEXT', // 多人审批方式
      sign: false, // 审批同意时是否需要签字
    },
  });
  const [popupVisible, setPopupVisible] = useState(false);
  const [visible, setVisible] = useState(false);

  const [form] = Form.useForm();

  const handler = useRef<ActionSheetShowHandler>(undefined);
  const formRef = useRef<FormInstance>(null);
  const teacherPicker = useRef<TeacherPickerRef>(null);

  const actions: Action[] = [
    {
      text: '全员',
      key: '1',
      onClick: () => {
        setCurrentData((draft) => {
          draft.props = {
            ...draft.props,
            assignedUser: [],
          };
        });
        setVisible(false);
      },
    },
    {
      text: '指定部门或成员',
      key: '2',
      onClick: () => {
        setVisible(false);
        teacherPicker.current?.toggle();
        teacherPicker.current?.initData(
          currentData.props?.assignedUser || [],
          currentData.type === 'ROOT'
        );
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    toggle() {
      setPopupVisible((prev) => !prev);
    },
    initData(data) {
      console.log('🚀 ~ file: nodeSetting.tsx:60 ~ data:', data);
      setCurrentData(data);
      form.setFieldsValue(data);
    },
  }));

  const title = () => {
    const { type } = currentData as { type: keyof typeof typeMap };
    return typeMap[type] || '';
  };

  const approvalActions: Action[] = Object.keys(modeType).map((key) => {
    return {
      text: modeType[key] || '',
      key,
      onClick: () => setApprovalType(key),
    };
  });

  const setApprovalType = (type) => {
    console.log('🚀 ~ file: nodeSetting.tsx:25 ~ type:', type);
    setCurrentData((draft) => {
      draft.props.mode = type;
    });
    handler.current?.close();
  };

  const onFinish = (values: any) => {
    const { id } = currentData;
    const addConditionFn = (arr: any, id: string) => {
      for (let index = 0; index < arr.length; index++) {
        const item = arr[index];
        if (item.id === id) {
          console.log('🚀 ~ file: nodeSetting.tsx:112 ~ item:', item);
          arr[index].id = currentData.id;
          arr[index].name = currentData.name;
          arr[index].type = currentData.type;
          arr[index].props = currentData.props;
          arr[index].children = currentData.children;
          break;
        }
        if (item.children) {
          addConditionFn([item.children], id);
        }
      }
      console.log('🚀 ~ file: nodeSetting.tsx:140 ~ arr:', arr);
      return arr;
    };

    const originalData = JSON.parse(JSON.stringify(process));
    const [result] = addConditionFn([originalData], id);
    console.log('🚀 ~ file: nodeSetting.tsx:120 ~ result:', result);
    // 刷新state
    setProcess(JSON.parse(JSON.stringify(result)));
    setPopupVisible(false);
  };

  const renderUser = () => {
    if (
      Array.isArray(currentData.props?.assignedUser) &&
      currentData.props?.assignedUser.length
    ) {
      return currentData.props?.assignedUser.map((item) => item.name).join(',');
    }
    if (currentData.type === 'ROOT') {
      return '全员';
    }

    return '请选择';
  };

  const onChange = debounce((value: any) => {
    setCurrentData((draft) => {
      draft.name = value;
    });
  }, 500);

  return (
    <Popup
      bodyStyle={{ width: '100vw', backgroundColor: '#F7F9FF' }}
      onClose={() => {
        setPopupVisible(false);
      }}
      position="right"
      visible={popupVisible}
    >
      <div>
        <NavBar
          className="bg-white"
          onBack={() => {
            setPopupVisible(false);
          }}
        >
          {title()}
        </NavBar>
        <Form
          footer={
            <div className="flex justify-evenly">
              <Button
                className="w-1/3"
                color="primary"
                fill="outline"
                onClick={() => {
                  setPopupVisible(false);
                }}
              >
                取消
              </Button>
              <Button className="w-1/3" color="primary" type="submit">
                确定
              </Button>
            </div>
          }
          form={form}
          layout="horizontal"
          mode="card"
          onFinish={onFinish}
          ref={formRef}
          // requiredMarkStyle="text-required"
          style={{
            '--prefix-width': '150px',
          }}
        >
          <Form.Item
            className="border-none"
            label="节点名称"
            name="name"
            rules={[{ required: true }]}
          >
            <Input
              onChange={onChange}
              placeholder="请输入审批名称"
              style={{ '--text-align': 'right' }}
            />
          </Form.Item>
          <Form.Item
            arrow
            clickable
            extra={<div>{renderUser()}</div>}
            label="添加成员"
            name="icon"
            onClick={() => {
              if (currentData.type === 'ROOT') {
                setVisible(true);
              } else {
                teacherPicker.current?.toggle();
                teacherPicker.current?.initData(
                  currentData.props?.assignedUser || [],
                  currentData.type === 'ROOT'
                );
              }
            }}
            shouldUpdate={(prevValues, curValues) =>
              prevValues.icon !== curValues.icon
            }
            style={{
              '--prefix-width': '80px',
            }}
          />
          {(currentData.type === 'APPROVAL' || currentData.type === 'TASK') && (
            <Form.Item
              arrow
              extra={<div>{modeType[currentData.props?.mode] || ''}</div>}
              label="多人审批方式"
              name="type"
              onClick={() => {
                handler.current = ActionSheet.show({
                  actions: approvalActions,
                  cancelText: '取消',
                });
              }}
            />
          )}
          {(currentData.type === 'APPROVAL' || currentData.type === 'TASK') && (
            <Form.Item
              childElementPosition="right"
              label="是否需要签字"
              name="delivery"
            >
              <Switch
                checked={currentData.props?.sign}
                onChange={(value) => {
                  setCurrentData((draft) => {
                    if (draft.props) {
                      draft.props.sign = value;
                    }
                  });
                }}
              />
            </Form.Item>
          )}
        </Form>
        <TeacherPicker
          onConfirm={(value) => {
            console.log('🚀 ~ file: index.tsx:97 ~ value:', value);
            setCurrentData((draft) => {
              if (draft.props) {
                draft.props.assignedUser = value;
              }
            });
          }}
          ref={teacherPicker}
          title={title()}
        />
      </div>
      <ActionSheet
        actions={actions}
        cancelText="取消"
        onClose={() => setVisible(false)}
        visible={visible}
      />
    </Popup>
  );
});

export default NodeSetting;
