# 学生选择器组件 (StudentPicker Widget)

这是一个用于 workflow 表单系统的自定义学生选择器组件，专门用于任务分配和工作流程中的学生选择功能。

## 功能特性

- ✅ 支持班级和学生的级联选择
- ✅ 支持多选学生功能
- ✅ 学生头像显示
- ✅ 只读模式支持
- ✅ 响应式设计
- ✅ 与 form-render 完全兼容

## 使用方法

### 1. 在表单设计器中使用

在 workflow 创建页面的组件库中，可以找到"选择学生"组件，点击即可添加到表单中。

### 2. 在代码中使用

```tsx
import StudentPickerWidget from './StudentPicker';

// 基本使用
<StudentPickerWidget
  value={selectedStudents}
  onChange={handleStudentChange}
  placeholder="请选择参与的学生"
/>

// 只读模式
<StudentPickerWidget
  value={selectedStudents}
  readOnly={true}
/>
```

## Props 接口

```typescript
interface StudentPickerWidgetProps {
  value?: Student[];                    // 当前选中的学生数组
  onChange?: (value: Student[]) => void; // 选择变化回调
  readOnly?: boolean;                   // 是否只读模式
  placeholder?: string;                 // 占位符文本
  schema?: Record<string, unknown>;     // 表单 schema
  addons?: Record<string, unknown>;     // 附加配置
}

interface Student {
  studentId: string;    // 学生ID
  studentName: string;  // 学生姓名
  avatar: string;       // 头像URL
  gender: number;       // 性别 (1: 男, 2: 女)
  classId?: string;     // 班级ID
}
```

## 组件配置

在 `data.ts` 中的配置：

```typescript
studentPicker: {
  title: '选择学生',
  description: '用于任务分配，选择参与工作流程的学生',
  type: 'array',
  widget: 'studentPicker',
  readOnlyWidget: 'studentPicker',
  layout: 'column',
  printable: true,
  required: false,
  props: {
    multiple: true,
    maxCount: 50,
    placeholder: '请选择参与的学生'
  }
}
```

## 依赖组件

- `StudentAvatar`: 学生头像显示组件
- `CascadePicker`: antd-mobile 级联选择器
- `Toast`: antd-mobile 提示组件

## API 依赖

- `getClassList()`: 获取班级列表
- `getStudentListByClass(classId)`: 根据班级ID获取学生列表

## 测试页面

访问 `/workflow/test-student-picker` 可以测试组件的各种功能。

## 注意事项

1. 组件会自动加载班级和学生数据
2. 支持学生头像的错误处理和默认头像
3. 在只读模式下会显示已选择的学生信息
4. 组件使用了 React.memo 进行性能优化

## 更新日志

- v1.0.0: 初始版本，支持基本的学生选择功能
