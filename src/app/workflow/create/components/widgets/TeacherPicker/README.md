# TeacherPicker Widget 组件

## 📋 概述
TeacherPicker 是一个用于工作流表单的老师选择组件，基于现有的 `src/components/TeacherPicker/index.tsx` 改造，适配 form-render-mobile 的 widget 规范。

## 🎯 功能特性
- ✅ 多选老师支持
- ✅ 实时搜索功能
- ✅ 横向显示已选老师
- ✅ 头像+姓名+删除按钮显示
- ✅ 移动端适配
- ✅ 只显示人员，不支持部门选择

## 📁 文件结构
```
src/app/workflow/create/components/widgets/TeacherPicker/
├── index.tsx                 # 主组件文件
├── components/
│   └── TeacherAvatar.tsx    # 老师头像显示组件
└── README.md                # 组件文档
```

## 🔧 技术实现

### 组件接口
```typescript
interface Teacher {
  id: string;
  name: string;
  avatar: string;
  type: 'user';
}

interface TeacherPickerWidgetProps {
  value?: Teacher[];
  onChange?: (value: Teacher[]) => void;
  readOnly?: boolean;
  placeholder?: string;
  schema?: Record<string, unknown>;
  addons?: Record<string, unknown>;
}
```

### 核心功能

#### 1. TeacherAvatar 组件
- 参考 `StudentAvatar.tsx` 实现
- 支持 sm/md/lg 三种尺寸
- 显示老师头像、姓名
- 右上角删除按钮
- 图片加载失败时显示默认头像

#### 2. 数据获取
- 使用 `getDeptUser` API 获取部门人员
- 使用 `searchTeacher` API 搜索老师
- 状态管理：已选老师列表、搜索关键词、弹窗显示状态

#### 3. UI 交互
- 主界面：横向显示已选老师 + 添加按钮
- 弹窗选择器：搜索栏 + 人员列表 + 已选显示
- 支持搜索和浏览两种选择方式

## 🎨 界面设计

### 主界面布局
```
[👤张老师] [👤李老师] [👤王老师] [➕]
```

### 选择器弹窗
```
┌─────────────────────────┐
│ 取消    选择老师    确定 │
├─────────────────────────┤
│ 🔍 [搜索框]    [搜索]   │
├─────────────────────────┤
│ 已选择的老师 (横向显示)  │
├─────────────────────────┤
│ 📍 面包屑导航           │
├─────────────────────────┤
│ ☑️ 张老师               │
│ ☑️ 李老师               │
│ ☐ 王老师               │
└─────────────────────────┘
```

## 🔄 数据流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as TeacherPicker组件
    participant A as API
    participant P as 父组件

    U->>C: 点击添加按钮
    C->>C: 打开弹窗
    C->>A: getDeptUser() 获取部门人员
    A-->>C: 返回人员列表
    C->>C: 渲染人员列表
    
    U->>C: 搜索老师
    C->>A: searchTeacher(keyword)
    A-->>C: 返回搜索结果
    C->>C: 显示搜索结果
    
    U->>C: 选择老师
    C->>C: 更新已选列表
    C->>C: 横向显示已选老师
    
    U->>C: 确认选择
    C->>P: onChange(selectedTeachers)
    C->>C: 关闭弹窗
```

## 📊 实现流程

```mermaid
graph TD
    A[用户点击添加按钮] --> B[打开选择器弹窗]
    B --> C{用户操作}
    C -->|搜索| D[调用searchTeacher API]
    C -->|浏览部门| E[调用getDeptUser API]
    C -->|选择老师| F[更新已选列表]
    D --> G[显示搜索结果]
    E --> H[显示部门人员]
    G --> F
    H --> F
    F --> I[横向显示已选老师]
    I --> J{用户确认}
    J -->|确定| K[关闭弹窗，触发onChange]
    J -->|取消| L[关闭弹窗，不保存]
    I --> M[点击删除按钮]
    M --> N[从已选列表移除]
    N --> I
```

## ✅ 验收标准

### 功能完整性
- [x] 支持多选老师
- [x] 搜索功能正常
- [x] 已选老师横向显示
- [x] 删除功能正常
- [x] 只显示人员，不显示部门选择

### 界面体验
- [x] 移动端适配良好
- [x] 交互流畅
- [x] 视觉风格统一

### 代码质量
- [x] TypeScript 类型完整
- [x] 组件可复用
- [x] 性能优化（memo、useMemo等）
- [x] 错误处理完善

## 🚀 使用示例

```tsx
import TeacherPicker from './TeacherPicker';

// 在 form-render-mobile 中使用
const schema = {
  type: 'object',
  properties: {
    teachers: {
      title: '选择老师',
      type: 'array',
      widget: 'teacherPicker',
      items: {
        type: 'object'
      }
    }
  }
};

// 直接使用
<TeacherPicker
  value={selectedTeachers}
  onChange={handleTeachersChange}
  placeholder="请选择老师"
/>
```

## 📝 开发注意事项

1. **API 依赖**：确保 `getDeptUser` 和 `searchTeacher` API 可用
2. **图片处理**：头像加载失败时使用默认头像
3. **性能优化**：使用 React.memo 和 useMemo 优化渲染
4. **错误处理**：网络请求失败时显示友好提示
5. **类型安全**：完整的 TypeScript 类型定义
