import { Dialog, ImageUploader, Toast } from 'antd-mobile';
import type { ImageUploadItem } from 'antd-mobile/es/components/image-uploader';
import { omit } from 'lodash-es';
import React, { useEffect, useState } from 'react';

import { upload } from '../../utils/upload';

type FileItem = ImageUploadItem & { type: 'image' | 'video' };

const maxSize = '10MB';

export default function Image(props: any) {
  const {
    onChange,
    value,
    max = 20,
    ...rest
  } = omit(props, ['addons', 'schema']);

  const readOnly = rest.readOnly;

  const [fileList, setFileList] = useState<FileItem[]>([]);

  useEffect(() => {
    if (Array.isArray(value)) {
      setFileList(value);
    }
  }, [value]);

  function beforeUpload(file: File) {
    if (file.size > 10 * 1024 * 1024) {
      Toast.show(`请选择小于 ${maxSize} 的图片`);
      return null;
    }
    return file;
  }

  return (
    <>
      <ImageUploader
        accept="image/*"
        beforeUpload={beforeUpload}
        deletable={!readOnly}
        maxCount={max}
        multiple
        onChange={(files: any) => {
          setFileList(files);
          onChange(files);
        }}
        onCountExceed={(exceed) => {
          Toast.show(`最多选择 ${max} 张图片，你多选了 ${exceed} 张`);
        }}
        onDelete={() => {
          return Dialog.confirm({
            content: '是否确认删除',
          });
        }}
        preview
        showUpload={!readOnly && fileList.length < max}
        upload={upload as any}
        value={fileList}
      />
      {!readOnly && (
        <div className="mt-3 text-sm text-stone-400">
          上限{max}个文件，最大{maxSize}/个
        </div>
      )}
    </>
  );
}
