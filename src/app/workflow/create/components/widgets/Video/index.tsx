import { Dialog, ImageUploader, Mask, Toast } from 'antd-mobile';
import type { ImageUploadItem } from 'antd-mobile/es/components/image-uploader';
import { omit } from 'lodash-es';
import React, { useEffect, useState } from 'react';

import { upload } from '../../utils/upload';
import VideoPlayer from '../../VideoPlayer';

type FileItem = ImageUploadItem & { type: 'image' | 'video' };

const maxSize = '100MB';

export default function Image(props: any) {
  const {
    onChange,
    value,
    max = 5,
    ...rest
  } = omit(props, ['addons', 'schema']);
  const readOnly = rest.readOnly;
  const [videoId, setVideoId] = useState<string>('');
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (Array.isArray(value)) {
      setFileList(value);
    }
  }, [value]);

  function beforeUpload(file: File) {
    if (file.size > 100 * 1024 * 1024) {
      Toast.show(`请选择小于 ${maxSize} 的视频`);
      return null;
    }
    return file;
  }

  return (
    <>
      <ImageUploader
        accept="video/*"
        beforeUpload={beforeUpload}
        deletable={!readOnly}
        maxCount={max}
        multiple
        onChange={(files: any) => {
          setFileList(files);
          onChange(files);
        }}
        onCountExceed={(exceed) => {
          Toast.show(`最多选择 ${max} 个视频，你多选了 ${exceed} 个`);
        }}
        onDelete={() => {
          return Dialog.confirm({
            content: '是否确认删除',
          });
        }}
        onPreview={(_, file) => {
          setVideoId(file.url);
          setVisible(true);
        }}
        preview={false}
        showUpload={!readOnly && fileList.length < max}
        upload={upload as any}
        value={fileList}
      />
      {!readOnly && (
        <div className="mt-3 text-sm text-stone-400">
          上限{max}个文件，最大{maxSize}/个
        </div>
      )}
      <Mask onMaskClick={() => setVisible(false)} visible={visible}>
        <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 flex w-full items-center justify-center rounded-lg bg-white">
          <VideoPlayer videoId={videoId} />
        </div>
      </Mask>
    </>
  );
}
