import { Cascader } from 'antd-mobile';
import type {
  CascaderActions,
  CascaderValueExtend,
} from 'antd-mobile/es/components/cascader';
import { omit } from 'lodash-es';
import { useImperativeHandle, useRef } from 'react';
import { useImmer } from 'use-immer';

import api from '@/lib/api';

// Types
interface AreaItemApi {
  name: string;
  areaCode: string;
}
interface AreaItem {
  label: string;
  value: string;
  children: AreaItem[];
}

function getArea(parentCode = '0'): Promise<AreaItemApi[]> {
  return api
    .get('/v1/contact/areas', { params: { parentCode } })
    .then((response) => response.data);
}

interface AddressProps {
  placeholder?: string;
  value?: { label: string; value: string }[];
  onChange: (data: { label: string; value: string }[]) => void;
  addons: { fieldRef: React.Ref<CascaderActions> };
  [key: string]: unknown;
}

export default function Address(props: AddressProps) {
  const {
    placeholder = '请选择',
    value,
    onChange,
    ...rest
  } = omit(props, ['addons', 'schema']) as AddressProps;

  const pickerRef = useRef<CascaderActions>(null);
  const [area, setArea] = useImmer<AreaItem[]>([]);

  useImperativeHandle(props.addons.fieldRef, () => ({
    open:
      pickerRef.current?.open ??
      (() => {
        throw new Error('Cascader not ready');
      }),
    close:
      pickerRef.current?.close ??
      (() => {
        throw new Error('Cascader not ready');
      }),
    toggle:
      pickerRef.current?.toggle ??
      (() => {
        throw new Error('Cascader not ready');
      }),
  }));

  const init = () => {
    if (area.length === 0) {
      getArea().then((data) => {
        if (Array.isArray(data)) {
          const arr: AreaItem[] = data.map((item) => ({
            label: item.name,
            value: item.areaCode,
            children: [],
          }));
          setArea(arr);
        }
      });
    }
  };

  const ensureProvinceChildren = async (
    province: string,
    provinceIndex: number
  ) => {
    const provinceItem = area[provinceIndex];
    if (provinceItem?.children?.length === 0) {
      const data = await getArea(province);
      if (Array.isArray(data)) {
        const arr: AreaItem[] = data.map((item) => ({
          label: item.name,
          value: item.areaCode,
          children: [],
        }));
        setArea((draft) => {
          if (draft[provinceIndex]) {
            draft[provinceIndex].children = arr;
          }
        });
      }
    }
  };

  const ensureCityChildren = async (
    provinceIndex: number,
    cityValue: string
  ) => {
    const provinceItem = area[provinceIndex];
    const cityIndex =
      provinceItem?.children?.findIndex((item) => item.value === cityValue) ??
      -1;
    if (
      cityIndex > -1 &&
      provinceItem?.children?.[cityIndex]?.children?.length === 0
    ) {
      const data = await getArea(cityValue);
      if (Array.isArray(data)) {
        const arr: AreaItem[] = data.map((item) => ({
          label: item.name,
          value: item.areaCode,
          children: [],
        }));
        setArea((draft) => {
          if (draft[provinceIndex]?.children?.[cityIndex]) {
            draft[provinceIndex].children[cityIndex].children = arr;
          }
        });
      }
    }
  };

  const onCascaderChange = async (
    cascaderValue: (string | number)[],
    _extend: CascaderValueExtend
  ) => {
    if (cascaderValue.length === 3) {
      return;
    }
    const [province, city] = cascaderValue;
    const provinceIndex = area.findIndex((item) => item.value === province);
    if (provinceIndex > -1 && typeof province === 'string') {
      await ensureProvinceChildren(province, provinceIndex);
      if (city && typeof city === 'string') {
        await ensureCityChildren(provinceIndex, city);
      }
    }
  };

  const onConfirm = (_: (string | number)[], extend: CascaderValueExtend) => {
    const data = extend.items.slice(0, 3).map((item) => ({
      value: item?.value ?? '',
      label: item?.label ?? '',
    }));
    onChange(data);
  };

  // Accessibility: use a button for interaction
  return (
    <Cascader
      {...rest}
      onConfirm={onConfirm}
      onSelect={onCascaderChange}
      options={area}
      ref={pickerRef}
    >
      {(items) => {
        let display = '';
        if (items.every((i) => i === null)) {
          if (Array.isArray(value) && value.length > 0) {
            display = value.map((item) => item.label).join('-');
          } else {
            display = '';
          }
        } else if (Array.isArray(items) && items.length > 0) {
          display = items.map((i) => i?.label ?? '未选择').join('-');
        }
        return (
          <button
            aria-label={display || placeholder}
            onClick={init}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                init();
              }
            }}
            style={{
              background: 'none',
              border: 'none',
              padding: 0,
              color: display ? undefined : '#ccc',
              cursor: 'pointer',
            }}
            type="button"
          >
            {display || placeholder}
          </button>
        );
      }}
    </Cascader>
  );
}
