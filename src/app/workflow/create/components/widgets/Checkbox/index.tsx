import { Checkbox as AntdCheckbox, Space } from 'antd-mobile';
import { omit } from 'lodash-es';

const isValidateArray = (list: unknown) =>
  Array.isArray(list) && list.length > 0;

const findLabels = (value: any[], options: any[]) => {
  if (!(isValidateArray(value) && isValidateArray(options))) {
    return [];
  }
  return value.map((v) => options.find((o) => o.value === v)?.label);
};

export default function Checkbox(props: any) {
  const { readOnly, checked, options, ...rest } = omit(props, [
    'addons',
    'schema',
  ]);

  if (readOnly) {
    const __html = findLabels(checked, options).join('，');
    return <div dangerouslySetInnerHTML={{ __html }} />;
  }

  return (
    <AntdCheckbox.Group value={checked} {...rest}>
      <Space direction="vertical" wrap>
        {options.map((item: any) => {
          return (
            <AntdCheckbox key={item.value} value={item.value}>
              {item.label}
            </AntdCheckbox>
          );
        })}
      </Space>
    </AntdCheckbox.Group>
  );
}
