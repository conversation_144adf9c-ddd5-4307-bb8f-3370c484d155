'use client';

import { Button } from 'antd-mobile';
import { omit } from 'lodash-es';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import SmoothSignature from 'smooth-signature';

import { PiArrowCounterClockwise } from '@/components/Icons';

interface SignatureProps {
  readOnly?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  addons?: unknown;
  schema?: unknown;
}

// 签名图标组件
const SignatureIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <title>签名图标</title>
    <path
      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

export default function Signature(props: SignatureProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const signatureRef = useRef<SmoothSignature | null>(null);
  const [windowSize, setWindowSize] = useState<[number, number]>(
    typeof window !== 'undefined'
      ? [window.innerWidth, window.innerHeight]
      : [0, 0]
  );
  const { readOnly, value, onChange } = omit(props, ['addons', 'schema']);
  const [isSignatureActive, setIsSignatureActive] = useState(false);
  const [isFullscreenSignature, setIsFullscreenSignature] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setWindowSize([window.innerWidth, window.innerHeight]);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
    return;
  }, []);

  // 初始化 SmoothSignature (仅在全屏模式下初始化)
  useEffect(() => {
    if (canvasRef.current && !signatureRef.current && isFullscreenSignature) {
      // 竖屏模式：保持正常的宽高
      signatureRef.current = new SmoothSignature(canvasRef.current, {
        width: windowSize[0],
        height: windowSize[1],
        scale: 1,
        color: '#000000',
        bgColor: '#ffffff', // 白色背景
        openSmooth: true,
        minSpeed: 1.5,
        maxWidthDiffRate: 20,
      });
    }
  }, [windowSize, isFullscreenSignature]);

  // 提交签名数据
  const submitSignature = useCallback(() => {
    if (!signatureRef.current) {
      return;
    }

    // 检查是否有签名内容
    if (signatureRef.current.isEmpty()) {
      return;
    }

    // 获取旋转后的画布 (旋转90度，因为我们是横屏签名)
    const rotatedCanvas = signatureRef.current.getRotateCanvas(90);
    const dataUrl = rotatedCanvas.toDataURL('image/png');

    if (onChange && dataUrl) {
      onChange(dataUrl);
    }
  }, [onChange]);

  // 全屏模式提交
  const handleFullscreenSubmit = useCallback(() => {
    submitSignature();
    setIsFullscreenSignature(false);
    setIsSignatureActive(false);
    // 清理签名实例
    signatureRef.current = null;
  }, [submitSignature]);

  // 全屏模式清空
  const handleFullscreenClear = useCallback(() => {
    signatureRef.current?.clear();
  }, []);

  // 打开全屏签名
  const openFullscreenSignature = useCallback(() => {
    setIsFullscreenSignature(true);
    setIsSignatureActive(true);
  }, []);

  // 取消全屏签名
  const handleFullscreenCancel = useCallback(() => {
    setIsFullscreenSignature(false);
    setIsSignatureActive(false);
    // 清理签名实例
    signatureRef.current = null;
  }, []);

  if (readOnly) {
    return (
      <div className="bg-white">
        {!!value && (
          <Image
            alt=""
            className="w-full object-cover"
            height="0"
            sizes="100%"
            src={value}
            width="0"
          />
        )}
      </div>
    );
  }

  return (
    <>
      <div className="relative flex flex-col items-center">
        {/* 只在非全屏模式下显示预览区域 */}
        {!isFullscreenSignature && (
          <div className="relative h-[300px] w-full rounded-lg border-2 border-gray-300 border-dashed bg-gray-50">
            {/* 签名激活蒙层 - 只在没有签名内容时显示 */}
            {!value && (
              <button
                className="absolute inset-0 z-10 flex cursor-pointer items-center justify-center rounded-lg border-none bg-black bg-opacity-20"
                onClick={openFullscreenSignature}
                type="button"
              >
                <div className="flex items-center gap-2 rounded-lg bg-white px-4 py-2 shadow-lg">
                  <SignatureIcon className="h-5 w-5 text-gray-600" />
                  <span className="font-medium text-gray-700">点击签名</span>
                </div>
              </button>
            )}

            {/* 显示已有签名 */}
            {!!value && (
              <Image
                alt="用户签名"
                className="h-full w-full rounded-lg object-contain"
                height="0"
                sizes="100vw"
                src={value}
                width="0"
              />
            )}

            {/* 重签按钮 - 绝对定位在右上角 */}
            {!!value && (
              <button
                className="absolute top-3 right-3 rounded-full bg-white p-2 shadow-md transition-shadow hover:shadow-lg"
                onClick={openFullscreenSignature}
                title="重签"
                type="button"
              >
                <SignatureIcon className="h-4 w-4 text-gray-600" />
              </button>
            )}
          </div>
        )}
      </div>

      {/* 全屏签名模态框 */}
      {isFullscreenSignature && (
        <div className="fixed inset-0 z-50 flex flex-col bg-gray-100">
          {/* 签名区域 */}
          <div className="relative h-screen w-screen">
            <canvas
              ref={canvasRef}
              style={{
                width: '100%',
                height: '100%',
                display: 'block',
              }}
            />

            {/* 横屏提示 - 右侧垂直居中 */}
            <div className="-translate-y-1/2 -right-10 absolute top-1/2 flex items-center justify-center">
              <div className="rotate-90 whitespace-nowrap rounded bg-gray-300 px-3 py-2">
                <p className="text-gray-600 text-sm">请横向持握手机进行签名</p>
              </div>
            </div>

            {/* 清空按钮 - 右上角 */}
            {isSignatureActive && (
              <button
                className="absolute top-4 right-4 rounded-full border border-gray-200 bg-white p-3 shadow-lg"
                onClick={handleFullscreenClear}
                title="清空"
                type="button"
              >
                <PiArrowCounterClockwise color="#333" fontSize={20} />
              </button>
            )}

            {/* 底部按钮组 */}
            <div className="-left-5 absolute bottom-16 flex rotate-90 justify-center gap-4">
              <Button
                color="default"
                onClick={handleFullscreenCancel}
                shape="rounded"
                size="large"
              >
                取消
              </Button>
              <Button
                color="primary"
                onClick={handleFullscreenSubmit}
                shape="rounded"
                size="large"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
