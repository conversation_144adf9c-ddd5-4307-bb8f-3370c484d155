# ClassPicker 班级选择组件

基于 `src/components/ClassPicker/index.tsx` 开发的工作流表单班级选择组件，支持单选和多选两种模式。

## 功能特性

- ✅ **单选模式**：类似输入框的简洁界面，点击后弹出级联选择器
- ✅ **多选模式**：横向显示已选择的班级标签，支持添加和删除
- ✅ 每个班级标签右上角有红色删除角标（仅多选模式）
- ✅ 级联选择：年级 -> 班级
- ✅ 只读模式支持
- ✅ 响应式设计

## 模式区别

### 单选模式
- 界面类似普通输入框，显示当前选中的班级
- 点击后弹出级联选择器（Picker）
- 选择后自动关闭选择器
- 返回单个 `ClassInfo` 对象

### 多选模式
- 显示已选班级的标签列表
- 每个标签有删除按钮
- 点击加号按钮弹出班级列表
- 支持在列表中勾选/取消勾选班级
- 返回 `ClassInfo[]` 数组

## 使用方法

### 基本用法

```tsx
import ClassPickerWidget from './ClassPicker';

// 单选模式
<ClassPickerWidget
  value={selectedClass}
  onChange={(value) => setSelectedClass(value)}
  placeholder="请选择班级"
/>

// 多选模式
<ClassPickerWidget
  value={selectedClasses}
  onChange={(value) => setSelectedClasses(value)}
  multiple={true}
  placeholder="请选择班级"
/>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | `ClassInfo \| ClassInfo[]` | - | 选中的班级信息 |
| onChange | `(value: ClassInfo \| ClassInfo[]) => void` | - | 选择变化回调 |
| readOnly | `boolean` | `false` | 是否只读模式 |
| placeholder | `string` | `'请选择班级'` | 占位符文本 |
| multiple | `boolean` | `false` | 是否支持多选 |

### 数据结构

#### ClassInfo 班级信息

```typescript
interface ClassInfo {
  id: string;        // 班级ID
  name: string;      // 班级名称
  gradeName: string; // 年级名称
  fullName: string;  // 完整名称（年级 / 班级）
}
```

### 配置选项

#### multiple

- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否启用多选模式

```tsx
// 单选模式（默认）
<ClassPickerWidget
  multiple={false}
  // ...其他props
/>

// 多选模式
<ClassPickerWidget
  multiple={true}
  // ...其他props
/>
```

## 使用示例

### 单选模式示例

```tsx
import React, { useState } from 'react';
import ClassPickerWidget from './ClassPicker';

function SingleSelectExample() {
  const [selectedClass, setSelectedClass] = useState<ClassInfo | null>(null);

  return (
    <ClassPickerWidget
      value={selectedClass}
      onChange={(value) => setSelectedClass(value as ClassInfo)}
      placeholder="请选择一个班级"
    />
  );
}
```

### 多选模式示例

```tsx
import React, { useState } from 'react';
import ClassPickerWidget from './ClassPicker';

function MultiSelectExample() {
  const [selectedClasses, setSelectedClasses] = useState<ClassInfo[]>([]);

  return (
    <ClassPickerWidget
      value={selectedClasses}
      onChange={(value) => setSelectedClasses(value as ClassInfo[])}
      multiple={true}
      placeholder="请选择班级"
    />
  );
}
```

### 只读模式示例

```tsx
<ClassPickerWidget
  value={selectedClasses}
  readOnly={true}
/>
```

## 样式说明

组件使用 Tailwind CSS 进行样式设计：

- 已选择的班级显示为蓝色圆角标签
- 删除按钮为红色圆形图标，位于标签右上角
- 添加按钮为带边框的圆形加号图标
- 支持响应式布局，标签会自动换行

## 依赖项

- `antd-mobile`: 移动端UI组件库
- `lucide-react`: 图标库
- `lodash-es`: 工具函数库
- `@/api/common`: 班级数据API

## 注意事项

1. 组件依赖 `getClassList` API 获取班级数据
2. 数据结构需要符合 `RawClassData` 接口定义
3. 单选模式下 `onChange` 回调参数为 `ClassInfo | null`
4. 多选模式下 `onChange` 回调参数为 `ClassInfo[]`
5. 只读模式下不会显示选择器，仅展示已选择的班级
