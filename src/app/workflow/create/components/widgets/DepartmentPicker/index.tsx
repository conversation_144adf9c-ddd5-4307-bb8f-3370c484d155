'use client';

import { List, Popup, Radio, Toast } from 'antd-mobile';
import { ChevronLeft, ChevronRight, Plus, X } from 'lucide-react';
import { useCallback, useState } from 'react';
import { getDeptUser } from '@/api/common';

// 后端返回的原始数据结构
interface Department {
  id: string;
  instId: string;
  parentId: string;
  level: number;
  name: string;
  sort: number;
  children?: Department[];
  staffList?: unknown[];
}

// 部门信息
interface DepartmentInfo {
  id: string;
  name: string;
  level: number;
}

interface DepartmentPickerWidgetProps {
  value?: DepartmentInfo[];
  onChange?: (value?: DepartmentInfo[]) => void;
  readOnly?: boolean;
  placeholder?: string;
  multiple?: boolean; // 是否支持多选
}

// 获取部门数据的API函数
const fetchDepartments = async (): Promise<Department> => {
  try {
    const response = await getDeptUser({ parentId: 0, fetchChild: 1 });
    return response.data || response;
  } catch (error) {
    console.error('获取部门数据失败：', error);
    throw error;
  }
};

export default function DepartmentPickerWidget(
  props: DepartmentPickerWidgetProps
) {
  const {
    readOnly = false,
    value,
    onChange,
    placeholder = '请选择部门',
    multiple = false,
  } = props;
  console.log('🚀 ~ props:', props);

  const [visible, setVisible] = useState(false);
  const [departmentTree, setDepartmentTree] = useState<Department | null>(null);
  const [currentPath, setCurrentPath] = useState<Department[]>([]); // 当前导航路径
  const [currentDepartments, setCurrentDepartments] = useState<Department[]>(
    []
  ); // 当前显示的部门列表
  const [loading, setLoading] = useState(false);

  // 获取部门数据
  const loadDepartments = useCallback(async () => {
    if (departmentTree) {
      return; // 已加载过数据
    }

    setLoading(true);
    try {
      const data = await fetchDepartments();
      setDepartmentTree(data);
      // 初始显示根部门的子部门
      setCurrentDepartments(data.children || []);
      setCurrentPath([data]); // 根部门作为路径起点
    } catch (_error) {
      Toast.show({
        content: '获取部门列表失败',
        position: 'center',
      });
    } finally {
      setLoading(false);
    }
  }, [departmentTree]);

  const selectedDepartments = Array.isArray(value) ? value : [];

  // 只读模式渲染
  if (readOnly) {
    if (!selectedDepartments || selectedDepartments.length === 0) {
      return <div className="text-gray-400">未选择部门</div>;
    }

    return (
      <div className="flex flex-wrap gap-2">
        {selectedDepartments.map((dept) => (
          <div
            className="inline-flex items-center rounded-full bg-blue-50 px-3 py-1 text-blue-700 text-sm"
            key={dept.id}
          >
            {dept.name}
          </div>
        ))}
      </div>
    );
  }

  // 处理选择器打开
  const handlePickerOpen = async () => {
    await loadDepartments();
    setVisible(true);
  };

  // 处理选择器关闭
  const handlePickerClose = () => {
    setVisible(false);
  };

  // 进入子部门
  const handleEnterSubDepartment = (department: Department) => {
    if (department.children && department.children.length > 0) {
      setCurrentPath([...currentPath, department]);
      setCurrentDepartments(department.children);
    }
  };

  // 返回上级部门
  const handleGoBack = () => {
    if (currentPath.length > 1) {
      const newPath = currentPath.slice(0, -1);
      setCurrentPath(newPath);
      const parentDepartment = newPath.at(-1);
      setCurrentDepartments(parentDepartment?.children || []);
    }
  };

  // 导航到指定路径
  const handleNavigateToPath = (index: number) => {
    if (index < currentPath.length - 1) {
      const newPath = currentPath.slice(0, index + 1);
      setCurrentPath(newPath);
      const targetDepartment = newPath.at(-1);
      setCurrentDepartments(targetDepartment?.children || []);
    }
  };

  // 选择/取消选择部门
  const handleSelectDepartment = (department: Department) => {
    const departmentInfo: DepartmentInfo = {
      id: department.id,
      name: department.name,
      level: department.level,
    };

    const isSelected = selectedDepartments.some((d) => d.id === department.id);

    let newSelectedDepartments: DepartmentInfo[];

    if (multiple) {
      if (isSelected) {
        // 取消选择
        newSelectedDepartments = selectedDepartments.filter(
          (d) => d.id !== department.id
        );
      } else {
        // 添加选择
        newSelectedDepartments = [...selectedDepartments, departmentInfo];
      }
    } else if (isSelected) {
      // 单选模式 - 取消选择
      newSelectedDepartments = [];
    } else {
      // 单选模式 - 选择
      newSelectedDepartments = [departmentInfo];
    }

    if (onChange && typeof onChange === 'function') {
      onChange(newSelectedDepartments);
    }
  };

  // 移除已选择的部门
  const handleRemoveDepartment = (departmentId: string) => {
    const newSelectedDepartments = selectedDepartments.filter(
      (d) => d.id !== departmentId
    );

    if (onChange && typeof onChange === 'function') {
      onChange(newSelectedDepartments);
    }
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <div className="mb-4 flex items-center gap-1 text-sm">
        {currentPath.slice(1).map(
          (
            dept,
            index // 排除根部门
          ) => (
            <div className="flex items-center" key={dept.id}>
              {index > 0 && (
                <ChevronRight className="mx-1 h-3 w-3 text-gray-400" />
              )}
              <button
                className={`text-blue-600 hover:text-blue-800 ${
                  index === currentPath.length - 2 ? 'font-medium' : ''
                }`}
                onClick={() => handleNavigateToPath(index + 1)}
                type="button"
              >
                {dept.name}
              </button>
            </div>
          )
        )}
      </div>
    );
  };

  // 渲染部门列表
  const renderDepartmentList = () => {
    return (
      <div className="flex max-h-[50vh] flex-col overflow-y-auto">
        <List
          style={{
            '--border-top': 'none',
            '--border-inner': 'solid 1px #F1F1F1',
          }}
        >
          {currentDepartments.map((department) => {
            const isSelected = selectedDepartments.some(
              (d) => d.id === department.id
            );
            const hasChildren =
              department.children && department.children.length > 0;

            return (
              <List.Item
                arrow={false}
                key={department.id}
                onClick={() => handleSelectDepartment(department)}
              >
                <div className="flex flex-1 items-center justify-between">
                  <div className="flex flex-1 items-center">
                    <Radio
                      checked={isSelected}
                      onChange={() => handleSelectDepartment(department)}
                      style={{ marginRight: '12px' }}
                    />
                    <span className="flex-1">{department.name}</span>
                  </div>
                  {hasChildren && (
                    <button
                      className="ml-2 p-1 text-gray-400 hover:text-gray-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEnterSubDepartment(department);
                      }}
                      type="button"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </List.Item>
            );
          })}
        </List>
      </div>
    );
  };

  // 渲染弹窗内容
  const renderPopupContent = () => {
    return (
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '56vh',
          maxHeight: '100vh',
          paddingTop: '10px',
        }}
        onMaskClick={handlePickerClose}
        visible={visible}
      >
        <div className="p-4 pt-2">
          {/* 顶部导航栏 */}
          <div className="mb-4 flex items-center justify-between text-base">
            <button
              className="flex items-center text-stone-400"
              onClick={
                currentPath.length > 1 ? handleGoBack : handlePickerClose
              }
              type="button"
            >
              {currentPath.length > 1 ? (
                <>
                  <ChevronLeft className="mr-1 h-4 w-4" />
                  返回
                </>
              ) : (
                '取消'
              )}
            </button>
            <div className="text-lg text-stone-900">选择部门</div>
            <button
              className="text-[#3B82F7]"
              onClick={handlePickerClose}
              type="button"
            >
              确定
            </button>
          </div>

          {/* 面包屑导航 */}
          {currentPath.length > 1 && renderBreadcrumb()}

          {/* 已选部门展示 */}
          {selectedDepartments.length > 0 && (
            <div className="mb-4 flex flex-wrap gap-2">
              {selectedDepartments.map((dept) => (
                <div
                  className="inline-flex items-center rounded-full bg-blue-50 px-3 py-1 text-blue-700 text-sm"
                  key={dept.id}
                >
                  {dept.name}
                  <button
                    className="ml-2 text-blue-500 hover:text-blue-700"
                    onClick={() => handleRemoveDepartment(dept.id)}
                    type="button"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* 部门列表 */}
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : (
            renderDepartmentList()
          )}
        </div>
      </Popup>
    );
  };

  // 统一渲染逻辑
  if (!multiple) {
    // 单选模式
    const selectedDepartment = selectedDepartments[0];

    return (
      <div className="department-picker-widget">
        <button
          className="flex min-h-[48px] w-full cursor-pointer items-center justify-between transition-colors"
          onClick={handlePickerOpen}
          type="button"
        >
          <div className="flex-1 text-left">
            {selectedDepartment ? (
              <span className="text-gray-900">{selectedDepartment.name}</span>
            ) : (
              <span className="text-gray-400">{String(placeholder)}</span>
            )}
          </div>
          <ChevronRight className="h-5 w-5 text-gray-400 transition-colors group-hover:text-gray-600" />
        </button>
        {renderPopupContent()}
      </div>
    );
  }

  // 多选模式
  return (
    <div className="department-picker-widget">
      <div className="space-y-3">
        <div className="flex flex-wrap items-center gap-2">
          {selectedDepartments.map((dept) => (
            <div
              className="relative inline-flex items-center rounded-full bg-blue-50 px-3 py-1 text-blue-700 text-sm"
              key={dept.id}
            >
              {dept.name}
              <button
                className="-top-1 -right-1 absolute flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-white text-xs hover:bg-red-600"
                onClick={() => handleRemoveDepartment(dept.id)}
                type="button"
              >
                <X className="h-full w-full p-0.5" />
              </button>
            </div>
          ))}
          <button
            className="flex items-center text-primary text-sm hover:text-primary/80"
            onClick={handlePickerOpen}
            type="button"
          >
            <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded-full border-2 border-gray-400">
              <Plus className="h-4 w-4" />
            </div>
          </button>
        </div>
        {selectedDepartments.length === 0 && (
          <div className="text-gray-400 text-sm">{String(placeholder)}</div>
        )}
      </div>
      {renderPopupContent()}
    </div>
  );
}
