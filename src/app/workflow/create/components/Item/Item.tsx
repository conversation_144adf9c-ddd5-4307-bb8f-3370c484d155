'use client';

import type { DraggableSyntheticListeners } from '@dnd-kit/core';
import clsx from 'clsx';
import React, { useCallback, useEffect } from 'react';
import { PiDotsSixVerticalBold, PiMinusCircleFill } from '@/components/Icons';
import type { ItemType } from '../../../types';
import styles from './Item.module.scss';

export interface Props {
  dragOverlay?: boolean;
  dragging?: boolean;
  handle?: boolean;
  handleProps?: { ref: React.Ref<HTMLElement> };
  listeners?: DraggableSyntheticListeners;
  sorting?: boolean;
  value: ItemType;
  onRemove?(id: string): void;
  onCopyGroup?(id: string): void;
  setWidgetId: (id: string) => void;
  onAddGroupChild?: (groupId: string) => void;
  removeGroupChildItem?: (groupId: string, childId: string) => void;
}

export const Item = React.memo(
  React.forwardRef<HTMLLIElement, Props>(
    (
      {
        dragOverlay,
        dragging,
        handle,
        handleProps,
        listeners,
        onRemove,
        onCopyGroup,
        sorting,
        value,
        setWidgetId,
        onAddGroupChild,
        removeGroupChildItem,
      },
      ref
    ) => {
      // 设置拖拽时光标样式
      useEffect(() => {
        if (dragOverlay && typeof document !== 'undefined') {
          document.body.style.cursor = 'grabbing';
          return () => {
            document.body.style.cursor = '';
          };
        }
      }, [dragOverlay]);

      // 检查是否为地址类型的group组件
      const isAddressGroup = useCallback((item: ItemType): boolean => {
        if (item.widget !== 'group' || !item.properties) {
          return false;
        }

        const props = item.properties as Record<string, ItemType>;
        const hasAreaField = Object.values(props).some(
          (prop: ItemType) => prop?.widget === 'address'
        );
        const hasDetailField = Object.values(props).some(
          (prop: ItemType) => prop?.widget === 'textArea'
        );

        return hasAreaField && hasDetailField;
      }, []);

      // 渲染分组项（排除地址类型）
      if (value.widget === 'group' && !isAddressGroup(value)) {
        const childItems = value.properties
          ? Object.values(value.properties)
              .filter(
                (item): item is ItemType =>
                  item != null && typeof item === 'object' && 'id' in item
              )
              .sort((a, b) => {
                const orderA = (a as { order?: number }).order ?? 0;
                const orderB = (b as { order?: number }).order ?? 0;
                return orderA - orderB;
              })
          : [];

        return (
          <li
            className={clsx(
              styles.Wrapper,
              sorting && styles.sorting,
              dragOverlay && styles.dragOverlay
            )}
            ref={ref}
            {...(handle ? undefined : listeners)}
          >
            <div className={clsx(styles.Item, 'group-container')}>
              {/* 分组标题 */}
              <div className="mb-2 flex w-full justify-between border-slate-100 border-b pb-2">
                <div className="flex-1 text-base text-gray-900">
                  {value.title || '分组'}
                </div>
              </div>

              {/* 分组内容区域 */}
              <ol className="rounded-lg border-2 border-gray-200 border-dashed bg-gray-50 p-3">
                {childItems.length > 0 ? (
                  childItems.map((childItem) => (
                    <Item
                      dragging={false}
                      dragOverlay={false}
                      handle={false}
                      key={childItem.id}
                      onRemove={
                        removeGroupChildItem
                          ? (childId: string) =>
                              removeGroupChildItem(value.id, childId)
                          : undefined
                      }
                      setWidgetId={setWidgetId}
                      sorting={false}
                      value={childItem}
                    />
                  ))
                ) : (
                  <div className="py-4 text-center text-gray-500 text-sm">
                    暂无控件，点击下方按钮添加
                  </div>
                )}

                {/* 添加控件按钮 */}
                <button
                  className="mt-3 flex w-full items-center justify-center rounded border border-blue-300 border-dashed py-2 text-blue-600 hover:bg-blue-50"
                  onClick={() => onAddGroupChild?.(value.id)}
                  type="button"
                >
                  <span className="mr-1 text-lg">+</span>
                  添加控件
                </button>
              </ol>
              {/* 分组操作按钮 */}
              <div className="mt-2 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {onRemove && (
                    <button
                      className="flex items-center"
                      onClick={() => onRemove(value.id)}
                      type="button"
                    >
                      <PiMinusCircleFill color="#DBDBDB" fontSize={20} />
                      <span className="ml-2 text-sm text-stone-500">
                        删除分组
                      </span>
                    </button>
                  )}
                  {onCopyGroup && (
                    <button
                      className="flex items-center"
                      onClick={() => onCopyGroup(value.id)}
                      type="button"
                    >
                      <span style={{ color: '#DBDBDB', fontSize: '20px' }}>
                        ⊕
                      </span>
                      <span className="ml-2 text-sm text-stone-500">
                        复制分组
                      </span>
                    </button>
                  )}
                </div>
                <div className="flex items-center">
                  <button
                    className="primary-color ml-3 text-base"
                    onClick={() => setWidgetId(value.id)}
                    type="button"
                  >
                    分组设置
                  </button>
                  {handle && (
                    <div
                      className="ml-3"
                      ref={handleProps?.ref as React.LegacyRef<HTMLDivElement>}
                    >
                      <PiDotsSixVerticalBold
                        color="#DBDBDB"
                        fontSize={20}
                        {...listeners}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </li>
        );
      }

      // 渲染普通表单项
      return (
        <li
          className={clsx(
            styles.Wrapper,
            sorting && styles.sorting,
            dragOverlay && styles.dragOverlay
          )}
          ref={ref}
        >
          <div
            className={clsx(
              styles.Item,
              dragging && styles.dragging,
              handle && styles.withHandle,
              dragOverlay && styles.dragOverlay
            )}
            {...(handle ? undefined : listeners)}
          >
            <div className="mb-2 flex w-full justify-between border-slate-100 border-b pb-2">
              <div className="flex-1 text-base text-gray-900">
                {value.title}
              </div>
              {value.required && (
                <div className="w-8 text-gray-400 text-xs">必填</div>
              )}
            </div>
            <div className="flex items-center justify-between">
              {onRemove && (
                <button
                  className="flex items-center"
                  onClick={() => onRemove(value.id)}
                  type="button"
                >
                  <PiMinusCircleFill color="#DBDBDB" fontSize={20} />
                  <span className="ml-2 text-sm text-stone-500">删除</span>
                </button>
              )}
              <div className="flex items-center">
                <button
                  className="primary-color ml-3 text-base"
                  onClick={() => setWidgetId(value.id)}
                  type="button"
                >
                  控件设置
                </button>
                {handle && (
                  <div
                    className={clsx(styles.Actions, 'ml-3')}
                    ref={handleProps?.ref as React.LegacyRef<HTMLDivElement>}
                  >
                    <PiDotsSixVerticalBold
                      color="#DBDBDB"
                      fontSize={20}
                      {...listeners}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </li>
      );
    }
  )
);
