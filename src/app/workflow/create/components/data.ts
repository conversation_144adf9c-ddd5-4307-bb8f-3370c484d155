import { nanoid } from '@/utils';

type TypeKeys = 'ROOT' | 'APPROVAL' | 'CC' | 'TASK';

export const typeMap: Record<TypeKeys, string> = {
  ROOT: '发起人',
  APPROVAL: '审批人',
  CC: '抄送人',
  TASK: '办理人',
};

export const modeType: { [key: string]: string } = {
  AND: '会签（须所有成员通过）',
  OR: '或签（一名成员通过即可）',
  NEXT: '依次审批（按人员顺序依次审批）',
};

export const properties: any = {
  input: {
    title: '单行文本',
    description: '',
    type: 'string',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    exportable: true,
    required: true,
    props: {
      maxLength: 20,
    },
  },
  textArea: {
    title: '多行文本',
    description: '',
    type: 'string',
    layout: 'column',
    widget: 'textArea',
    placeholder: '请输入',
    printable: true,
    exportable: true,
    required: true,
    props: {
      rows: 5,
      showCount: true,
      maxLength: 200,
    },
  },
  richText: {
    title: '说明文字',
    description: '',
    type: 'string',
    widget: 'richText',
    readOnlyWidget: 'richText',
    printable: true,
    exportable: false,
    required: false,
    props: {
      value: '',
    },
  },
  inputNumber: {
    title: '数字',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    exportable: true,
    required: true,
    props: {
      type: 'number',
    },
  },
  amountNumber: {
    title: '金额',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    exportable: true,
    required: true,
    props: {
      type: 'number',
    },
  },
  phoneNumber: {
    title: '手机号',
    description: '',
    type: 'number',
    widget: 'input',
    placeholder: '请输入',
    printable: true,
    exportable: true,
    required: true,
    props: {
      type: 'number',
    },
    rules: [{ pattern: /^1[34578]\d{9}$/, message: '请输入正确的手机号！' }],
  },
  signature: {
    title: '签名',
    description: '',
    type: 'string',
    widget: 'signature',
    placeholder: '请签名',
    readOnlyWidget: 'signature',
    layout: 'column',
    printable: true,
    exportable: true,
    required: true,
  },
  image: {
    title: '图片',
    description: '',
    type: 'array',
    widget: 'image',
    placeholder: '请上传图片',
    readOnlyWidget: 'image',
    layout: 'column',
    printable: true,
    exportable: true,
    required: true,
    props: {
      max: 20,
    },
  },
  video: {
    title: '视频',
    description: '',
    type: 'array',
    widget: 'video',
    placeholder: '请上传视频',
    readOnlyWidget: 'video',
    layout: 'column',
    printable: true,
    exportable: true,
    required: true,
    props: {
      max: 5,
    },
  },
  attachment: {
    title: '附件',
    description: '',
    type: 'array',
    widget: 'attachment',
    placeholder: '请上传附件',
    readOnlyWidget: 'attachment',
    layout: 'column',
    printable: true,
    exportable: true,
    required: true,
    props: {
      max: 20,
    },
  },
  // 下拉选择
  picker: {
    title: '下拉选择',
    description: '',
    type: 'array',
    widget: 'picker',
    placeholder: '请选择',
    printable: true,
    exportable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  checkboxes: {
    title: '列表多选',
    description: '',
    type: 'array',
    widget: 'checkboxes',
    placeholder: '请选择',
    readOnlyWidget: 'checkboxes',
    printable: true,
    exportable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  date: {
    title: '日期',
    description: '',
    type: 'string',
    widget: 'datePicker',
    placeholder: '请选择',
    printable: true,
    exportable: true,
    required: true,
    props: {
      precision: 'day',
    },
  },
  dateTime: {
    title: '日期时间',
    description: '',
    type: 'string',
    widget: 'datePicker',
    placeholder: '请选择',
    printable: true,
    exportable: true,
    required: true,
    props: {
      format: 'YYYY-MM-DD HH:mm',
      precision: 'minute',
    },
  },
  slider: {
    title: '滑动条',
    description: '',
    type: 'number',
    widget: 'slider',
    printable: true,
    exportable: true,
    required: true,
    props: {
      range: true,
    },
  },
  switch: {
    title: '开关',
    description: '',
    type: 'bool',
    widget: 'switch',
    placeholder: '请选择',
    printable: true,
    exportable: true,
    required: true,
    props: {},
  },
  address: {
    title: '地址',
    description: '',
    type: 'object',
    widget: 'group',
    layout: 'column',
    printable: true,
    exportable: true,
    properties: {
      area: {
        id: `form-${nanoid(12)}`,
        title: '地区',
        type: 'array',
        widget: 'address',
        readOnlyWidget: 'address',
        placeholder: '请选择',
        order: 1,
      },
      detail: {
        id: `form-${nanoid(12)}`,
        title: '详细地址',
        type: 'string',
        layout: 'column',
        widget: 'textArea',
        placeholder: '请输入详细地址',
        props: {
          rows: 2,
          maxLength: 100,
        },
        order: 2,
      },
    },
  },
  rate: {
    title: '评分',
    description: '',
    type: 'number',
    widget: 'rate',
    printable: true,
    exportable: true,
    required: true,
    props: {
      count: 5,
    },
  },
  selector: {
    title: '选择组多选',
    description: '',
    type: 'array',
    widget: 'selector',
    printable: true,
    exportable: true,
    required: true,
    props: {
      multiple: true,
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  stepper: {
    title: '步进器',
    description: '',
    type: 'number',
    widget: 'stepper',
    printable: true,
    exportable: true,
    required: true,
  },
  cascader: {
    title: '级联',
    description: '',
    type: 'array',
    widget: 'cascader',
    printable: true,
    exportable: true,
    required: true,
    props: {
      options: [
        {
          label: '一级菜单',
          value: 1,
          children: [{ label: '二级菜单', value: 2 }],
        },
      ],
    },
  },
  radio: {
    title: '单选',
    description: '',
    type: 'string',
    widget: 'radio',
    printable: true,
    exportable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' },
        { label: '选项4', value: '4' },
        { label: '选项5', value: '5' },
        { label: '选项6', value: '6' },
        { label: '选项7', value: '7' },
        { label: '选项8', value: '8' },
        { label: '选项9', value: '9' },
      ],
    },
  },
  checkbox: {
    title: '多选',
    description: '',
    type: 'array',
    widget: 'checkbox',
    readOnlyWidget: 'checkbox',
    printable: true,
    exportable: true,
    required: true,
    props: {
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
      ],
    },
  },
  group: {
    title: '分组',
    description: '',
    type: 'object',
    widget: 'group',
    printable: true,
    exportable: true,
    required: false,
  },
  group2: {
    type: 'object',
    title: '分组2',
    widget: 'group',
    description: '这是一个对象类型',
    exportable: true,
    properties: {
      input1: {
        title: '输入框 A',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input2: {
        title: '输入框 B',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
    },
  },
  card: {
    type: 'object',
    widget: 'card',
    title: '卡片主题',
    description: '这是一个对象类型',
    exportable: true,
    properties: {
      input1: {
        title: '输入框 A',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input2: {
        title: '输入框 B',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
      input3: {
        title: '输入框 C',
        type: 'string',
        widget: 'input',
        placeholder: '请输入',
      },
    },
  },
  list: {
    title: '对象数组',
    description: '',
    type: 'array',
    widget: 'cardList',
    exportable: true,
    items: {
      type: 'object',
      widget: 'card',
      properties: {
        input1: {
          title: '输入框 A',
          type: 'string',
          widget: 'input',
        },
        input2: {
          title: '输入框 B',
          type: 'string',
          widget: 'input',
        },
        input3: {
          title: '输入框 B',
          type: 'string',
          widget: 'input',
        },
      },
    },
  },
  studentPicker: {
    title: '学生',
    description: '',
    type: 'array',
    widget: 'student',
    readOnlyWidget: 'student',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: true,
      maxCount: 50,
      placeholder: '请选择学生',
    },
  },
  teacherPicker: {
    title: '老师',
    description: '',
    type: 'array',
    widget: 'teacher',
    readOnlyWidget: 'teacher',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: true,
      maxCount: 50,
      placeholder: '请选择老师',
    },
  },
  classPicker: {
    title: '班级',
    description: '',
    type: 'array',
    widget: 'class',
    readOnlyWidget: 'class',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: false,
      placeholder: '请选择班级',
    },
  },
  classMultiple: {
    title: '班级（多选）',
    description: '',
    type: 'array',
    widget: 'class',
    readOnlyWidget: 'class',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: true,
      placeholder: '请选择班级',
    },
  },
  departmentPicker: {
    title: '部门',
    description: '',
    type: 'array',
    widget: 'department',
    readOnlyWidget: 'department',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: false,
      placeholder: '请选择部门',
    },
  },
  departmentMultiple: {
    title: '部门（多选）',
    description: '',
    type: 'array',
    widget: 'department',
    readOnlyWidget: 'department',
    layout: 'column',
    printable: true,
    exportable: true,
    required: false,
    props: {
      multiple: true,
      placeholder: '请选择部门',
    },
  },
  table: {
    title: '表格',
    description: '',
    type: 'array',
    placeholder: '请选择',
    widget: 'table',
    layout: 'column',
    required: false,
    printable: true,
    props: {
      columns: [
        {
          key: 'name',
          title: '姓名',
        },
        {
          key: 'age',
          title: '年龄',
        },
        {
          key: 'phone',
          title: '电话',
        },
      ],
    },
  },
};

export const schema = {
  type: 'object',
  displayType: 'row',
  properties: {
    ...properties,
    richText: {
      title: '富文本',
      type: 'string',
      widget: 'richText',
      readOnlyWidget: 'richText',
      displayType: 'vertical',
      noStyle: true,
      exportable: false,
      props: {
        value: `<section data-role="outer" class="article" style="padding: 0px; box-sizing: border-box;" data-tplid="134825">
            <div class="text-red-400">富文本</div>
          </section>`,
      },
    },
  },
};

export const processJson: any = {
  id: 'root',
  desc: '任何人',
  name: '发起人',
  type: 'ROOT',
  props: {
    formPerms: [],
    listeners: {},
    assignedUser: [],
    operationPerm: {
      agree: {
        show: true,
        alias: '提交',
      },
    },
  },
  children: {
    id: `node-${nanoid(12)}`,
    name: '审批人',
    type: 'APPROVAL',
    props: {
      mode: 'AND',
      role: [],
      sign: false,
      leader: {
        level: 1,
        skipEmpty: true,
      },
      nobody: {
        handler: 'TO_ADMIN',
        assignedUser: [],
      },
      refuse: {
        type: 'TO_END',
        target: '',
      },
      formDept: '',
      formUser: '',
      formPerms: [],
      leaderTop: {
        endLevel: 0,
        skipEmpty: false,
        endCondition: 'TOP',
      },
      listeners: {},
      timeLimit: {
        handler: {
          type: 'REFUSE',
          notify: {
            hour: 1,
            once: true,
          },
        },
        timeout: {
          unit: 'H',
          value: 0,
        },
      },
      selfSelect: {
        multiple: false,
      },
      assignedDept: [],
      assignedType: 'ASSIGN_USER',
      assignedUser: [],
      operationPerm: {
        agree: {
          show: true,
          alias: '同意',
        },
        refuse: {
          show: true,
          alias: '拒绝',
        },
      },
    },
    children: {
      id: `node-${nanoid(12)}`,
      name: '抄送人',
      type: 'CC',
      props: {
        assignedType: 'ASSIGN_USER',
        formPerms: [],
        shouldAdd: false,
        assignedUser: [],
      },
      children: {},
      // parentId: 'node-669820554802',
    },
    parentId: 'root',
  },
  parentId: null,
};

export const formItems = Object.keys(properties).map((key) => ({
  ...properties[key],
  id: `form-${nanoid(12)}`,
}));

export const testData = {
  type: 'object',
  displayType: 'column',
  properties: {
    'form-qZ5w2FLx7Sdm': {
      title: '检查部门',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'picker',
      props: {
        options: [
          {
            label: '行政部',
            value: '1',
          },
          {
            label: '财务部',
            value: '2',
          },
          {
            label: '教学部',
            value: '3',
          },
          {
            label: '后勤部',
            value: '4',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-qZ5w2FLx7Sdm',
      order: 0,
    },
    switch: {
      title: '开关',
      description: '',
      type: 'bool',
      widget: 'switch',
      placeholder: '请选择',
      printable: true,
      exportable: true,
      required: true,
    },
    'form-qZ5w2FLx7Sef': {
      title: '检查部门',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'table',
      props: {
        columns: [
          {
            key: 'name',
            title: '姓名',
            dataIndex: 'name',
          },
          {
            key: 'age',
            title: '班级',
            dataIndex: 'age',
          },
          {
            key: 'email',
            title: '333',
            dataIndex: 'email',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-qZ5w2FLx7Sef',
      order: 0,
    },
    group1: {
      type: 'object',
      widget: 'group',
      title: '检查项目明细',
      order: 1,
      properties: {
        'form-HB3lF6BMO775f': {
          title: '每日一次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '鞋柜', value: '1' },
              { label: '窗', value: '2' },
              { label: '门', value: '3' },
              { label: '书包柜', value: '4' },
              { label: '口杯', value: '5' },
              { label: '被褥', value: '6' },
              { label: '环创装饰物', value: '7' },
              { label: '储物架', value: '8' },
              { label: '教师办公设施', value: '9' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7754': {
          title: '每日两次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '椅子', value: '1' },
              { label: '玩具', value: '2' },
              { label: '榻榻米', value: '3' },
              { label: '毛巾', value: '4' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7755': {
          title: '每日三次消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '门把手', value: '1' },
              { label: '灯具开关', value: '2' },
              { label: '地面', value: '3' },
              { label: '桌子', value: '4' },
              { label: '餐具', value: '5' },
              { label: '洗手间', value: '6' },
              { label: '水龙头', value: '7' },
              { label: '垃圾桶', value: '8' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
        'form-HB3lF6BMO7756': {
          title: '每周消毒',
          type: 'array',
          widget: 'selector',
          props: {
            multiple: true,
            options: [
              { label: '墙面', value: '1' },
              { label: '风扇', value: '2' },
            ],
          },
          required: false,
          printable: true,
          id: 'form-HB3lF6BMO775f',
          order: 1,
        },
      },
    },
    group2: {
      type: 'object',
      widget: 'group',
      title: '检查项目明细',
      order: 1,
      layout: 'horizontal',
      columns: 2,
      properties: {
        input: {
          title: '输入框',
          type: 'string',
          widget: 'input',
          column: 3,
          layout: 'row',
          printable: true,
        },
        switch: {
          title: '是否检查',
          type: 'bool',
          widget: 'switch',
          layout: 'row',
        },
        radio: {
          title: '单选',
          type: 'string',
          widget: 'radio',
          layout: 'row',
          printable: true,
          props: {
            options: [
              { label: '未检', value: 'a' },
              { label: '已检', value: 'b' },
              { label: '优秀', value: 'c' },
            ],
          },
        },
      },
    },
    'form-6zUMHAqUXWQT': {
      id: 'form-6zUMHAqUXWQT',
      title: '地址',
      description: '',
      type: 'object',
      widget: 'group',
      layout: 'column',
      printable: true,
      properties: {
        area: {
          title: '地区',
          type: 'array',
          widget: 'address',
          readOnlyWidget: 'address',
          placeholder: '请选择',
        },
        detail: {
          title: '详细地址',
          type: 'string',
          layout: 'column',
          widget: 'textArea',
          placeholder: '请输入详细地址',
          props: {
            rows: 2,
            maxLength: 100,
          },
        },
      },
      order: 1,
    },
    'form-HB3lF6BMO77f': {
      title: '检查日期',
      description: '',
      type: 'string',
      placeholder: '请选择',
      widget: 'datePicker',
      required: false,
      printable: true,
      id: 'form-HB3lF6BMO77f',
      order: 1,
    },
    'form-l1cGrydGHcr0': {
      title: '检查说明',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'textArea',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-l1cGrydGHcr0',
      order: 2,
    },
    'form-B1teMmXgKQlT': {
      title: '上级部门',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'input',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-B1teMmXgKQlT',
      order: 3,
    },
    'form-FacGX4e7cwLs': {
      title: '接待人',
      description: '',
      type: 'string',
      placeholder: '请输入',
      widget: 'textArea',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-FacGX4e7cwLs',
      order: 4,
    },
    'form-vVK4zsNdiu9J': {
      title: '检查照片',
      description: '',
      type: 'array',
      placeholder: '请上传图片',
      widget: 'image',
      readOnlyWidget: 'image',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-vVK4zsNdiu9J',
      order: 5,
    },
    'form-mTE1pkHNsFSH': {
      title: '整改后图片',
      description: '',
      type: 'array',
      placeholder: '请上传图片',
      widget: 'image',
      readOnlyWidget: 'image',
      layout: 'column',
      required: false,
      printable: true,
      id: 'form-mTE1pkHNsFSH',
      order: 6,
    },
    'form-X2bGSDAEL1Eg': {
      id: 'form-X2bGSDAEL1Eg',
      title: '日期时间',
      description: '',
      type: 'string',
      widget: 'datePicker',
      placeholder: '请选择',
      printable: true,
      required: false,
      props: {
        format: 'YYYY-MM-DD HH:mm',
        precision: 'minute',
      },
      order: 7,
    },
    'form-WTHwi1OcppmL': {
      title: '多选',
      description: '',
      type: 'array',
      placeholder: '请选择',
      widget: 'checkbox',
      readOnlyWidget: 'checkbox',
      props: {
        options: [
          {
            label: '的方法发',
            value: '1',
          },
          {
            label: '冲刺冲刺',
            value: '2',
          },
          {
            label: '罚款罚款',
            value: '3',
          },
          {
            label: '飞飞飞',
            value: '4',
          },
          {
            label: '选项5',
            value: '5',
          },
        ],
      },
      required: false,
      printable: true,
      id: 'form-WTHwi1OcppmL',
      order: 8,
    },
  },
};
