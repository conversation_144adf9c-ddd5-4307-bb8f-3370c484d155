import type { Modifiers, UniqueIdentifier } from '@dnd-kit/core';
import {
  DndContext,
  DragOverlay,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import type { SortingStrategy } from '@dnd-kit/sortable';
import {
  arrayMove,
  rectSortingStrategy,
  SortableContext,
  useSortable,
} from '@dnd-kit/sortable';
import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import type { ItemType } from '../../types';
import { Item } from '.';

const defaultInitializer = (index: number) => index;

export function createRange<T = number>(
  length: number,
  initializer: (index: number) => any = defaultInitializer
): T[] {
  return [...new Array(length)].map((_, index) => initializer(index));
}

export interface Props {
  handle?: boolean;
  modifiers?: Modifiers;
  removable?: boolean;
  strategy?: SortingStrategy;
  forms: ItemType[];
  setForm: (data: ItemType[]) => void;
  removeFormItem: (id: string) => void;
  copyGroupItem?: (id: string) => void;
  setWidgetId: (id: string) => void;
  onAddGroupChild?: (groupId: string) => void;
  removeGroupChildItem?: (groupId: string, childId: string) => void;
}
export function Sortable({
  handle = false,
  modifiers,
  removable,
  strategy = rectSortingStrategy,
  forms,
  setForm,
  removeFormItem,
  copyGroupItem,
  setWidgetId,
  onAddGroupChild,
  removeGroupChildItem,
}: Props) {
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));
  const isFirstAnnouncement = useRef(true);
  const getIndex = (id: UniqueIdentifier) =>
    forms.findIndex((item) => item.id === id);
  const activeIndex = activeId ? getIndex(activeId) : -1;
  const handleRemove = removable
    ? (id: string) => removeFormItem(id)
    : // setItems((items) => items.filter((item) => item.id !== id))
      undefined;

  useEffect(() => {
    if (!activeId) {
      isFirstAnnouncement.current = true;
    }
  }, [activeId]);

  return (
    <DndContext
      modifiers={modifiers}
      onDragCancel={() => setActiveId(null)}
      onDragEnd={({ over }) => {
        console.log('🚀 ~ file: Sortable.tsx:214 ~ over:', over);
        setActiveId(null);

        if (over) {
          const overIndex = getIndex(over.id);
          if (activeIndex !== overIndex) {
            const d = arrayMove(forms, activeIndex, overIndex);
            setForm(d);
          }
        }
      }}
      onDragStart={({ active }) => {
        if (!active) {
          return;
        }
        setActiveId(active.id);
      }}
      sensors={sensors}
    >
      <div className="p-3">
        <SortableContext items={forms} strategy={strategy}>
          <ol className="flex flex-col gap-2">
            {forms.map((value, index) => (
              <SortableItem
                handle={handle}
                id={value.id}
                index={index}
                key={value.id}
                onAddGroupChild={onAddGroupChild}
                onCopyGroup={copyGroupItem}
                onRemove={handleRemove}
                removeGroupChildItem={removeGroupChildItem}
                setWidgetId={setWidgetId}
                value={value}
              />
            ))}
          </ol>
        </SortableContext>
      </div>
      {createPortal(
        <DragOverlay>
          {activeId && activeIndex >= 0 && forms[activeIndex] && (
            <Item
              dragOverlay
              handle={handle}
              setWidgetId={setWidgetId}
              value={forms[activeIndex]}
            />
          )}
        </DragOverlay>,
        document.body
      )}
    </DndContext>
  );
}

interface SortableItemProps {
  id: UniqueIdentifier;
  value: ItemType;
  index: number;
  handle: boolean;
  onRemove?(id: UniqueIdentifier): void;
  onCopyGroup?(id: string): void;
  setWidgetId: (id: string) => void;
  onAddGroupChild?: (groupId: string) => void;
  removeGroupChildItem?: (groupId: string, childId: string) => void;
}

export function SortableItem({
  handle,
  id,
  value,
  index,
  onRemove,
  onCopyGroup,
  setWidgetId,
  onAddGroupChild,
  removeGroupChildItem,
}: SortableItemProps) {
  const {
    attributes,
    isDragging,
    isSorting,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
  } = useSortable({
    id,
    animateLayoutChanges: undefined,
    disabled: false,
  });

  return (
    <Item
      data-id={id}
      data-index={index}
      dragging={isDragging}
      handle={handle}
      handleProps={
        handle
          ? {
              ref: setActivatorNodeRef,
            }
          : undefined
      }
      listeners={listeners}
      onAddGroupChild={onAddGroupChild}
      onCopyGroup={onCopyGroup}
      onRemove={onRemove ? (itemId: string) => onRemove(itemId) : undefined}
      ref={setNodeRef}
      removeGroupChildItem={removeGroupChildItem}
      setWidgetId={setWidgetId}
      sorting={isSorting}
      value={value}
      {...attributes}
    />
  );
}
