import 'plyr-react/plyr.css';

import type { APITypes } from 'plyr-react';
import { useRef } from 'react';
import ReactPlayer from 'react-player';

const PlyrComponent = ({ videoId = '' }) => {
  const ref = useRef<APITypes>(null);

  return (
    <ReactPlayer
      controls
      style={{
        maxWidth: '100%',
        maxHeight: '60vh',
      }}
      url={videoId}
      width="auto"
    />
  );
};

export default PlyrComponent;
