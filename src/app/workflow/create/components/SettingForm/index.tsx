import { Dialog, Form, Switch } from 'antd-mobile';
import type { FormInstance } from 'antd-mobile/es/components/form';
import React, { memo } from 'react';

import { useWorkflowStore } from '@/store/useWorkflowStore';

function SettingForm() {
  const settingForm = useWorkflowStore((state) => state.settingForm);
  const setSettingForm = useWorkflowStore((state) => state.setSettingForm);
  const [form] = Form.useForm();
  const formRef = React.useRef<FormInstance>(null);

  const onFinish = (values: any) => {
    Dialog.alert({
      content: <pre>{JSON.stringify(values, null, 2)}</pre>,
    });
  };

  return (
    <Form
      form={form}
      layout="horizontal"
      mode="card"
      onFinish={onFinish}
      ref={formRef}
      style={{
        '--prefix-width': '240px',
      }}
    >
      <Form.Item
        childElementPosition="right"
        disabled
        label="审批人离职自动交接给管理员"
        name="delivery"
      >
        <Switch defaultChecked />
      </Form.Item>
      <Form.Item
        childElementPosition="right"
        label="是否允许撤回"
        name="delivery"
      >
        <Switch
          defaultChecked={settingForm.allowCancel}
          onChange={(value) => {
            setSettingForm({ ...settingForm, allowCancel: value });
          }}
        />
      </Form.Item>
      <Form.Item
        childElementPosition="right"
        help="若开启，所有的办理和审批同意都需要签字"
        label="审批时需要签名"
        name="delivery"
      >
        <Switch
          defaultChecked={settingForm.sign}
          onChange={(value) => {
            setSettingForm({ ...settingForm, sign: value });
          }}
        />
      </Form.Item>
    </Form>
  );
}

export default memo(SettingForm);
