'use client';

import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from '@dnd-kit/modifiers';
import { verticalListSortingStrategy } from '@dnd-kit/sortable';
import { NavBar, Popup, Tabs } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getApproval, getTemplate } from '@/api/approval';
import { PiPlusCircle } from '@/components/Icons';
import { useWorkflowStore } from '@/store/useWorkflowStore';
import { nanoid, postMessage } from '@/utils';

import { Sortable } from './components';
import BaseForm from './components/BaseForm';
import { processJson, properties } from './components/data';
import ProcessDesign from './components/ProcessDesign';
import SettingForm from './components/SettingForm';
import Submit from './components/Submit';
import WidgetSetting from './components/WidgetSetting';
import WidgetsList from './components/WidgetsList';

export default function Index() {
  const searchParams = useSearchParams();
  const modelId = searchParams?.get('modelId');
  const templateId = searchParams?.get('templateId');
  const isPreview = searchParams?.get('isPreview');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const addFormItem = useWorkflowStore((state) => state.addFormItem);
  const forms = useWorkflowStore((state) => state.forms);
  const setProcess = useWorkflowStore((state) => state.setProcess);
  const setBaseForm = useWorkflowStore((state) => state.setBaseForm);
  const setForm = useWorkflowStore((state) => state.setForm);
  const setSettingForm = useWorkflowStore((state) => state.setSettingForm);
  const removeFormItem = useWorkflowStore((state) => state.removeFormItem);
  const widgetId = useWorkflowStore((state) => state.widgetId);
  const setWidgetId = useWorkflowStore((state) => state.setWidgetId);
  const updateFormItem = useWorkflowStore((state) => state.updateFormItem);
  const addGroupChildItem = useWorkflowStore(
    (state) => state.addGroupChildItem
  );
  const updateGroupChildItem = useWorkflowStore(
    (state) => state.updateGroupChildItem
  );
  const removeGroupChildItem = useWorkflowStore(
    (state) => state.removeGroupChildItem
  );
  const _copyGroupItem = useWorkflowStore((state) => state.copyGroupItem);

  const [activeIndex, setActiveIndex] = useState('1');
  const [formItemPopupVisible, setFormItemPopupVisible] = useState(false);
  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null); // 当前要添加控件的分组ID

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (forms.length === 0) {
      // 编辑数据
      if (modelId) {
        getApproval(modelId).then((res: any) => {
          const {
            modelName,
            iconUrl,
            remark,
            groupId,
            form,
            process,
            settings,
          } = res;
          setBaseForm({
            name: modelName,
            iconUrl,
            remark,
            groupId,
          });
          if (form && typeof form === 'object') {
            const { properties } = form;
            const formSchema = Object.keys(properties).map((key) => ({
              ...properties[key],
              id: key,
            }));
            setForm(formSchema.sort((a, b) => a.order - b.order));
          }
          if (
            process &&
            typeof process === 'object' &&
            Object.keys(process).length > 0
          ) {
            setProcess(process);
          }
          setSettingForm(settings);
        });
      } else if (templateId) {
        getTemplate(templateId).then((res: any) => {
          const { templateName, iconUrl, description, form } = res;
          setBaseForm({
            name: templateName,
            iconUrl,
            remark: description,
            groupId: '', // 需清空分组
          });
          if (form && typeof form === 'object') {
            const { properties } = form;
            const formSchema = Object.keys(properties).map((key) => ({
              ...properties[key],
              id: key,
            }));
            console.log('🚀 ~ file: page.tsx:74 ~ formSchema:', formSchema);
            setForm(formSchema.sort((a, b) => a.order - b.order));
          }
          setProcess(processJson);
        });
      } else {
        // 初始数据
        // 测试全部表单类型取消下面注释
        // setForm(formItems);
        setForm([
          {
            id: `form-${nanoid(12)}`,
            ...(properties.input || {}),
          },
        ]);
        setProcess(processJson);
      }
    }
  }, []);

  return (
    <div className="flex h-screen flex-col bg-[#F7F9FF]">
      {' '}
      setProcess modelId {/* 初始数据 // 测试全部表单类型取消下面注释 // */}
      setForm(formItems); setForm
      <div className="fixed top-0 z-10 w-full bg-white">
        <NavBar
          onBack={() => {
            postMessage({ goBack: 1 });
          }}
        >
          <span className="font-bold">{modelId ? '修改审批' : '创建审批'}</span>
        </NavBar>
        <Tabs
          activeLineMode="fixed"
          onChange={(key) => {
            setActiveIndex(key);
          }}
          style={{
            '--fixed-active-line-width': '30px',
            '--content-padding': '0',
            '--active-line-height': '4px',
            '--active-line-color':
              'linear-gradient(90deg, #31C3FF 0%, #4E78FF 100%)',
            '--active-line-border-radius': '2px',
          }}
        >
          <Tabs.Tab className="w-1/3" forceRender key="1" title="基础信息" />
          <Tabs.Tab className="w-1/3" forceRender key="2" title="流程设计" />
          <Tabs.Tab className="w-1/3" forceRender key="3" title="设置" />
        </Tabs>
      </div>
      <div className="flex flex-1 flex-col overflow-scroll pt-[180px]">
        {activeIndex === '1' && (
          <div className="flex flex-1 flex-col overflow-scroll bg-[#F7F9FF] ">
            <BaseForm />
            <div className="px-4 text-base text-stone-400">表单信息</div>
            <Sortable
              copyGroupItem={_copyGroupItem}
              forms={forms}
              handle
              modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
              onAddGroupChild={(groupId: string) => {
                setCurrentGroupId(groupId);
                setFormItemPopupVisible(true);
              }}
              removable
              removeFormItem={removeFormItem}
              removeGroupChildItem={removeGroupChildItem}
              setForm={setForm}
              setWidgetId={setWidgetId}
              strategy={verticalListSortingStrategy}
            />
            <button
              className="mb-4 flex items-center justify-center"
              onClick={() => {
                setFormItemPopupVisible(true);
              }}
              type="button"
            >
              <PiPlusCircle
                fontSize={24}
                style={{ color: 'var(--adm-color-primary)' }}
              />
              <span className="primary-color ml-1 text-base">添加控件</span>
            </button>
          </div>
        )}
        {activeIndex === '2' && <ProcessDesign />}
        {activeIndex === '3' && <SettingForm />}
      </div>
      {!isPreview && (
        <Submit modelId={modelId || ''} templateId={templateId || '0'} />
      )}
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          backgroundColor: '#fff',
        }}
        onMaskClick={() => {
          676;
          setFormItemPopupVisible(false);
          setCurrentGroupId(null);
        }}
        visible={formItemPopupVisible}
      >
        <div className="mt-3 text-center text-bold text-lg">控件库</div>
        <WidgetsList
          filterTypes={currentGroupId ? (type) => type !== 'group' : undefined}
          onSelect={(type: string) => {
            setFormItemPopupVisible(false);

            const newItem = {
              id: `form-${nanoid(12)}`,
              ...(properties[type] || {}),
            };

            if (currentGroupId) {
              // 添加到分组内
              addGroupChildItem(currentGroupId, newItem);
              setCurrentGroupId(null);
            } else {
              // 添加到表单根级别
              addFormItem(newItem);
            }
          }}
        />
      </Popup>
      <Popup
        bodyStyle={{ width: '100vw' }}
        position="right"
        visible={!!widgetId}
      >
        <WidgetSetting
          forms={forms}
          setWidgetId={setWidgetId}
          updateFormItem={updateFormItem}
          updateGroupChildItem={updateGroupChildItem}
          widgetId={widgetId}
        />
      </Popup>
    </div>
  );
}
