'use client';

import './page.css';

import { AnimatePresence, motion } from 'framer-motion';
import Head from 'next/head';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getCardsInfo, postCards } from '@/api/member';
import Layout from '@/components/layout/Layout';

export default function Home() {
  // 获取参数
  const searchParams = useSearchParams() as any;
  const [cardId] = useState<any>(searchParams.get('cardId') ?? '0');
  const [isOpened, setIsOpened] = useState(false);
  const [cardInfo, setCardInfo] = useState({});
  useEffect(() => {
    // 保存原来的标题
    const originalTitle = document.title;
    // 设置新标题
    document.title = '掌心会员';

    // 组件卸载时恢复原标题
    return () => {
      document.title = originalTitle;
    };
  }, []);
  const handleClaim = () => {
    if (isOpened) return;
    postCards({
      cardId
    }).then(() => {
      setIsOpened(true);
    });
  };

  const getRemainingDays = (endTime: string) => {
    if (!endTime) return 0;
    const now = new Date();
    // 将当前时间设置为当天的开始（00:00:00）
    now.setHours(0, 0, 0, 0);
    const endTimeDate = new Date(endTime);
    // 将结束时间设置为当天的结束（23:59:59）
    endTimeDate.setHours(23, 59, 59, 999);
    const diffTime = endTimeDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const getEndTime = (days: number) => {
    const now = new Date();
    const endTime = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    return endTime
      .toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
      .replace(/\//g, '-');
  };
  useEffect(() => {
    getCardsInfo({ cardId }).then((res) => {
      setCardInfo(res.card ?? {});
      if (res.card?.collectTime) {
        setIsOpened(true);
      }
    });
  }, [cardId]);
  return (
    <Layout>
      <Head>
        <title>掌心会员</title>
      </Head>
      <div className="min-h-screen bg-gray-100">
        <div className="flex flex-col items-center">
          <h2 className="my-4 text-xl font-medium">{cardInfo?.user?.name}</h2>
          <p className="mb-12 text-gray-600">
            送你掌心会员周卡({cardInfo.days}天)
          </p>
          <motion.div
            animate={{ y: isOpened ? 40 : 0 }}
            className="relative mb-16 aspect-[2/1] min-h-[400px] w-full max-w-md"
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
          >
            <div className="absolute inset-0 left-[10%] w-4/5 overflow-hidden rounded-lg bg-gradient-to-br from-[#F4E9D7] to-[#E5D5BC] shadow-lg" />

            {/* 遮盖 */}
            {isOpened && (
              <div className="[10%] absolute inset-x-0 bottom-0 left-[10%] z-[22] h-3/5 w-4/5 bg-gradient-to-br from-[#F4E9D7] to-[#E5D5BC] shadow-lg" />
            )}

            {/* 信封盖 */}
            <motion.div
              animate={{
                rotateX: isOpened ? 180 : 0,
                y: isOpened ? 100 : 0,
                scale: isOpened ? 0 : 1,
                zIndex: isOpened ? -1 : 6
              }}
              className="absolute inset-x-0 left-[10%] top-0 h-[51%] w-4/5 origin-top"
              initial={{ rotateX: 0 }}
              style={{
                transformStyle: 'preserve-3d',
                perspective: '1000px',
                padding: '0 4px'
              }}
              transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            >
              <div className="relative size-full overflow-hidden">
                <div
                  className="absolute inset-0 bg-gradient-to-b from-[#E5D5BC] to-[#D4C4AB]"
                  style={{
                    clipPath: 'polygon(0 0, 100% 0, 50% 100%, 0 0)',
                    borderBottom: '1px solid rgba(212, 196, 171, 0.5)'
                  }}
                />
                <div
                  className="absolute inset-0"
                  style={{
                    background:
                      'linear-gradient(to bottom, transparent, rgba(212, 196, 171, 0.2))',
                    clipPath: 'polygon(0 0, 100% 0, 50% 100%, 0 0)'
                  }}
                />
                <div
                  className="absolute left-1/2 top-2 -translate-x-1/2 whitespace-nowrap text-[26px] font-semibold tracking-wide text-[#B8860B]"
                  style={{
                    textShadow: '0 1px 2px rgba(218, 165, 32, 0.4)',
                    opacity: 0.85
                  }}
                >
                  {getRemainingDays(cardInfo.endTime) <= 0
                    ? '已过期'
                    : `还有${getRemainingDays(cardInfo.endTime)}天过期，点击立即领取`}
                </div>
              </div>
            </motion.div>

            {/* 印章 */}
            <motion.div
              animate={{
                opacity: isOpened ? 0 : 1,
                scale: isOpened ? 0.8 : 1
              }}
              className="vipBadge"
              onClick={handleClaim}
            >
              <div className="waxSeal">
                <div className="sealPattern">
                  <div className="sealText">点击开启</div>
                  <div className="sealLine" />
                  <div className="sealYear">VIP</div>
                </div>
              </div>
            </motion.div>
            {/* 卡片 */}
            <AnimatePresence>
              {isOpened && (
                <motion.div
                  animate={{
                    y: -45,
                    opacity: 1,
                    scale: 1,
                    transition: {
                      delay: 0.2,
                      type: 'spring',
                      damping: 12,
                      stiffness: 100
                    }
                  }}
                  className="z-4 absolute left-[15%] w-[70%]"
                  initial={{ y: 0, opacity: 0, scale: 0.8 }}
                >
                  <div className="relative overflow-hidden rounded-xl bg-[#1a1a2e] px-6 py-4 text-white">
                    <div className="absolute right-0 top-0 size-20 -translate-x-8 -translate-y-8 rounded-full bg-[rgba(255,215,0,0.1)]" />
                    <div className="absolute bottom-0 left-0 size-32 -translate-x-16 translate-y-16 rounded-full bg-[rgba(255,215,0,0.05)]" />
                    <div className="absolute bottom-4 left-4 -rotate-45 text-6xl text-[rgba(255,215,0,0.1)]">
                      VIP
                    </div>
                    <div className="relative z-10">
                      <div className="mb-2 flex items-start">
                        <h3 className="text-sm font-bold text-[#F0C16D]">
                          尊享掌心会员周卡
                        </h3>
                        <span className="ml-2">✨</span>
                      </div>

                      <div className="space-y-2">
                        <p className="text-sm text-[#F4F1DE]">
                          💎 优享 · 7+特权
                        </p>
                        <p className="text-xs text-[#A8A8A8]">
                          有效截止日期：{getEndTime(cardInfo.days)}
                        </p>
                        <p className="text-right text-xs text-[#DBB563]">
                          ID:{cardInfo.accId}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          <div className="w-full max-w-md space-y-4 p-5 text-sm text-gray-500">
            <h3 className="font-medium">领取须知：</h3>
            <ol className="list-decimal space-y-2 pl-5">
              <li>该礼品卡仅限非掌心会员用户使用,且每人每月最多领取1张;</li>
              <li>单个用户每月仅可领取1次,不随赠送者数量增加而变化。</li>
            </ol>
          </div>
        </div>
      </div>
    </Layout>
  );
}
