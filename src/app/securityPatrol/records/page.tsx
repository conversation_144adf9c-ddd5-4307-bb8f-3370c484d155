'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>icker,
  ImageViewer,
  InfiniteScroll,
  Modal,
  Picker,
  Popup,
  PullToRefresh,
  SearchBar,
  Selector,
  SwipeAction,
  Toast
} from 'antd-mobile';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { ChevronRight, Filter } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import {
  deleteVisitRecord,
  getPatrolRecords,
  getPositions
} from '@/api/securityPatrol';

// 引入巡视点接口类型
interface PatrolPositionInfo {
  posCode?: string;
  posId: string;
  posName?: string;
  qrcodeUrl?: string;
  status?: number;
}

// 更新巡检记录接口以匹配API返回结构
interface PatrolRecord {
  /**
   * 巡视时间 (Unix时间戳, 秒，对应数据库 patrol.create_time)
   */
  createTime: number;
  /**
   * 巡视说明 (对应数据库 patrol.description)
   */
  description: string;
  /**
   * 巡视记录ID (对应数据库 patrol.patrol_id)
   */
  patrolId: string;
  /**
   * 巡视人ID (对应数据库 patrol.patrolman_id)
   */
  patrolmanId?: number;
  /**
   * 巡视人姓名
   */
  patrolmanName?: string;
  /**
   * 图片URL (对应数据库 patrol.photo_url)
   */
  photoUrl?: string;
  /**
   * 巡视点名称 (需要从 patrol_position 表关联查询得到)
   */
  posName: string;
  /**
   * 巡视点编号 (需要从 patrol_position 表关联查询得到)
   */
  posCode: string;
  /**
   * 巡检人
   */
  staffName?: string;
  /**
   * 状态 (对应数据库 patrol.status, 使用 PatrolRecordStatus 枚举) 1-正常 2-异常
   */
  status: number;
}

// API 响应接口
interface PatrolRecordsResponse {
  items: PatrolRecord[];
  total?: number;
}

interface PositionsResponse {
  items: PatrolPositionInfo[];
  total?: number;
}

export default function PatrolRecordsPage() {
  const router = useRouter();
  const [records, setRecords] = useState<PatrolRecord[]>([]);
  const [searchText, setSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');

  // 已应用的筛选条件（用于实际筛选）
  const [appliedStatus, setAppliedStatus] = useState<string>('all');
  const [appliedStartDate, setAppliedStartDate] = useState<Date | null>(null);
  const [appliedEndDate, setAppliedEndDate] = useState<Date | null>(null);
  const [appliedPosition, setAppliedPosition] = useState<string>('all');

  // 临时筛选条件（用于筛选面板中的选择）
  const [tempStatus, setTempStatus] = useState<string>('all');
  const [tempStartDate, setTempStartDate] = useState<Date | null>(null);
  const [tempEndDate, setTempEndDate] = useState<Date | null>(null);
  const [tempPosition, setTempPosition] = useState<string>('all');

  const [showFilter, setShowFilter] = useState(false);
  const [startDateVisible, setStartDateVisible] = useState(false);
  const [endDateVisible, setEndDateVisible] = useState(false);
  const [positionPickerVisible, setPositionPickerVisible] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const pageSize = 50;

  // 巡视点数据
  const [positions, setPositions] = useState<PatrolPositionInfo[]>([]);

  // 图片预览相关状态
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const statusOptions = [
    { label: '全部状态', value: 'all' },
    { label: '正常', value: '1' },
    { label: '异常', value: '2' }
  ];

  // 巡视点选项（用于Picker）
  const positionPickerData = [
    [
      { label: '全部巡视点', value: 'all' },
      ...positions.map((pos) => ({
        label: `${pos.posCode} - ${pos.posName}`,
        value: pos.posId.toString() || 'unknown'
      }))
    ]
  ];

  // 防抖处理搜索文本
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 500); // 500ms 防抖延迟

    return () => {
      clearTimeout(timer);
    };
  }, [searchText]);

  // 获取巡检记录
  const fetchPatrolRecords = useCallback(
    async (pageNum = 1, isRefresh = false) => {
      try {
        // 构建请求参数
        const params = {
          pageNum,
          pageSize,
          sort: 0, // 默认倒序
          ...(appliedStatus !== 'all' && {
            status: Number.parseInt(appliedStatus, 10)
          }),
          ...(appliedStartDate && {
            startTime: format(appliedStartDate, 'yyyy-MM-dd')
          }),
          ...(appliedEndDate && {
            endTime: format(appliedEndDate, 'yyyy-MM-dd')
          }),
          ...(appliedPosition !== 'all' && { posId: appliedPosition }),
          ...(debouncedSearchText && {
            posName: debouncedSearchText // 使用防抖后的搜索文本
          })
        };

        const response = (await getPatrolRecords(
          params
        )) as unknown as PatrolRecordsResponse;

        if (isRefresh || pageNum === 1) {
          setRecords(response.items || []);
        } else {
          setRecords((prev) => [...prev, ...(response.items || [])]);
        }

        setHasMore((response.items?.length || 0) === pageSize);
        return true; // 请求成功返回true
      } catch (error) {
        // console.error('获取巡检记录失败:', error);
        Toast.show({
          icon: 'fail',
          content: '获取数据失败'
        });
        return false; // 请求失败返回false
      }
    },
    [
      appliedStatus,
      appliedStartDate,
      appliedEndDate,
      appliedPosition,
      debouncedSearchText // 使用防抖后的搜索文本作为依赖
    ]
  );

  // 获取巡视点列表
  const fetchPositions = useCallback(async () => {
    try {
      const response = (await getPositions({})) as unknown as PositionsResponse;

      setPositions(response.items || []);
    } catch (error) {
      // console.error('获取巡视点失败:', error);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '安防巡查记录';
    }
    fetchPositions();
  }, [fetchPositions]);

  // 初始加载和筛选条件变化时获取数据
  useEffect(() => {
    setPage(1);
    fetchPatrolRecords(1, true);
  }, [fetchPatrolRecords]);

  // 搜索处理函数 - 只更新搜索文本，不立即触发搜索
  const handleSearch = useCallback((value: string) => {
    setSearchText(value);
    // 移除立即调用 fetchPatrolRecords 的逻辑
  }, []);

  // 移除本地筛选，直接使用从API获取的数据
  const filteredRecords = records;

  const handleRecordClick = (record: PatrolRecord) => {
    router.push(`/securityPatrol/record/${record.patrolId}`);
  };

  const handleDelete = (recordId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这条巡检记录吗？',
      onConfirm: async () => {
        // TODO: 调用删除API
        await deleteVisitRecord({ patrolId: recordId });
        setRecords((prev) => prev.filter((r) => r.patrolId !== recordId));
        Toast.show({
          icon: 'success',
          content: '删除成功'
        });
      }
    });
  };

  // 下拉刷新
  const onRefresh = async () => {
    setPage(1);
    await fetchPatrolRecords(1, true);
    Toast.show('刷新成功');
  };

  // 上拉加载更多
  const loadMore = async () => {
    if (!hasMore) return;

    const nextPage = page + 1;
    const success = await fetchPatrolRecords(nextPage, false);
    if (success) {
      setPage(nextPage);
    }
  };

  const getStatusColor = (status: number) => {
    return status === 1 ? '#00b578' : '#ff3141';
  };

  const getStatusText = (status: number) => {
    return status === 1 ? '正常' : '异常';
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000); // 转换为毫秒
    const now = new Date();

    // 获取年月日部分进行比较，忽略时分秒
    const dateOnly = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );
    const nowOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 计算日期差（天数）
    const days = Math.floor(
      (nowOnly.getTime() - dateOnly.getTime()) / (1000 * 60 * 60 * 24)
    );

    // 使用date-fns格式化时间部分，包含秒
    const timeStr = format(date, 'HH:mm:ss', { locale: zhCN });

    if (days === 0) return `今天 ${timeStr}`;
    if (days === 1) return `昨天 ${timeStr}`;
    if (days === 2) return `前天 ${timeStr}`;

    // 其他时间显示完整日期和时间
    return format(date, 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN });
  };

  const clearFilters = () => {
    setTempStatus('all');
    setTempStartDate(null);
    setTempEndDate(null);
    setTempPosition('all');
    Toast.show('筛选条件已清空');
  };

  const applyFilters = () => {
    setAppliedStatus(tempStatus);
    setAppliedStartDate(tempStartDate);
    setAppliedEndDate(tempEndDate);
    console.log('tempPosition', tempPosition);
    setAppliedPosition(tempPosition);
    setShowFilter(false);
    setPage(1);
    Toast.show('筛选条件已应用');
  };

  const cancelFilters = () => {
    // 恢复临时状态为当前已应用的状态
    setTempStatus(appliedStatus);
    setTempStartDate(appliedStartDate);
    setTempEndDate(appliedEndDate);
    setTempPosition(appliedPosition);
    setShowFilter(false);
  };

  // 打开筛选面板时，将已应用的状态复制到临时状态
  const openFilter = () => {
    setTempStatus(appliedStatus);
    setTempStartDate(appliedStartDate);
    setTempEndDate(appliedEndDate);
    setTempPosition(appliedPosition);
    setShowFilter(true);
  };

  // 处理图片点击预览
  const handleImagePreview = (imageUrl: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发列表项点击
    if (imageUrl) {
      setCurrentImages([imageUrl]);
      setCurrentImageIndex(0);
      setImageViewerVisible(true);
    }
  };

  // 获取当前选择的巡视点名称（用于显示）
  const getCurrentPositionLabel = () => {
    if (tempPosition === 'all') return '全部巡视点';
    const position = positions.find((pos) => pos.posId === tempPosition);
    return position
      ? `${position.posCode} - ${position.posName}`
      : '全部巡视点';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 搜索栏和筛选按钮 */}
      <div className="bg-white p-4 shadow-sm">
        <div className="flex items-center space-x-3">
          <div className="flex-1">
            <SearchBar
              placeholder="请输入巡检点名称"
              value={searchText}
              onChange={handleSearch}
              style={{
                '--height': '40px'
              }}
            />
          </div>
          <Button
            size="small"
            fill="outline"
            onClick={openFilter}
            className="flex items-center space-x-1 whitespace-nowrap"
          >
            <Filter size={16} />
            <span>筛选</span>
            {(appliedStatus !== 'all' ||
              appliedStartDate ||
              appliedEndDate ||
              appliedPosition !== 'all') && (
              <div className="absolute right-1 top-1 size-2 rounded-full bg-red-500" />
            )}
          </Button>
        </div>
      </div>
      {/* 巡检记录列表 */}
      <div className="px-4">
        <PullToRefresh onRefresh={onRefresh}>
          <div className="space-y-3 py-3">
            {filteredRecords.map((record) => (
              <SwipeAction
                key={record.patrolId}
                style={{
                  background: 'transparent',
                  overflow: 'hidden'
                }}
                closeOnAction={false}
                rightActions={[
                  {
                    key: 'delete',
                    text: '删除',
                    color: 'danger',
                    onClick: () => handleDelete(record.patrolId)
                  }
                ]}
              >
                <div
                  className="cursor-pointer rounded-xl bg-white p-4"
                  onClick={() => handleRecordClick(record)}
                >
                  <div className="flex items-center gap-3">
                    {/* 图片部分 */}
                    {record.photoUrl && (
                      <div
                        className="size-20 shrink-0 cursor-pointer overflow-hidden rounded-lg bg-gray-100"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (record.photoUrl) {
                            handleImagePreview(
                              record.photoUrl?.split(',')?.[0] || '',
                              e
                            );
                          }
                        }}
                      >
                        <img
                          src={record.photoUrl?.split(',')?.[0] || ''}
                          alt="巡检照片"
                          className="size-full object-cover transition-transform duration-200 hover:scale-105"
                        />
                      </div>
                    )}

                    {/* 内容部分 */}
                    <div className="min-w-0 flex-1">
                      <div className="mb-2 flex items-start justify-between">
                        <h3 className="truncate text-base font-medium text-gray-900">
                          {record.posName}
                        </h3>
                        <div className="ml-2 flex shrink-0 items-center gap-2">
                          <Badge
                            content={getStatusText(record.status)}
                            color={getStatusColor(record.status)}
                          />
                          <ChevronRight size={16} className="text-gray-400" />
                        </div>
                      </div>

                      <div className="space-y-1 text-sm text-gray-500">
                        <div className="flex items-center gap-4">
                          <span>
                            巡检员：
                            {record.staffName || record.patrolmanName || '未知'}
                          </span>
                        </div>
                        <div>时间：{formatDate(record.createTime)}</div>
                        {record.description && (
                          <div className="mt-2 line-clamp-2 text-xs text-gray-600">
                            {record.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </SwipeAction>
            ))}
          </div>
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
        </PullToRefresh>
      </div>

      {/* 右侧筛选抽屉 */}
      <Popup
        visible={showFilter}
        onMaskClick={cancelFilters}
        position="right"
        bodyStyle={{ width: '300px', padding: '20px' }}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">筛选条件</h3>
            <Button size="small" fill="none" onClick={clearFilters}>
              清空
            </Button>
          </div>

          {/* 状态筛选 */}
          <div>
            <h4 className="mb-3 text-sm font-medium text-gray-700">巡检状态</h4>
            <Selector
              options={statusOptions}
              value={[tempStatus]}
              onChange={(arr) => setTempStatus((arr[0] as string) || 'all')}
            />
          </div>

          {/* 巡视点筛选 */}
          <div>
            <h4 className="mb-3 text-sm font-medium text-gray-700">巡视点</h4>
            <div
              className="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-3"
              onClick={() => setPositionPickerVisible(true)}
            >
              <span className="text-sm text-gray-700">
                {getCurrentPositionLabel()}
              </span>
              <span className="text-gray-400">›</span>
            </div>
          </div>

          {/* 日期筛选 */}
          <div>
            <h4 className="mb-3 text-sm font-medium text-gray-700">时间范围</h4>
            <div className="space-y-3">
              <div
                className="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-3"
                onClick={() => setStartDateVisible(true)}
              >
                <span className="text-sm text-gray-700">
                  开始日期:
                  {tempStartDate
                    ? tempStartDate.toLocaleDateString()
                    : '请选择'}
                </span>
              </div>
              <div
                className="flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-3"
                onClick={() => setEndDateVisible(true)}
              >
                <span className="text-sm text-gray-700">
                  结束日期:
                  {tempEndDate ? tempEndDate.toLocaleDateString() : '请选择'}
                </span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3 pt-4">
            <Button block color="primary" onClick={applyFilters}>
              确定筛选
            </Button>
            <Button block fill="outline" onClick={cancelFilters}>
              取消
            </Button>
          </div>
        </div>
      </Popup>

      {/* 日期选择器 */}
      <DatePicker
        visible={startDateVisible}
        value={tempStartDate || new Date()}
        title="选择开始日期"
        min={new Date('2023-01-01')}
        max={tempEndDate || new Date()}
        onClose={() => setStartDateVisible(false)}
        onConfirm={(val) => {
          setTempStartDate(val);
          setStartDateVisible(false);
        }}
      />

      <DatePicker
        visible={endDateVisible}
        value={tempEndDate || new Date()}
        title="选择结束日期"
        min={tempStartDate || new Date('2023-01-01')}
        max={new Date()}
        onClose={() => setEndDateVisible(false)}
        onConfirm={(val) => {
          setTempEndDate(val);
          setEndDateVisible(false);
        }}
      />

      {/* 巡视点选择器 */}
      <Picker
        columns={positionPickerData}
        visible={positionPickerVisible}
        onClose={() => setPositionPickerVisible(false)}
        value={[tempPosition]}
        onConfirm={(val) => {
          console.log('onConfirm', val);
          setTempPosition(val[0] as string);
          setPositionPickerVisible(false);
          Toast.show('巡视点已选择');
        }}
        title="选择巡视点"
      />

      {/* 图片预览器 */}
      <ImageViewer
        image={currentImages[currentImageIndex] || ''}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
      />
    </div>
  );
}
