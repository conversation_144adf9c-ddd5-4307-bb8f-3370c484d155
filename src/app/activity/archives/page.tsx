'use client';
import clsx from 'clsx';
import {
  Bar<PERSON>hartBig,
  FileCheck2,
  PartyPopper,
  Rocket,
  Wand2,
} from 'lucide-react';
import { useEffect, useState } from 'react';

function page() {
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '成长档案';
    }
  }, []);

  const handleService = () => {};

  // 帮助函数：用于延迟动画效果
  const useAnimatedElements = (count) => {
    const [show, setShow] = useState(Array(count).fill(false));

    useEffect(() => {
      const timers = [];
      for (let i = 0; i < count; i++) {
        timers.push(
          setTimeout(() => {
            setShow((prev) => {
              const newShow = [...prev];
              newShow[i] = true;
              return newShow;
            });
          }, i * 200)
        ); // 每个元素延迟 200ms
      }
      return () => timers.forEach(clearTimeout);
    }, [count]);

    return show;
  };

  // 特性数据
  const features = [
    {
      icon: 'party-popper',
      title: '零成本使用',
      description: '免平台费，让每所学校都能轻松记录孩子的成长足迹！',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      iconColor: 'text-blue-500',
    },
    {
      icon: 'wand-2',
      title: '智能省时',
      description:
        '老师发动态→系统自动人脸识别生成孩子成长档案，工作量直降 90%！',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      iconColor: 'text-green-500',
    },
    {
      icon: 'bar-chart-big',
      title: '专业赋能',
      description: '内置发展评估体系，科学追踪成长轨迹，教学质量 UP！',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      iconColor: 'text-yellow-500',
    },
    {
      icon: 'file-check-2',
      title: '合规利器',
      description: '档案格式完全符合检查要求，迎检准备效率翻倍！',
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      iconColor: 'text-purple-500',
    },
  ];

  const animatedFeatures = useAnimatedElements(features.length);
  const animatedOffer = useAnimatedElements(1);

  return (
    <div>
      <div className="flex-grow overflow-y-auto bg-blue-50 pb-12">
        <div className="relative flex min-h-[300px] flex-col items-center justify-center p-4 pt-12 text-center text-white">
          <div
            className="absolute inset-0 h-full w-full bg-center bg-cover"
            style={{
              backgroundImage: 'url(/images/activity/archives/archives1.jpg)',
            }}
          >
            <div className="absolute inset-0 h-full w-full bg-black bg-opacity-40" />
          </div>
          <div className="relative z-10 animate-fade-in-up">
            <h1 className="mb-3 font-bold font-serif text-4xl leading-10 ">
              童年每一刻
              <br />
              成长全记录
            </h1>
            <p className="mb-4 font-sans text-lg opacity-90">
              专为幼儿园打造的智能成长档案解决方案
            </p>
          </div>
        </div>
        <div className="p-4">
          <div
            className={clsx(
              ' card-hover-effect mb-6 rounded-xl bg-gradient-to-r from-yellow-900 via-yellow-800 to-yellow-900 p-6 text-amber-100 shadow-xl',
              animatedOffer[0] ? 'animate-fade-in-up opacity-100' : 'opacity-0'
            )}
            style={{ animationDelay: `${features.length * 150}ms` }}
          >
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="mb-1 font-bold font-serif text-xl">
                  🌟 成本直降，福利加倍
                </h3>
                <p className="font-sans text-sm opacity-90">
                  学期末专属档案册打印优惠限时开放，行业最底价，助力园所省心又省力！
                </p>
              </div>
            </div>
          </div>
          <div className="mb-6 rounded-xl bg-white p-4 pb-0">
            <div className="p-4">
              <p className="mb-4 font-sans text-base">
                「掌心成长档案」运用前沿 AI 技术，用智能化工具生成宝贝成长档案，
                学期末给家长一份孩子成长的可视化珍贵纪念，提升园所专业度和家长满意度。
              </p>
            </div>
            <div className="space-y-5 pb-6">
              {features.map((feature, index) => (
                <div
                  className={clsx(
                    'card-hover-effect flex items-start space-x-4 rounded-xl p-5 shadow-lg',
                    feature.bgColor,
                    animatedFeatures[index]
                      ? 'animate-fade-in-up opacity-100'
                      : 'opacity-0'
                  )}
                  key={feature.title}
                  style={{ animationDelay: `${index * 150}ms` }} // 为Tailwind JIT 无法直接处理的动态延迟添加
                >
                  <div
                    className={clsx(
                      'rounded-full bg-white p-3 shadow-md',
                      feature.iconColor
                    )}
                  >
                    {feature.icon === 'party-popper' && (
                      <PartyPopper className="h-6 w-6" />
                    )}
                    {feature.icon === 'wand-2' && <Wand2 className="h-6 w-6" />}
                    {feature.icon === 'bar-chart-big' && (
                      <BarChartBig className="h-6 w-6" />
                    )}
                    {feature.icon === 'file-check-2' && (
                      <FileCheck2 className="h-6 w-6" />
                    )}
                  </div>
                  <div>
                    <h3
                      className={clsx(
                        'mb-1 font-bold font-serif text-lg',
                        feature.textColor
                      )}
                    >
                      {feature.title}
                    </h3>
                    <p
                      className={clsx(
                        'font-sans text-sm',
                        feature.textColor,
                        'opacity-80'
                      )}
                    >
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="fixed right-0 bottom-0 left-0 rounded-b-3xl p-4">
        <button
          className="flex w-full transform items-center justify-center space-x-2 rounded-xl bg-indigo-500/80 px-6 py-4 font-sans font-semibold text-white shadow-lg transition-all duration-300 hover:scale-[1.02] hover:bg-indigo-500 hover:shadow-xl"
          onClick={handleService}
          type="button"
        >
          <Rocket className="h-5 w-5" />
          <span>联系客服获取优惠</span>
        </button>
      </div>
    </div>
  );
}

export default page;
