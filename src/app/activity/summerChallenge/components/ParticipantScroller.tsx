'use client';

import { useEffect, useRef, useState } from 'react';

interface ParticipantScrollerProps {
  interval?: number;
  animationDuration?: number;
  className?: string;
}

const ParticipantScroller: React.FC<ParticipantScrollerProps> = ({
  interval = 2000,
  animationDuration = 1500,
  className = ''
}) => {
  // 姓氏数组
  const surnames = [
    '王',
    '李',
    '张',
    '陈',
    '刘',
    '杨',
    '黄',
    '周',
    '吴',
    '徐',
    '朱',
    '林',
    '何',
    '郭',
    '马',
    '罗',
    '高',
    '梁',
    '宋',
    '郑',
    '谢',
    '韩',
    '唐',
    '冯',
    '于',
    '董',
    '萧',
    '程',
    '曹',
    '袁',
    '邓',
    '许',
    '傅',
    '沈',
    '曾',
    '彭',
    '吕',
    '苏',
    '卢',
    '蒋',
    '蔡',
    '贾',
    '丁',
    '魏',
    '薛',
    '叶',
    '阎',
    '余',
    '潘',
    '杜',
    '戴',
    '夏',
    '钟',
    '汪',
    '田',
    '任',
    '姜',
    '范',
    '方',
    '石',
    '姚',
    '谭',
    '廖',
    '邹',
    '熊',
    '金',
    '陆',
    '郝',
    '孔',
    '白',
    '崔',
    '康',
    '毛',
    '邱',
    '秦',
    '江',
    '史',
    '顾',
    '侯',
    '邵',
    '孟',
    '龙',
    '万',
    '段',
    '漕',
    '钱',
    '汤',
    '尹',
    '黎',
    '易',
    '常',
    '武',
    '乔',
    '贺',
    '赖',
    '龚',
    '文',
    '庞',
    '樊',
    '兰',
    '殷'
  ];

  // 名字数组
  const givenNames = [
    '玲',
    '华',
    '雨',
    '明',
    '强',
    '丽',
    '军',
    '娟',
    '峰',
    '梅',
    '涛',
    '芳',
    '辉',
    '静',
    '斌',
    '霞',
    '刚',
    '红',
    '龙',
    '兰',
    '飞',
    '萍',
    '伟',
    '琴',
    '超',
    '燕',
    '鹏',
    '花',
    '东',
    '云',
    '海',
    '莲',
    '山',
    '秀',
    '波',
    '君',
    '亮',
    '英',
    '磊',
    '娜',
    '勇',
    '洁',
    '凯',
    '艳',
    '豪',
    '慧',
    '威',
    '珍',
    '翔',
    '蓉',
    '昊',
    '倩',
    '宇',
    '颖',
    '阳',
    '婷',
    '浩',
    '欣',
    '晨',
    '瑶',
    '轩',
    '薇',
    '宁',
    '雅',
    '昕',
    '琪',
    '睿',
    '萱',
    '航',
    '妍',
    '涵',
    '宸',
    '悦',
    '泽',
    '彤',
    '恒',
    '怡',
    '博',
    '琳',
    '妮',
    '晖',
    '璇',
    '煜',
    '萌',
    '哲',
    '蕊',
    '晴',
    '瑞',
    '轩',
    '雯',
    '晨',
    '宇',
    '昊'
  ];

  const relations = ['妈妈', '爸爸'];

  const generateRandomName = (): string => {
    const randomSurname = surnames[Math.floor(Math.random() * surnames.length)];
    const randomGivenName =
      givenNames[Math.floor(Math.random() * givenNames.length)];
    const randomRelation =
      relations[Math.floor(Math.random() * relations.length)];

    return `${randomSurname}*${randomGivenName}${randomRelation}`;
  };

  const [currentName, setCurrentName] = useState(() => generateRandomName());
  const [previousName, setPreviousName] = useState('');
  const [showAnimation, setShowAnimation] = useState(false);
  const [participantCount, setParticipantCount] = useState(1);
  const nameRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const intervalId = setInterval(() => {
      // 保存当前名字作为旧名字
      setPreviousName(currentName);
      setCurrentName(generateRandomName());
      setShowAnimation(true);
      setParticipantCount((prev) => prev + 1);
      setTimeout(() => {
        setShowAnimation(false);
        setPreviousName('');
      }, animationDuration);
    }, interval);

    return () => clearInterval(intervalId);
  }, [interval, animationDuration, currentName]);

  return (
    <div className={`relative h-12 w-full ${className}`}>
      <div className="absolute bottom-0 flex h-8 w-full items-center">
        <div
          ref={nameRef}
          className="whitespace-nowrap rounded-full bg-gradient-to-r from-blue-100 to-green-100 px-3 py-1 text-xs font-medium text-gray-700"
        >
          {currentName}已报名
        </div>
      </div>

      {showAnimation && previousName && (
        <div
          className="pointer-events-none absolute left-0 top-0 flex items-center"
          style={{
            animation: `floatUpAndFade ${animationDuration}ms ease-out forwards`
          }}
        >
          <div className="flex items-center gap-1 rounded-full  px-2 py-1 ">
            <span className="text-xs font-medium text-gray-600">
              {previousName}已报名
            </span>
            <span className="text-xs font-bold text-red-500">+1</span>
          </div>
        </div>
      )}

      <style jsx global>{`
        @keyframes floatUpAndFade {
          0% {
            opacity: 0;
            transform: translateY(0) scale(0.8);
          }
          20% {
            opacity: 1;
            transform: translateY(-10px) scale(1);
          }
          80% {
            opacity: 0.8;
            transform: translateY(-25px) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(-35px) scale(0.7);
          }
        }
      `}</style>
    </div>
  );
};

export default ParticipantScroller;
