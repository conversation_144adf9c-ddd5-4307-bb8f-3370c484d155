'use client';

import { Toast } from 'antd-mobile';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { activitySign } from '@/api/summerActivity';
import wechat from '@/assets/images/wechat.png';
import course from '@/public/images/activity/summerChallenge/course.webp';
import success from '@/public/images/activity/summerChallenge/success.png';
import {
  appPay,
  canCallAppPay,
  getMessage,
  hinaTrack,
  navigationToNativePage
} from '@/utils';

// 在文件顶部添加导入
import ParticipantScroller from '../components/ParticipantScroller';

export const getDynamicParticipantCount = (): string => {
  try {
    const startDate = new Date('2025-06-30');
    const currentDate = new Date();
    const baseCount = 999;
    const daysDiff = Math.floor(
      (currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    let totalCount = baseCount;
    for (let i = 0; i <= daysDiff; i++) {
      const dateStr = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
        .toISOString()
        .split('T')[0];
      let hash = 0;
      if (dateStr) {
        for (let j = 0; j < dateStr.length; j++) {
          const char = dateStr.charCodeAt(j);
          hash = (hash << 5) - hash + char;
          hash &= hash;
        }
      }
      // 生成250-1500之间的随机增量膨胀85倍
      const dailyIncrease = ((Math.abs(hash) % 26) + 5) * 85;
      totalCount += dailyIncrease;
    }
    if (totalCount >= 1000) {
      const roundedCount = Math.floor(totalCount / 100) * 100;
      return `${roundedCount}+人`;
    }
    return `${totalCount}人`;
  } catch (error) {
    console.error('获取动态参与人数失败', error);
    return '999+人';
  }
};

const SummerPayPage = () => {
  const searchParams = useSearchParams();
  const [selectedPayMethod, setSelectedPayMethod] = useState('1'); // 默认支付宝

  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const orderIdRef = useRef(''); // 使用useRef替代let变量
  const taskId = searchParams.get('taskId'); // 亲子任务Id
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '支付';
    }
  }, []);

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data);
      if ('pay' in data) {
        if (data.pay) {
          // 支付成功，显示弹窗
          hinaTrack('summer_checkinAct_pay_success');
          setShowSuccessModal(true);
        } else {
          navigationToNativePage(
            `rn://SunshineStack?initialRoute=VideoServiceOrderDetailScreen&orderId=${orderIdRef.current}`
          );
          // 支付失败，显示跳转到订单详情页面
          router.back();
        }
      }
      console.log('获取到RN的通知 onMessage ', JSON.stringify(data));
    } catch (error) {
      console.error('解析失败', error);
    }
  };
  useEffect(() => {
    getMessage(onMessage);

    // 清理函数，移除事件监听器
    return () => {
      window.removeEventListener('message', onMessage, false);
      document.removeEventListener('message', onMessage, false);
    };
  }, [onMessage]);

  // 支付方式选择
  const handlePayMethodSelect = (method: string) => {
    setSelectedPayMethod(method);
    hinaTrack('summer_pay_method_select', {
      pay_method: method
    });
  };

  // 立即支付
  const handlePay = () => {
    hinaTrack('summer_checkinAct_pay_click');
    if (isLoading) return;
    if (canCallAppPay() === false) {
      Toast.show({
        content: '请更新App版本后再试！'
      });
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    activitySign({ payMethod: selectedPayMethod })
      .then((res) => {
        console.log('🚀 ~ handlePay ~ res:', res);
        orderIdRef.current = res.orderId; // 直接赋值给ref.current
        appPay({ payMethod: selectedPayMethod, orderNo: res.orderNo });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // 关闭成功弹窗
  const handleCloseSuccessModal = () => {
    router.back();
    setShowSuccessModal(false);
  };

  // 去打卡
  const handleGoClockIn = () => {
    setShowSuccessModal(false);
    router.back();
    // 这里可以添加跳转到打卡页面的逻辑
    navigationToNativePage(
      `rn://BabyTaskStack?initialRoute=SubmitListScreen&taskId=${taskId}`
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 活动奖励说明 */}
      <div className="bg-white p-4">
        <h1 className="mb-4 text-lg font-bold text-gray-900">活动奖励说明</h1>

        {/* 报名奖励 */}
        <div className="mb-4">
          <div className="mb-3 flex items-center">
            <span className="text-base font-medium text-gray-900">
              报名奖励
            </span>
            <span className="ml-2 text-sm text-gray-600">报名成功立即获得</span>
          </div>
          <Image
            src={course}
            alt="报名奖励内容1"
            className="size-40 rounded-md object-contain"
          />
          <p className="mt-3 text-left text-base font-medium text-gray-900">
            30天亲子专注力训练课程
          </p>
        </div>

        {/* 挑战成功奖励 */}
        <div>
          <h2 className="mb-3 text-base font-medium text-gray-900">
            挑战成功奖励
          </h2>
          <div className="flex items-center justify-normal py-1">
            <img
              src="/images/activity/summerChallenge/reward_1.jpg"
              alt="2元报名"
              className="size-20 rounded-md object-contain"
            />
            <img
              src="/images/activity/summerChallenge/reward_2.jpg"
              alt="瓜分奖金"
              className="ml-4 size-20 h-14 rounded-md object-contain"
            />
          </div>
          <p className="mt-3 text-left text-sm text-gray-700">
            2元报名费全额返 + 瓜分挑战失败者奖金池
          </p>
        </div>

        {/* TA们已参与 - 使用组件 */}
        <div className="mb-4">
          <div className="flex items-center">
            <span className="mr-4 pt-4 font-bold text-gray-800">
              TA们已参与
            </span>
            <ParticipantScroller className="flex-1" />
          </div>
        </div>
      </div>

      {/* 支付方式 */}
      <div className="mt-1 bg-white p-4">
        <h2 className="mb-4 text-lg font-bold text-gray-900">支付方式</h2>

        <div className="space-y-3">
          {/* 支付宝 */}
          {/* <div
            className={`flex cursor-pointer items-center justify-between rounded-lg border-2 p-4 transition-colors ${
              selectedPayMethod === '2'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 bg-white'
            }`}
            onClick={() => handlePayMethodSelect('2')}
          >
            <div className="flex items-center">
              <div className="mr-3 flex size-10 items-center justify-center rounded-lg">
                <Image
                  src={alipay}
                  alt="支付宝"
                  width={40}
                  height={40}
                  className="size-full object-contain"
                />
              </div>
              <span className="text-base font-medium text-gray-900">
                支付宝
              </span>
            </div>
            <div
              className={`flex size-5 items-center justify-center rounded-full border-2 ${
                selectedPayMethod === '2'
                  ? 'border-green-500 bg-green-500'
                  : 'border-gray-300'
              }`}
            >
              {selectedPayMethod === '2' && (
                <span className="text-xs text-white">✓</span>
              )}
            </div>
          </div> */}

          {/* 微信支付 */}
          <div
            className={`flex cursor-pointer items-center justify-between rounded-lg border-2 p-4 transition-colors ${
              selectedPayMethod === '1'
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 bg-white'
            }`}
            onClick={() => handlePayMethodSelect('1')}
          >
            <div className="flex items-center">
              <div className="mr-3 flex size-10 items-center justify-center rounded-lg">
                <Image
                  src={wechat}
                  alt="微信支付"
                  width={40}
                  height={40}
                  className="size-full object-contain"
                />
              </div>
              <span className="text-base font-medium text-gray-900">微信</span>
            </div>
            <div
              className={`flex size-5 items-center justify-center rounded-full border-2 ${
                selectedPayMethod === '1'
                  ? 'border-green-500 bg-green-500'
                  : 'border-gray-300'
              }`}
            >
              {selectedPayMethod === '1' && (
                <span className="text-xs text-white">✓</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 底部支付栏 */}
      <div className="safe-area-pb fixed inset-x-0 bottom-0 border-t border-gray-200 bg-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="mr-2 text-base text-gray-600">需付款：</span>
            <span className="text-xl font-bold text-red-500">¥ 2.0元</span>
          </div>

          <button
            type="button"
            onClick={handlePay}
            disabled={isLoading}
            className={`rounded-full px-8 py-3 text-base font-medium text-white transition-colors ${
              isLoading
                ? 'cursor-not-allowed bg-gray-400'
                : 'bg-green-500 hover:bg-green-600 active:bg-green-700'
            }`}
          >
            {isLoading ? '支付中...' : '立即支付'}
          </button>
        </div>
      </div>

      {/* 支付成功弹窗 */}
      {showSuccessModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4">
          <div
            className="bg-clear relative mx-auto w-full max-w-xs rounded-2xl"
            style={{ aspectRatio: '6/8' }}
          >
            {/* 弹窗内容容器 */}
            <div
              className="flex size-full cursor-pointer flex-col items-center justify-center"
              onClick={handleGoClockIn}
            >
              {/* 关闭按钮 */}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCloseSuccessModal();
                }}
                className="bg-clear absolute right-3 top-3 z-10 flex size-6 items-center justify-center text-gray-400 hover:text-gray-600"
              >
                <img
                  src="/images/activity/summerChallenge/close.png"
                  alt="关闭"
                  className="size-6 rounded-md object-contain"
                />
              </button>
              {/* success图片 - 占据主要空间 */}
              <div className="flex flex-1 items-center justify-center">
                <Image
                  src={success}
                  alt="支付成功"
                  width={200}
                  height={200}
                  className="size-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 底部安全区域占位 */}
      <div className="h-20" />
    </div>
  );
};

export default SummerPayPage;
