'use client'; // 确保在客户端渲染

import React from 'react';
import { Modal, Button, Space } from 'antd-mobile';
// 移除对CSS Modules的导入：import styles from './ChallengeResultModal.module.css';

interface ChallengeResultModalProps {
  isOpen: boolean;
  isSuccess: boolean;
  onClose: () => void;
  onClaimReward: () => void;
}

const ChallengeResultModal: React.FC<ChallengeResultModalProps> = ({
                                                                     isOpen,
                                                                     isSuccess,
                                                                     onClose,
                                                                     onClaimReward,
                                                                   }) => {
  if (!isOpen) {
    return null;
  }

  // 定义内联样式对象
  const modalContentStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '20px 15px',
    textAlign: 'center',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '20px',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '10px',
  };

  const messageStyle: React.CSSProperties = {
    fontSize: '16px',
    color: '#666',
    marginBottom: '25px',
    lineHeight: 1.5,
  };

  const buttonGroupStyle: React.CSSProperties = {
    width: '100%',
  };

  // 注意：antd-mobile Button组件通过 `color` 和 `className` prop来定制样式，
  // 它的内部样式可能需要覆盖，或者通过Ant Design Mobile的自定义主题/变量来实现更深的定制。
  // 对于直接在Button上应用内联样式可能不会完全生效，因为antd-mobile组件有自己的样式层级。
  // 通常会通过 `className` + 全局CSS 或者 `styled-components` 等方式定制antd-mobile组件。
  // 这里我们假设可以部分通过className覆盖。

  return (
    <Modal
      visible={isOpen}
      content={
        <div style={modalContentStyle}> {/* 应用内联样式 */}
          {isSuccess ? (
            <>
              <div style={titleStyle}>挑战成功！</div> {/* 应用内联样式 */}
              <div style={messageStyle}>恭喜您完成挑战，快来领取您的奖励吧！</div> {/* 应用内联样式 */}
            </>
          ) : (
            <>
              <div style={titleStyle}>挑战失败</div> {/* 应用内联样式 */}
              <div style={messageStyle}>很遗憾您未能完成本次挑战。下次再接再厉！</div> {/* 应用内联样式 */}
            </>
          )}
          <Space block direction='vertical' style={buttonGroupStyle}> {/* 应用内联样式 */}
            {isSuccess && (
              <Button
                block
                // antd-mobile Button的样式定制通常通过color/fill/className实现
                // 内联style在这里可能不会完全生效，或者需要使用`style`属性来覆盖部分默认样式
                onClick={onClaimReward}
                color='primary' // primary color for success button
                size='large'
                style={{ backgroundColor: '#4CAF50', borderColor: '#4CAF50', color: 'white', fontSize: '16px', fontWeight: 'bold' }} // 直接覆盖背景色和字体色
              >
                瓜分奖励
              </Button>
            )}
            <Button
              block
              onClick={onClose}
              color='default' // default color for close button
              size='large'
              style={{ backgroundColor: '#f0f0f0', borderColor: '#f0f0f0', color: '#666', fontSize: '16px', fontWeight: 'bold' }} // 直接覆盖背景色和字体色
            >
              {isSuccess ? '稍后领取' : '关闭'}
            </Button>
          </Space>
        </div>
      }
      showCloseButton={false}
      onClose={onClose}
      // 对于antd-mobile Modal的根样式，可能需要通过全局CSS或定制主题来覆盖，
      // 因为它可能不直接支持通过 className 或 style prop直接修改其内部的弹窗容器。
      // 如果要修改 Modal 自身的宽度和圆角，通常会在全局 CSS 中针对 antd-mobile 的类名进行覆盖，
      // 或者通过其提供的 `bodyStyle`, `maskStyle` 等 prop 来实现。
      // 例如：
      bodyStyle={{ width: '80vw', borderRadius: '12px', marginLeft: '-10px' }} // 示例：直接设置body样式
    />
  );
};

export default ChallengeResultModal;