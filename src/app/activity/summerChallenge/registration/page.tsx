'use client';

import { Loading, Modal, Toast } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

import {
  activityStatus,
  applyForWithdrawal,
  getSummerActivityBonus,
  summerTasks
} from '@/api/summerActivity';
import {
  getMessage,
  getMobile,
  hinaTrack,
  navigationToNativePage
} from '@/utils';

import ParticipantScroller from '../components/ParticipantScroller';
import ChallengeResultModal from './ChallengeResultModal';

const style1 = 'text-gray-500 bg-gray-300';
const style2 = 'text-white bg-green-500';
// 定义报名状态映射规则
const registerStatusMap: {
  [key: number]: {
    type: number;
    btnText: string;
    receiveStyle: string;
    btnStyle: string;
    btnDisable: boolean;
  };
} = {
  0: {
    type: 0,
    btnText: '活动未开始',
    receiveStyle: style1,
    btnStyle: style1,
    btnDisable: true
  },
  1: {
    type: 1,
    btnText: '立即参与',
    receiveStyle: style1,
    btnStyle: style2,
    btnDisable: false
  },
  2: {
    type: 2,
    btnText: '报名已截止',
    receiveStyle: style1,
    btnStyle: style1,
    btnDisable: true
  },
  3: {
    type: 3,
    btnText: '立即打卡',
    receiveStyle: style2,
    btnStyle: style2,
    btnDisable: false
  },
  4: {
    type: 4,
    btnText: '活动已结束',
    receiveStyle: style1,
    btnStyle: style1,
    btnDisable: true
  },
  5: {
    type: 5,
    btnText: '等待开奖',
    receiveStyle: style2,
    btnStyle: style1,
    btnDisable: true
  },
  6: {
    type: 6,
    btnText: '瓜分奖励',
    receiveStyle: style2,
    btnStyle: style2,
    btnDisable: false
  },
  7: {
    type: 7,
    btnText: '确认收款',
    receiveStyle: style2,
    btnStyle: style2,
    btnDisable: false
  },
  8: {
    type: 8,
    btnText: '提现失败',
    receiveStyle: style2,
    btnStyle: style1,
    btnDisable: false
  },
  9: {
    type: 9,
    btnText: '已领取奖励',
    receiveStyle: style2,
    btnStyle: style1,
    btnDisable: true
  },
  10: {
    type: 10,
    btnText: '挑战失败',
    receiveStyle: style2,
    btnStyle: style1,
    btnDisable: false
  }
};

const Registration = () => {
  const router = useRouter();
  const startDate = new Date(2025, 6, 1); // 2025年7月1日
  const registerEndDate = new Date(2025, 6, 15); // 2025年7月15日
  const endDate = new Date(2025, 7, 14); // 2025年8月14日

  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<any>(registerStatusMap[0]); // 0: 未开始, 1: 进行中, 2: 已结束
  const [regStatus, setRegStatus] = useState(0); // 0: 未开始, 1: 进行中, 2: 已截止
  const [showResultModal, setShowResultModal] = useState(false); //
  const [challengeSuccess, setChallengeSuccess] = useState(false); // 挑战成功
  const taskId = useRef('');
  const isBindWx = useRef(false);
  const HAS_SHOWN_CHALLENGE_RESULT_MODAL = 'hasShownChallengeResultModal';

  useEffect(() => {
    document.title = '暑期打卡挑战活动';
    hinaTrack('summer_checkinAct_pageview');

    void fetchData();
  }, []); // 空数组表示只在组件挂载和卸载时运行一次

  const fetchData = async () => {
    console.log('🚀 ~ fetchData');
    const currentDate = new Date();
    const dateStatus =
      currentDate > endDate
        ? 4
        : currentDate >= startDate && currentDate <= endDate
          ? 1
          : 0;
    const canTRegister = dateStatus === 1 && currentDate > registerEndDate; // 过了报名日期，不能再报名了
    let currentActivityStatus = registerStatusMap[dateStatus];

    try {
      const activityRes: any = await activityStatus();
      setRegStatus(
        currentDate > registerEndDate ? 2 : currentDate < startDate ? 0 : 1
      );
      const tasksRes: any = await summerTasks();
      taskId.current = tasksRes.taskId;
      if (activityRes.isSign) {
        if (currentDate <= endDate) {
          currentActivityStatus = registerStatusMap[3]; // 立即打卡
        } else if (dateStatus === 4) {
          const bonus: any = await getSummerActivityBonus();
          if (bonus.bonus?.actResult === 1) {
            // 挑战成功
            setChallengeSuccess(true);
            isBindWx.current = bonus.bonus?.openid;
            if (bonus.bonus?.transferStatus === 0) {
              // 瓜分奖励
              currentActivityStatus = registerStatusMap[6];
              setShowResultModal(true);
            } else if (bonus.bonus?.transferStatus == 1) {
              // 等待用户确认收款
              currentActivityStatus = registerStatusMap[7];
            } else if (bonus.bonus?.transferStatus == 2) {
              // 成功
              currentActivityStatus = registerStatusMap[9];
            } else if (bonus.bonus?.transferStatus == -1) {
              // 失败
              currentActivityStatus = registerStatusMap[8];
            }
          } else if (bonus.bonus?.actResult === -1) {
            currentActivityStatus = registerStatusMap[10]; // 挑战失败
            showChallengeResultModal();
          } else {
            currentActivityStatus = registerStatusMap[5]; // 等待开奖
          }
        }
      } else if (canTRegister) {
        currentActivityStatus = registerStatusMap[2]; // 报名已截止
      }
    } catch (error) {
      Toast.show({ content: `Failed to fetch activity status: ${error}` });
      console.error('Failed to fetch activity status:', error);
    } finally {
      setStatus(currentActivityStatus);
    }
  };

  const onMessage = (event: any) => {
    try {
      console.log('onMessage:', event.data);
      const data = JSON.parse(event.data);

      console.log(`onMessage result: ${data.launchWeChatConfirmReceipt}`);
      if (data.launchWeChatConfirmReceipt === true) {
        queryStatus();
      } else setLoading(false);
    } catch (error) {
      console.error('Failed to onMessage:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getMessage(onMessage);

    // 清理函数，移除事件监听器
    return () => {
      window.removeEventListener('message', onMessage, false);
      document.removeEventListener('message', onMessage, false);
    };
  }, [onMessage]);

  function queryStatus() {
    console.log('queryStatus');
    setTimeout(async () => {
      const bonus: any = await getSummerActivityBonus();
      if (bonus.bonus?.transferStatus == 0) {
        queryStatus();
      } else {
        // 状态已更新
        void fetchData();
        setLoading(false);
      }
    }, 3000);
  }

  // 去领取课程
  function handleReceiveCourse() {
    if (status.type !== 3) {
      hinaTrack('summer_checkinAct_reward_click');
    }
    const type = status.type === 3 || status.type >= 5 ? 2 : 1;
    router.push(`/activity/summerChallenge/course?type=${type}`);
  }

  // 立即参与
  function handleParticipateNow() {
    if (status.type === 1) {
      // 报名
      hinaTrack('summer_checkinAct_signup_click');
      router.push(`/activity/summerChallenge/pay?taskId=${taskId.current}`);
    } else if (status?.type === 3) {
      // 去打卡
      hinaTrack('summer_checkinAct_checkin');
      navigationToNativePage(
        `rn://BabyTaskStack?initialRoute=SubmitListScreen&taskId=${taskId.current}`
      );
    } else if (status?.type === 6 || status?.type === 7) {
      // 提现
      handleClaimReward();
    } else if (status?.type === 8) {
      // 提现失败
      Toast.show({ content: '提现失败，请联系客服处理' });
    } else if (status?.type === 10) {
      // 挑战失败
      setShowResultModal(true);
    }
  }

  // 显示挑战结果失败弹框
  function showChallengeResultModal() {
    if (
      typeof window !== 'undefined' &&
      !localStorage.getItem(HAS_SHOWN_CHALLENGE_RESULT_MODAL)
    ) {
      setShowResultModal(true);
      localStorage.setItem(HAS_SHOWN_CHALLENGE_RESULT_MODAL, 'true');
    }
  }

  function handleCloseModal(): void {
    setShowResultModal(false);
  }

  function handleClaimReward() {
    handleCloseModal();
    if (!isBindWx.current) {
      // 未绑定微信，跳转到账号安全页面去绑定微信
      Toast.show({ content: '请先绑定微信' });
      setTimeout(() => {
        navigationToNativePage('app://app/my/accountSecurity');
      }, 3000);
      return;
    }
    try {
      const device = getMobile();
      if (device === 'android') {
        if (window.android && window.android.launchWeChatConfirmReceipt) {
          claimReward(device);
        } else {
          console.error(
            'android.launchWeChatConfirmReceipt function is not available.'
          );
          Toast.show({ content: '请先升级App后再试' });
        }
      } else if (device === 'ios') {
        if (window.webkit.messageHandlers.launchWeChatConfirmReceipt) {
          claimReward(device);
        } else {
          console.error(
            'window.webkit.messageHandlers.launchWeChatConfirmReceipt function is not available.'
          );
          Toast.show({ content: '请先升级App后再试' });
        }
      } else {
        console.log('device is not available.');
      }
    } catch (e: any) {
      console.log(e.message);
    }
  }

  // 获取奖励
  function claimReward(device: string) {
    setLoading(true);
    applyForWithdrawal()
      .then((res: any) => {
        if (device === 'android') {
          window.android.launchWeChatConfirmReceipt(res.query);
        } else {
          const { query } = res;
          window.webkit.messageHandlers.launchWeChatConfirmReceipt.postMessage({
            query
          });
        }
      })
      .catch((reason) => {
        Toast.show({ content: `提现失败 ${reason.response.data.message}` });
        setLoading(false);
      });
  }

  return (
    <div className="flex min-h-screen flex-col bg-white">
      {/* Header - GIF Banner */}
      <header className="relative w-full bg-white">
        {/* 使用用户提供的GIF图片 */}
        <img
          src="/images/activity/summerChallenge/banner.gif"
          alt="暑期打卡挑战"
          className="w-full object-cover"
        />
        <h1 className="w-full px-4 py-2 text-sm font-bold">
          暑期30天打卡挑战 | 玩亲子游戏·瓜分奖金
        </h1>
      </header>

      {/* Scrollable Content */}
      <main className="grow overflow-y-auto">
        {/* 活动详情 */}
        <section>
          <div className="px-4">
            <div className="flex items-center justify-normal py-1">
              <span className="font-bold text-gray-800">报名时间</span>
              <div>
                <span
                  className="ml-4 text-gray-800"
                  style={{ fontSize: '15px' }}
                >
                  7月1日~7月15日
                </span>
                <span
                  className={`${regStatus === 1 ? style2 : style1} ml-2 rounded-full px-2 py-1 text-xs`}
                >
                  {regStatus === 0
                    ? '未开始'
                    : regStatus === 1
                      ? '进行中'
                      : '已截止'}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-normal py-1">
              <span className="font-bold text-gray-800">挑战周期</span>
              <span className="ml-4 text-gray-800" style={{ fontSize: '15px' }}>
                7月1日~8月14日
                <span
                  className="text-sm text-gray-500"
                  style={{ fontSize: '15px' }}
                >
                  (45天)
                </span>
              </span>
            </div>
            <div className="flex items-center justify-normal py-1">
              <span className="font-bold text-gray-800">挑战目标</span>
              <span className="ml-4 text-gray-800" style={{ fontSize: '15px' }}>
                玩亲子专注力训练游戏 | 累计打卡满30天
              </span>
            </div>
          </div>
        </section>

        {/* 报名奖励 */}
        <section>
          <div className="px-4">
            <div className="flex items-center justify-normal pt-1">
              <span className="font-bold text-gray-800">报名奖励</span>
              <span className="ml-4 text-gray-800" style={{ fontSize: '15px' }}>
                报名成功立即获得
              </span>
            </div>
            <div className="flex justify-normal">
              <img
                src="/images/activity/summerChallenge/course.webp"
                alt="2元报名"
                className="size-20 rounded-md object-contain"
              />
              <button
                disabled={
                  status.type === 0 ||
                  status.type === 1 ||
                  status.type === 2 ||
                  status.type === 4
                }
                onClick={handleReceiveCourse}
                className={`${status?.receiveStyle} mb-2 ml-4 h-8 self-end rounded-sm px-2.5 py-1 text-base`}
                style={{ fontSize: '15px' }}
              >
                去领取
              </button>
            </div>
            <p className="text-gray-800" style={{ fontSize: '15px' }}>
              30天亲子专注力训练课程
            </p>
          </div>
        </section>

        {/* 挑战奖励 */}
        <section>
          <div className="px-4 pt-3">
            <div className="flex items-center justify-normal pt-1">
              <span className="font-bold text-gray-800">挑战奖励</span>
            </div>
            <div className="flex items-center justify-normal py-1">
              <img
                src="/images/activity/summerChallenge/reward_1.jpg"
                alt="2元报名"
                className="size-14 rounded-md object-contain"
              />
              <img
                src="/images/activity/summerChallenge/reward_2.jpg"
                alt="瓜分奖金"
                className="ml-4 size-14 rounded-md object-contain"
              />
            </div>
            <p className="text-gray-800" style={{ fontSize: '15px' }}>
              2元报名费全额返 + 瓜分未挑战成功者奖金池
            </p>
          </div>
        </section>

        {/* TA们已参与 */}
        <section>
          <div className="mr-3 flex items-center  px-4">
            <span className="mr-4 pt-4 font-bold text-gray-800">
              TA们已参与
            </span>
            <ParticipantScroller className="flex-1" />
          </div>
        </section>

        <section>
          <div
            className="relative mt-3 pb-20"
            style={{
              position: 'relative',
              backgroundImage:
                'url(/images/activity/summerChallenge/activityPlayBg.png)',
              backgroundRepeat: 'repeat-y',
              backgroundSize: '100% auto'
            }}
          >
            <div className="relative">
              <img
                src="/images/activity/summerChallenge/activityPlay.png"
                alt="活动玩法介绍"
                className="w-full"
              />
              <img
                onClick={handleReceiveCourse}
                src="/images/activity/summerChallenge/courseview.png"
                alt="查看课程详情"
                className="pulse absolute"
                style={{
                  bottom: '-10px',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '40%'
                }}
              />
            </div>
            <div className=" mt-2 overflow-hidden rounded-md p-4">
              <p className="rounded-t-md bg-[#f8367f] p-2 text-center text-[38px] text-white">
                活动说明
              </p>
              <div className="space-y-4 rounded-b-md bg-white px-2 py-4 text-[32px] leading-relaxed">
                <p className="m-2 text-[28px]">
                  本活动旨在通过亲子专注力训练游戏,帮助家长与孩子度过充实有趣的暑期,提升孩子的专注力,同时设置奖励机制,激励用户积极参与。
                </p>
                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      01 报名后什么时候开始打卡?
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      活动打卡时间为7月1日~8月14日,从7月1日开始就可以打卡啦。您需要在这45天内累计打卡满30天。
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      02 下次进入APP在哪里找到活动入口?
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      您可以在APP的【校园】页面的顶部轮播banner里找到此活动【暑期打卡挑战】,点击并查看挑战进度。
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      03 报名后怎么查看解锁的《30天亲子专注力训练课》?
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      报名成功后,课程权限实时解锁,仅限参与活动使用手机号对应的账户。您可以再次点击【校园】页面顶部的【暑期打卡挑战】banner,在"报名名奖励"处领取并下载课程。
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">04 怎么打卡?</span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      您需要带着孩子按照《30天亲子专注力训练课》的课程规划完成每日任务并通过活动页面上传训练相关照片。当打卡页面底部显示"今日已打卡"即视为当天打卡成功。
                    </p>
                    <p className="mt-1 text-[30px] text-gray-700">
                      在活动页面累计打卡成功30天后,活动页面显示"挑战成功,恭喜您完成所有挑战任务"即为挑战成功,可无需再继续打卡。
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      05 打卡挑战成功后,奖励什么时候发放?
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      ·报名费将会在活动结束后的7个工作日内原路退回至您支付的账户。
                      <br />
                      ·瓜分的奖金也将会在活动结束后的7个工作日内通过微信转账至您您绑定APP的微信账号
                      <br />
                      <span className="text-red-500">温馨提示：</span>
                      <br />
                      <span className=" text-[30px] text-red-500">
                        微信支付新规要求--需要您手动确认收款才可转账成功,请您在收到转账通知后的24小时内确认收款,否则将会过期,过期不予补发。
                      </span>
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      06 本活动属于虚拟物品,报名后概不支持申请退款
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      参与活动前请仔细阅读活动规则,若活动结束后因自身原因挑战失败掌心平台将不进行任何返还补偿哦。
                      <br />
                      但您仍享有学习和下载《30天亲子专注力训练课》的权力。
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div>
                    <span className="font-bold">
                      07 本活动不支持补卡,请记得定时来打卡哦
                    </span>
                    <p className="mt-1 text-[30px] text-gray-700">
                      若您当日已经上传照片并发布打卡帖子,但是页面未显示"今日已打卡"或出现打卡失败的情况,请于当日联系在线客服为您排查处理。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer className="fixed inset-x-0 bottom-0 p-4">
        <button
          className={`${status?.btnStyle} w-full rounded-md py-3 text-base font-medium`}
          onClick={handleParticipateNow}
          disabled={status.btnDisable}
        >
          {status?.btnText}
        </button>
      </footer>

      {showResultModal && (
        <ChallengeResultModal
          isOpen={showResultModal}
          isSuccess={challengeSuccess}
          onClose={handleCloseModal}
          onClaimReward={handleClaimReward}
        />
      )}

      <Modal
        visible={loading}
        showCloseButton={false}
        closeOnMaskClick={false}
        content={
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              flexDirection: 'column' // 确保内容垂直排列
            }}
          >
            <Loading style={{ marginBottom: '40px' }} />
            <div style={{ fontSize: '16px', color: '#666' }}>订单查询中...</div>
          </div>
        }
        bodyStyle={{
          width: '150px',
          height: '150px',
          backgroundColor: '#fff',
          borderRadius: '8px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        }}
      />
    </div>
  );
};

export default Registration;
