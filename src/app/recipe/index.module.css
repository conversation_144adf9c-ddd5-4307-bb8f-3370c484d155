.tableRowEven {
  background-color: #f3f9fb;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.tableRowOdd {
  background-color: #fefdf6;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.tableRow {
  border-bottom: 1px solid #fbf5b1;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.tableColumn {
  width: 25%;
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 28px;
  text-align: right;
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  color: #f3a90a;
}
.tableColumnLeft {
  width: 200px;
  text-align: right;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.tableColumnRight {
  width: 200px;
  text-align: right;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableHeader {
  border-bottom: 1px solid #fbf5b1;
  background-color: #fefdf6;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.nutritionTables {
  padding-bottom: 30px;
}
.rightItem {
  width: 50%;
  min-height: 80px;
  display: flex;
  padding: 10px 10px 10px 25px;
  font-size: 26px;
  border-left: 1px dotted #ccc;
  flex-direction: column;
  justify-content: center;
}
.leftItem {
  min-height: 80px;
  width: 50%;
  display: flex;
  flex: 1;
  color: #000;
  font-size: 26px;
  padding: 10px 10px 10px 25px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.simpleDish {
  font-size: 30px;
  align-items: flex-start;
}
.dishesFoodIngredientsItemHeader .rightItem,
.dishesFoodIngredientsItemHeader .leftItem {
  text-align: center;
  height: 80px;
  padding: 0;
  line-height: 80px;
  font-size: 30px;
  flex-direction: row;
  justify-content: center;
}
.dishesFoodIngredientsItem {
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-top: 1px dotted #ccc;
  flex: 1;
}
.dishesFoodIngredientsItemHeader {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background: #f7f7f7;
}
.dishesFoodIngredients {
  background: #f7f7f7;
  border-radius: 6px;
}
.mealBoxCTitle {
  padding-left: 26px;
  font-size: 30px;
  margin: 25px 0;
}
.mealNameBox {
  position: relative;
}
.leftLine {
  width: 8px;
  height: 36px;
  top: 5px;
  position: absolute;
}
.mealBoxContent {
  margin-top: 10px;
}
.mealBox {
  margin: 0px 20px 0 20px;
  padding: 30px 10px 0;
}
.mealBox1 .leftLine {
  background-color: #f7984c;
}
.mealBox1 .mealBoxCTitle,
.mealBox1 .dishesFoodIngredientsItemHeader .leftItem,
.mealBox1 .dishesFoodIngredientsItemHeader .rightItem,
.mealBox1 .simpleDish {
  color: #f7984c;
}
.mealBox2 .leftLine {
  background-color: #67dbf0;
}
.mealBox2 .mealBoxCTitle,
.mealBox2 .dishesFoodIngredientsItemHeader .leftItem,
.mealBox2 .dishesFoodIngredientsItemHeader .rightItem,
.mealBox2 .simpleDish {
  color: #67dbf0;
}
.mealBox3 .leftLine {
  background-color: #a1ec5e;
}
.mealBox3 .mealBoxCTitle,
.mealBox3 .dishesFoodIngredientsItemHeader .leftItem,
.mealBox3 .dishesFoodIngredientsItemHeader .rightItem,
.mealBox3 .simpleDish {
  color: #a1ec5e;
}
.mealBox4 .leftLine {
  background-color: #f87682;
}
.mealBox4 .mealBoxCTitle,
.mealBox4 .dishesFoodIngredientsItemHeader .leftItem,
.mealBox4 .dishesFoodIngredientsItemHeader .rightItem,
.mealBox4 .simpleDish {
  color: #f87682;
}
.mealBox5 .leftLine {
  background-color: #e884f7;
}
.mealBox5 .mealBoxCTitle,
.mealBox5 .dishesFoodIngredientsItemHeader .leftItem,
.mealBox5 .dishesFoodIngredientsItemHeader .rightItem,
.mealBox5 .simpleDish {
  color: #e884f7;
}
.recipeShare {
  background: #fff;
  flex: 1;
}
.headerTopBox {
  height: 417px;
  position: relative;
}
.babyName {
  position: absolute;
  width: 100%;
  top: 100px;
  font-size: 64px;
  text-align: center;
  font-weight: bold;
  z-index: 2;
}
.schoolName {
  position: absolute;
  top: 140px;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  left: 50%;
  /* background: url('/images/recipe/headerIcon.png') no-repeat; */
  background-size: 100% 100%;
  min-width: 250px;
  color: #000;
  padding: 5px 30px;
  transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  z-index: 2;
}
.dateWeekDay {
  width: 100%;
  position: absolute;
  top: 50px;
  font-size: 34px;
  font-weight: 500;
  text-align: center;
  z-index: 2;
}
.headerTop {
  height: 417px;
  position: relative;
  z-index: 1;
}
.imageBox {
  padding: 0 30px 30px;
  margin-top: 30px;
  overflow: hidden;
  position: relative;
  border-bottom: 1px solid #f1f1f1;
}
.imageItem {
  float: left;
  position: relative;
}
.imageItem1 {
  float: left;
  position: relative;
  width: 100%;
  padding-top: 100%;
}
.imageItem2 {
  float: left;
  position: relative;
  width: 50%;
  height: 0;
  padding-top: 50%;
  padding-left: 12px;
  padding-right: 12px;
}
.imageItem3 {
  float: left;
  position: relative;
  width: 33.33333%;
  padding-top: 33.33333%;
  padding-left: 6px;
  padding-right: 6px;
  box-sizing: border-box;
}
.foodImage {
  position: absolute;
  border-radius: 10px;
  display: block;
}
.listContent {
  border-top: 2px solid #f1f1f1 !important;
}
.foodImage3 {
  position: absolute;
  border-radius: 8px;
  display: block;
  top: 3px;
  left: 3px;
  width: calc(100% - 6px);
  height: calc(100% - 6px);
}
.foodImage2 {
  position: absolute;
  border-radius: 8px;
  display: block;
  top: 6px;
  left: 6px;
  width: calc(100% - 12px);
  height: calc(100% - 12px);
}
.foodImage1 {
  position: absolute;
  border-radius: 8px;
  display: block;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.foodImage img {
  border-radius: 8px;
  width: 100%;
  display: block;
  height: 100%;
}
