'use client';

import { Image, ImageViewer, Tabs } from 'antd-mobile';
import Head from 'next/head';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { getRecipeInfo, getStandard } from '@/api/recipe';
import FooterQrCode from '@/components/FooterQrcode';
import HeaderDown from '@/components/HeaderDown';
import LaunchWeApp from '@/components/LaunchWeApp';
import { wechatShare } from '@/utils/wechat';

import styles from './index.module.css';

export const dynamic = 'force-dynamic';

const Page = () => {
  const searchParams = useSearchParams();
  const instId = searchParams?.get('instId') || null;
  const date = searchParams?.get('date') || null;
  const type = searchParams?.get('type') || null;
  const schoolName = searchParams?.get('schoolName') || null;

  const [monthDay, setMonthDay] = useState('');
  const [weekday, setWeekday] = useState('');
  const [imagesData, setImagesData] = useState([]);
  const [mealList, setMealList] = useState<any>([]);
  const [nutritionStandard, setNutritionStandard] = useState<
    {
      id: number;
      nutritionCode: string;
      nutritionName: string;
      standardValue: number;
      unit: string;
    }[]
  >([]);

  const [nutritionData, setNutritionData] = useState<any>([]);

  useEffect(() => {
    if (date) {
      const dateArr = date?.split('-');
      const newDateTime = `${dateArr[0]}/${dateArr[1]}/${dateArr[2]}`;
      const weekDayArr = ['日', '一', '二', '三', '四', '五', '六'];
      setWeekday(weekDayArr[new Date(newDateTime).getDay()]);
      setMonthDay(`${dateArr[1]}-${dateArr[2]}`);
      getStandard({ instId, date }).then((res: any) => {
        setNutritionStandard(res.list);
        const wxData = {
          title: '宝贝食谱',
          desc: '守护健康，为爱分享',
          link: window.location.href,
          imgUrl: '',
        };
        wechatShare(wxData);
      });
      onLoad();
    }
  }, [instId, date, type]);

  // 将相同餐段不同菜肴合并
  const mergeSameMeal = (arr: any[]) => {
    const result = [];
    const tempMap = new Map();

    for (let i = 0; i < arr.length; i++) {
      const { mealName } = arr[i];

      if (tempMap.has(mealName)) {
        const index = tempMap.get(mealName);
        result[index].dishList.push(arr[i]);
      } else {
        const newItem = {
          mealName,
          mealSort: arr[i].mealSort,
          dishList: [arr[i]],
        };
        result.push(newItem);
        tempMap.set(mealName, result.length - 1);
      }
    }
    return result;
  };

  const filterClass = (index: number) => {
    const remainder = (index + 1) % 5;
    if (remainder === 0 || remainder === 5) {
      return styles.mealBox1;
    }
    return styles[`mealBox${index}`];
  };

  const countImageSize = () => {
    const imagesDataSize = imagesData.length;
    if (imagesDataSize === 1) {
      return {
        imageItem: styles.imageItem1,
        foodImage: styles.foodImage1,
      };
    }
    if (imagesDataSize > 4) {
      return {
        imageItem: styles.imageItem3,
        foodImage: styles.foodImage3,
      };
    }
    return {
      imageItem: styles.imageItem2,
      foodImage: styles.foodImage2,
    };
  };

  const isTableColumn = (index: number) => {
    if ((index + 1) % 2 === 0) {
      return styles.tableRowEven;
    }
    return styles.tableRowOdd;
  };

  const imagePreview = (index: number) => {
    ImageViewer.Multi.show({
      images: imagesData,
      defaultIndex: index,
    });
  };

  const onLoad = async () => {
    if (instId && date) {
      const res = (await getRecipeInfo({ instId, date })) as any;
      const recipeSectionArray = mergeSameMeal(res.info?.dishList);
      setMealList(recipeSectionArray);
      setImagesData(res.info?.cookPictureList);
      setNutritionData(res.info?.cookNutrition);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <Head>
        <title>宝贝食谱</title>
      </Head>
      <HeaderDown />
      <div className={styles.recipeShare}>
        <div className={styles.headerTopBox}>
          <p className={styles.dateWeekDay}>
            {monthDay} 星期{weekday}
          </p>
          <div className={styles.schoolName}>{schoolName || ''}</div>
          <Image alt="" src="/images/recipe/recipesHeader.png" />
        </div>
        {type === 'true' ? (
          <Tabs>
            <Tabs.Tab key="1" title="今日食谱">
              <div className={styles.imageBox}>
                {imagesData.map((imgItem, index) => {
                  return (
                    <div
                      aria-hidden="true"
                      className={(styles.imageItem, countImageSize().imageItem)}
                      key={index}
                      onClick={() => imagePreview(index)}
                    >
                      <Image
                        alt=""
                        className={
                          (styles.foodImage, countImageSize().foodImage)
                        }
                        fit="cover"
                        src={imgItem}
                      />
                    </div>
                  );
                })}
              </div>

              <div className={styles.mealBoxContent}>
                {mealList.map((item: any, index: number) => {
                  return (
                    <div
                      className={(styles.mealBox, filterClass(index))}
                      key={index}
                    >
                      <div className={styles.mealNameBox}>
                        <p className={styles.mealBoxCTitle}>{item.mealName}</p>
                      </div>
                      <div className={styles.dishesFoodIngredientsItemHeader}>
                        <div className={styles.leftItem}>菜肴</div>
                        <div className={styles.rightItem}>食材用料</div>
                      </div>
                      {item.dishList?.map((item1: any, index1: number) => {
                        return (
                          <div
                            className={styles.dishesFoodIngredients}
                            key={index1}
                          >
                            <div className={styles.dishesFoodIngredientsItem}>
                              <div className={styles.leftItem}>
                                {item1.dishName}
                              </div>
                              <div className={styles.rightItem}>
                                {item1.ingredientList?.map(
                                  (item2: any, index2: number) => {
                                    return (
                                      <span key={index2}>
                                        {item2.ingredientName}
                                        {item2.amount}g、
                                      </span>
                                    );
                                  }
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </Tabs.Tab>
            <Tabs.Tab key="2" title="营养分析">
              <div className={styles.nutritionTables}>
                <div className={styles.tableHeader}>
                  <div className={styles.tableColumn}>
                    <div className={styles.tableColumnLeft}>
                      <span>营养素</span>
                    </div>
                  </div>

                  <div className={styles.tableColumn}>
                    <div className={styles.tableColumnRight}>
                      <span>摄入量</span>
                    </div>
                  </div>
                </div>
                {nutritionStandard.map((item, index) => {
                  return (
                    <div
                      className={(styles.tableRow, isTableColumn(index))}
                      key={index}
                    >
                      <div className={styles.tableColumn}>
                        <div className={styles.tableColumnLeft}>
                          <span>{item.nutritionName}</span>
                        </div>
                      </div>
                      <div className={styles.tableColumn}>
                        <div className={styles.tableColumnRight}>
                          <span>
                            {nutritionData[item.nutritionCode]}
                            {item.unit}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Tabs.Tab>
          </Tabs>
        ) : (
          <div>
            <div className={styles.imageBox}>
              {imagesData.map((imgItem, index) => {
                return (
                  <div
                    className={(styles.imageItem, countImageSize().imageItem)}
                    key={index}
                    onClick={() => imagePreview(index)}
                  >
                    <Image
                      alt=""
                      className={(styles.foodImage, countImageSize().foodImage)}
                      fit="cover"
                      src={imgItem}
                    />
                  </div>
                );
              })}
            </div>

            <div className={styles.mealBoxContent}>
              {mealList.map((item: any, index: number) => {
                return (
                  <div
                    className={(styles.mealBox, filterClass(index))}
                    key={index}
                  >
                    <div className={styles.mealNameBox}>
                      <p className={styles.mealBoxCTitle}>{item.mealName}</p>
                    </div>
                    <div className={styles.dishesFoodIngredientsItemHeader}>
                      <div className={styles.leftItem}>菜肴</div>
                    </div>
                    {item.dishList?.map((item1: any, index1: number) => {
                      return (
                        <div
                          className={styles.dishesFoodIngredients}
                          key={index1}
                        >
                          <div className={styles.dishesFoodIngredientsItem}>
                            <div className={styles.leftItem}>
                              {item1.dishName}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
      <div className="fixed bottom-14 left-[50%] z-50 translate-x-[-50%]">
        <LaunchWeApp />
      </div>
      <FooterQrCode />
    </div>
  );
};
export default Page;
