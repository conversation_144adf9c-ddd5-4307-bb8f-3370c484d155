'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import Cookies from 'js-cookie';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import React, { useEffect } from 'react';
import { useCommonStore } from '@/store/useCommonStore';
import { getBrowser } from '@/utils';

function setThemeByDomain() {
  const { hostname } = window.location;

  if (hostname.includes('baby-mobile')) {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#17C5A6'
    );
  } else {
    document.documentElement.style.setProperty(
      '--adm-color-primary',
      '#4E78FF'
    );
  }
}

// 检查是否在微信中打开
function checkWechatEnvironment() {
  const browser = getBrowser();
  return browser === 'wechat';
}

export default function Providers({ children }) {
  const [queryClient] = React.useState(() => new QueryClient());
  const setAuthorization = useCommonStore((state) => state.setAuthorization);
  const setVersion = useCommonStore((state) => state.setVersion);
  const setBrand = useCommonStore((state) => state.setBrand);
  const setAppType = useCommonStore((state) => state.setAppType);
  const setUserId = useCommonStore((state) => state.setUserId);
  const setStudentId = useCommonStore((state) => state.setStudentId);
  const setStudentName = useCommonStore((state) => state.setStudentName);

  useEffect(() => {
    const authorization = Cookies.get('Authorization');
    const version = Cookies.get('Version');
    const Brand = Cookies.get('Brand');
    const AppType = Cookies.get('App-Type');
    const userId = Cookies.get('parentId') || Cookies.get('teacherId');
    const studentId = Cookies.get('studentId');
    const studentName = Cookies.get('studentName');
    setAuthorization(authorization || '');
    setVersion(version || '');
    setBrand(Brand || '');
    setAppType(AppType || '');
    setUserId(userId || '');
    setStudentId(studentId || '');
    setStudentName(studentName || '');
    setThemeByDomain();
    // 检查微信环境
    checkWechatEnvironment();
  }, []);

  return (
    <NuqsAdapter>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </NuqsAdapter>
  );
}
