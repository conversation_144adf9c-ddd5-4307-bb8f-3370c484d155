'use client';

import { createReactBlockSpec } from '@blocknote/react';
import { Button, DotLoading, Input, Modal } from 'antd-mobile';
import { useAtom } from 'jotai'; // Added useAtom
import { useEffect, useState } from 'react';
import { PiRobot } from 'react-icons/pi';
import { aiGenerateContent } from '@/api/pbl';
import { markdownAtom } from '../../page'; // Added markdownAtom import

// 生成唯一 ID 的函数
const generateUniqueId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 创建一个用于显示 AI 内容输入框的 Modal 组件
interface AIContentModalProps {
  // @ts-expect-error - 使用通用类型
  editor: unknown;
  onClose: () => void;
}

const AIContentModal = ({ editor, onClose }: AIContentModalProps) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [markdownText] = useAtom(markdownAtom); // Get markdown content
  // 不使用 ref，改为自动聚焦属性

  useEffect(() => {
    // 自动聚焦由 Input 组件的 autoFocus 属性处理
  }, []);

  const generateContent = async () => {
    setIsLoading(true);
    try {
      // 在实际应用中，这里会调用真实的 API
      console.log('🚀 ~ markdownText:', markdownText);
      const promptRequirement = prompt
        ? `要求：${prompt}`
        : '生成不超过 200 字的相关内容';
      const query = `
      下面是一份针对学前儿童的 PBL 项目学习报告，
      
      ${markdownText}

      你需要在{{AI_COMPLETION_HERE}}的位置生成内容，${promptRequirement}，不要追问我问题，这是个一次性任务，直接输出文本内容，不要带格式
      `;
      const response: any = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        // Combine markdown context with user prompt for the query
        query,
        response_mode: 'blocking',
      });
      if (response) {
        // 在光标位置插入一个 paragraph 类型的内容
        // @ts-expect-error - 忽略类型错误
        editor.insertBlocks(
          [
            {
              id: generateUniqueId(),
              type: 'paragraph',
              props: {
                textColor: 'default',
                backgroundColor: 'default',
                textAlignment: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: response.answer,
                  styles: {},
                },
              ],
              children: [],
            },
          ],
          // @ts-expect-error - 忽略类型错误
          editor.getTextCursorPosition().block,
          'after'
        );

        setIsLoading(false);
        onClose();
      }
    } catch (error) {
      console.error('生成内容失败：', error);
      setIsLoading(false);
    }
  };

  return (
    <Modal
      closeOnMaskClick={!isLoading}
      content={
        <div className="p-4">
          <div className="mb-4 flex items-center">
            <PiRobot className="mr-2 text-blue-500" size={20} />
            <span className="font-medium">AI 生成内容</span>
          </div>

          <Input
            autoFocus
            className="mb-4"
            disabled={isLoading}
            onChange={setPrompt}
            placeholder="输入附加要求（可选）"
            value={prompt}
          />

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-4">
              <div className="text-blue-500">
                <DotLoading />
              </div>
              <p className="mt-2 text-gray-500">正在生成内容，请稍候...</p>
            </div>
          ) : (
            <div className="flex justify-end">
              <Button className="mr-2" color="default" onClick={onClose}>
                取消
              </Button>
              <Button color="primary" onClick={generateContent}>
                立即生成
              </Button>
            </div>
          )}
        </div>
      }
      onClose={onClose}
      showCloseButton={!isLoading}
      visible={true}
    />
  );
};

// 创建一个空的 Block 规范，但我们不会真正使用它来渲染内容
// 而是用它来触发 Modal 的显示
export const AIContent = createReactBlockSpec(
  {
    type: 'aiContent',
    propSchema: {},
    content: 'none',
    isSelectable: false,
    isFileBlock: false,
  },
  {
    // @ts-expect-error - 忽略类型错误
    render: ({ editor, block }) => {
      const [showModal, setShowModal] = useState(true);

      // 当 Modal 关闭时，删除这个 block
      const handleClose = () => {
        setShowModal(false);
        setTimeout(() => {
          // @ts-expect-error - 忽略类型错误
          const blocks = editor.document;
          for (const block of blocks) {
            if (block.type === 'aiContent') {
              // @ts-expect-error - 忽略类型错误
              editor.removeBlocks([block]);
              break;
            }
          }
        }, 100);
      };

      return (
        <>
          <div>1</div>
          {showModal ? (
            <AIContentModal editor={editor} onClose={handleClose} />
          ) : null}
        </>
      );
    },
    toExternalHTML: () => {
      // No point in including the HTML here, because the titles are already in the document
      return <div>{'{{AI_COMPLETION_HERE}}'}</div>;
    },
  }
);
