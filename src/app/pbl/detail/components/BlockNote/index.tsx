import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';
import './styles.css';

import {
  type Block,
  BlockNoteSchema,
  defaultBlockSpecs,
  filterSuggestionItems,
  insertOrUpdateBlock,
} from '@blocknote/core';
// @ts-expect-error
import { zh } from '@blocknote/core/locales';
import { BlockNoteView } from '@blocknote/mantine';
import {
  ExperimentalMobileFormattingToolbarController,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { Button, Dialog } from 'antd-mobile';
import { useAtom } from 'jotai';
import { debounce } from 'lodash-es';
import { memo, useEffect, useMemo } from 'react';
import { PiImage, PiNotePencil, PiRobot, PiTextT } from 'react-icons/pi';
import { cn } from '@/lib/utils';
import { isEditableAtom, markdownAtom } from '@/store/pbl';
import { useCommonStore } from '@/store/useCommonStore';
import { share } from '@/utils';
import { AIContent } from './AIContent';
import { AIPlaceholder } from './AIPlaceholder';
import { Custom } from './custom';
import { DaySummary } from './DaySummary';
import { ImageGrid } from './ImageGroup';
import { RecordBlock } from './Record';
import { UserNotes } from './UserNotes/index';
import { uploadFile } from './upload';

const { codeBlock, ...remainingBlockSpecs } = defaultBlockSpecs;

const schema = BlockNoteSchema.create({
  blockSpecs: {
    ...remainingBlockSpecs,
    // Adds the Separator block.
    custom: Custom,
    imageGrid: ImageGrid,
    userNotes: UserNotes,
    record: RecordBlock,
    daySummary: DaySummary,
    aiContent: AIContent,
    aiPlaceholder: AIPlaceholder,
  },
});

interface EditorProps {
  projectId: string;
  content: Block[];
  onChange: (blocks: Block[]) => void;
}

const insertAIContent = (editor: typeof schema.BlockNoteEditor) => ({
  title: 'AI 生成',
  onItemClick: () => {
    insertOrUpdateBlock(editor, {
      type: 'aiContent',
    });
  },
  group: '其他',
  description: '使用 AI 生成内容',
  icon: <PiRobot />,
});

const insertAIPlaceholder = (editor: typeof schema.BlockNoteEditor) => ({
  title: 'AI 生成内容',
  onItemClick: () => {
    const insertedBlockId = insertOrUpdateBlock(editor, {
      type: 'aiPlaceholder',
      props: {
        textColor: 'default',
        backgroundColor: 'default',
        textAlignment: 'left',
      },
    });
    editor.setTextCursorPosition(insertedBlockId);
  },
  group: 'AI 工具',
  icon: <PiTextT />,
});

const insertImageGrid = (editor: typeof schema.BlockNoteEditor) => ({
  title: '图片组',
  onItemClick: () => {
    insertOrUpdateBlock(editor, {
      type: 'imageGrid',
    });
  },
  group: '其他',
  description: '添加图片组',
  icon: <PiImage />,
});

// 暂时注释掉未使用的函数
// const insertUserNotes = (editor: typeof schema.BlockNoteEditor) => ({
//   title: '学生反馈',
//   onItemClick: () => {
//     insertOrUpdateBlock(editor, {
//       type: 'userNotes'
//     });
//   },
//   group: '其他',
//   description: '添加学生反馈',
//   icon: <PiChatText />
// });

const protectedBlockId = 'cb05000d-2307-4941-a655-e3f60bc85cc1';

export const BlockNote = memo(function BlockNote({
  projectId,
  content,
  onChange,
}: EditorProps) {
  const authorization = useCommonStore((state) => state.authorization);
  const [_, setMarkdown] = useAtom(markdownAtom);

  const [isEditable, setIsEditable] = useAtom(isEditableAtom);
  // Creates a new editor instance.
  const editor = useCreateBlockNote({
    schema,
    // 使用类型断言来避免类型错误
    initialContent: content as unknown as any,
    uploadFile,
    dictionary: zh,
    _tiptapOptions: {
      onUpdate: (_content) => {
        console.log('🚀 ~ _content:', _content);
      },
    },
  });

  const restoreProtectedBlock = (blockId: string) => {
    console.log('🚀 ~ restoreProtectedBlock:', blockId);
    Dialog.confirm({
      content:
        '你删除了项目发展实录，会影响项目进展记录自动放入报告，是否恢复？',
      confirmText: '恢复',
      cancelText: <div className="text-gray-600">不恢复</div>,
      onConfirm: () => {
        editor.insertBlocks(
          [
            {
              id: protectedBlockId,
              type: 'heading',
              props: {
                textColor: 'default',
                backgroundColor: 'default',
                textAlignment: 'left',
                level: 2,
              },
              content: [
                {
                  type: 'text',
                  text: '二、项目发展实录',
                  styles: {},
                },
              ],
              children: [],
            },
          ],
          editor.getTextCursorPosition().block,
          'after'
        );
      },
    });
  };

  useEffect(() => {
    if (editor) {
      // 添加事件监听器，在文档变化时检查
      const unsubscribe = editor.onChange((editor, { getChanges }) => {
        const changes = getChanges();

        // 筛选出所有被删除的块
        const deletedBlocks = changes.filter(
          (change) => change.type === 'delete'
        );

        if (deletedBlocks.length > 0) {
          console.log('以下块被删除：', deletedBlocks);
          for (const block of deletedBlocks) {
            if (block.block.id === protectedBlockId) {
              restoreProtectedBlock(block.block.id);
            }
          }
        }
      });

      // 清理函数
      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }

    return;
  }, [editor]);

  if (!(Array.isArray(content) && content.length > 0)) {
    return null;
  }

  // const slashMenuItems = useMemo(() => {
  //   return combineByGroup(getDefaultReactSlashMenuItems(editor));
  // }, [editor]);

  const debouncedOnChange = useMemo(
    () =>
      debounce(async () => {
        const markdown = await editor.blocksToMarkdownLossy(editor.document); // Generate markdown

        console.log('🚀 ~ markdown:', markdown);
        setMarkdown(markdown);
        onChange(editor.document as Block[]);
      }, 1500),
    [editor, setMarkdown, onChange]
  );

  const onSave = () => {
    setIsEditable(false);
  };

  const sendToComputer = () => {
    const url = `${window.location.origin}/pbl/detail?ProjectId=${projectId}&activeIndex=1&authorization=${authorization}`;
    console.log('🚀 ~ sendToComputer:');
    const shareData = {
      type: 0,
      title: '在线编辑 PBL 报告',
      description: '',
      thumbImage: '',
      url,
    };
    share(shareData);
  };

  return (
    <div className={cn(isEditable ? 'pl-8' : '', '')}>
      <BlockNoteView
        editable={isEditable}
        editor={editor}
        formattingToolbar={false}
        onChange={debouncedOnChange}
        slashMenu={false}
      >
        <ExperimentalMobileFormattingToolbarController />
        <SuggestionMenuController
          getItems={async (query) =>
            filterSuggestionItems(
              [
                insertAIPlaceholder(editor),
                ...getDefaultReactSlashMenuItems(editor),
                // insertUserNotes(editor),
                insertImageGrid(editor),
                // insertAIContent(editor)
              ],
              query
            )
          }
          triggerCharacter="/"
        />
      </BlockNoteView>
      <div className="fixed bottom-0 flex w-full items-center justify-center gap-2 p-4">
        <div className="">
          {isEditable ? (
            <Button
              block
              color="primary"
              onClick={onSave}
              shape="rounded"
              size="small"
            >
              退出编辑
            </Button>
          ) : (
            <Button
              block
              color="primary"
              onClick={() => setIsEditable(true)}
              shape="rounded"
              size="small"
            >
              <PiNotePencil />
              编辑报告
            </Button>
          )}
        </div>
        <div className="md:hidden">
          <Button block onClick={sendToComputer} shape="rounded" size="small">
            发送到电脑编辑
          </Button>
        </div>
      </div>
    </div>
  );
});
