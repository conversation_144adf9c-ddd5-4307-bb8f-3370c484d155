'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll, PullToRefresh } from 'antd-mobile';
import { compare } from 'compare-versions';
import { Image, Mic, PlayCircle, Plus, UserCircle, Video } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { getMaterialList } from '@/api/pbl';
import Empty from '@/components/Empty';
import { useCommonStore } from '@/store/useCommonStore';
import { getMessage, navigationToNativePage } from '@/utils';
import StatusBadge from './StatusBadge';

// import Script from 'next/script';

interface Material {
  id: string;
  observationId: string;
  title: string;
  type: 'video' | 'audio' | 'image';
  size: string;
  author: string;
  date: string;
  duration?: string; // Optional for images
  status: number; // 0-待处理 1-处理中 2-已处理 3-处理失败
}

// 定义接口返回的数据类型
interface ApiMaterial {
  instId: string;
  mediaId: string;
  observationId: string;
  type: number; // 1=图片，2=视频，3=音频
  url: string;
  fileSize: string;
  cover?: string;
  duration?: number;
  videoPlayType: number;
  handleError: string;
  createUserId: string;
  createTime: string;
  updateTime: string;
  projectId: string;
  source: number;
  name: string;
  deptId: string;
  createUser: {
    id: string;
    name: string;
    avatar: string;
  };
  handleStatus: number;
  handleStatusDesc: string;
}

// 转换为 UI 使用的数据类型
const mapApiToUiMaterial = (apiMaterial: ApiMaterial): Material => {
  // 类型映射：1=图片，2=视频，3=音频
  const typeMap: Record<number, 'image' | 'video' | 'audio'> = {
    1: 'image',
    2: 'video',
    3: 'audio',
  };

  // 格式化文件大小
  const formatFileSize = (size: string) => {
    const sizeNum = Number.parseInt(size, 10);
    if (sizeNum < 1024 * 1024) {
      return `${(sizeNum / 1024).toFixed(1)}KB`;
    }
    return `${(sizeNum / (1024 * 1024)).toFixed(1)}MB`;
  };

  // 格式化时长
  const formatDuration = (seconds?: number) => {
    if (!seconds) return;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return {
    id: apiMaterial.mediaId,
    observationId: apiMaterial.observationId,
    title: apiMaterial.name,
    type: typeMap[apiMaterial.type] || 'video',
    size: formatFileSize(apiMaterial.fileSize),
    author: apiMaterial.createUser.name,
    date: apiMaterial.createTime,
    duration: formatDuration(apiMaterial.duration),
    status: apiMaterial.handleStatus,
  };
};

export default function App({ projectId }: { projectId?: string }) {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState('all');
  const pageSize = 10;
  const version = useCommonStore((state) => state.version);
  const brand = useCommonStore((state) => state.brand);

  const {
    data: infiniteMaterialData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['materialList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await getMaterialList({
        projectId,
        page: pageParam,
        perPage: pageSize,
      });

      const responseData = response as unknown as Record<string, unknown>;
      const materials = Array.isArray(responseData)
        ? responseData
        : Array.isArray(responseData.list as unknown[])
          ? (responseData.list as unknown[])
          : [];

      return {
        data: materials,
        page: pageParam,
        hasMore: materials.length >= pageSize,
      };
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.hasMore) {
        return;
      }
      return lastPage.page + 1;
    },
  });

  useEffect(() => {
    getMessage(onMessage);
  }, []);

  // 获取到原生通知
  const onMessage = useCallback(
    (event: { data: string }) => {
      console.log('获取到原生通知 data: ', event);
      try {
        const data = JSON.parse(event.data);
        console.log('🚀 ~ data:', data);
        if (data.activity_on_resume) {
          refetch();
        }
      } catch (error) {
        console.log('onMessage', error);
      }
    },
    [refetch]
  );

  // 将 API 数据转换为 UI 数据
  const materialsList = infiniteMaterialData?.pages
    ? infiniteMaterialData.pages.flatMap((page) => {
        if (page?.data) {
          return page.data.map((item) =>
            mapApiToUiMaterial(item as ApiMaterial)
          );
        }
        return [];
      })
    : [];

  // 根据选择的类型本地过滤
  const filteredMaterials =
    selectedType === 'all'
      ? materialsList
      : materialsList.filter((material) => material.type === selectedType);

  // 按 observationId 分组
  const groupedMaterials = filteredMaterials.reduce(
    (acc, material) => {
      const key = material.observationId;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(material);
      return acc;
    },
    {} as Record<string, Material[]>
  );

  // 加载更多数据
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <PlayCircle className="h-8 w-8 text-blue-500" />;
      case 'audio':
        return <Mic className="h-8 w-8 text-purple-500" />;
      case 'image':
        return <Image className="h-8 w-8 text-green-500" />;
      default:
        return null;
    }
  };

  const getTypeBackgroundColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'bg-blue-100';
      case 'audio':
        return 'bg-purple-100';
      case 'image':
        return 'bg-green-100';
      default:
        return 'bg-gray-100';
    }
  };

  // 渲染单个素材项的辅助函数
  const renderMaterialItem = (material: Material) => (
    <div
      className="media-item mb-3 overflow-hidden rounded-xl bg-white shadow-sm"
      key={material.id} // 使用 bg-white 并添加 mb-3
      onClick={() => router.push(`/pbl/material/detail?id=${material.id}`)}
    >
      <div className="flex items-start p-3">
        <div className="relative">
          <div
            className={`h-16 w-16 ${getTypeBackgroundColor(material.type)} flex items-center justify-center rounded-lg`}
          >
            {getTypeIcon(material.type)}
          </div>
          {material.duration && (
            <span className="-bottom-1 -right-1 absolute rounded bg-white px-1 text-gray-500 text-xs shadow">
              {material.duration}
            </span>
          )}
        </div>
        <div className="ml-4 flex-1">
          <div className="flex items-start justify-between">
            <h3 className="max-w-[45vw] whitespace-break-spaces break-all text-base text-gray-800">
              {material.title || '未命名文件'}
            </h3>
            <div className="flex items-center space-x-2">
              <StatusBadge status={material.status} />
            </div>
          </div>
          <div className="mt-1 flex items-center text-gray-500 text-xs">
            <span>{material.size}</span>
          </div>
          <div className="mt-2 flex items-center text-gray-500 text-xs">
            <UserCircle className="mr-1 h-3 w-3 text-gray-600" />
            <span className="mr-3">{material.author}</span>
            <span>{material.date}</span>
          </div>
        </div>
      </div>
    </div>
  );

  // 处理下拉刷新
  const handleRefresh = async () => {
    return refetch();
  };

  if (isLoading) {
    return (
      <div className="py-8 text-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <main className="min-h-main bg-slate-50 px-4 py-6">
      <PullToRefresh
        canReleaseText="释放立即刷新"
        completeDelay={500}
        completeText="刷新成功"
        onRefresh={handleRefresh}
      >
        <div className="mb-2 pb-4">
          <div className="grid grid-cols-4 gap-2">
            <button
              className={`whitespace-nowrap px-4 py-2 ${selectedType === 'all' ? 'bg-violet-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-full font-medium text-sm shadow-sm`}
              onClick={() => setSelectedType('all')}
              type="button"
            >
              全部
            </button>
            <button
              className={`whitespace-nowrap px-4 py-2 ${selectedType === 'video' ? 'bg-violet-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-full font-medium text-sm shadow-sm`}
              onClick={() => setSelectedType('video')}
              type="button"
            >
              <Video
                className={`mr-1 inline h-4 w-4 ${selectedType === 'video' ? 'text-white' : 'text-violet-500'}`}
              />
              视频
            </button>
            <button
              className={`whitespace-nowrap px-4 py-2 ${selectedType === 'audio' ? 'bg-violet-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-full font-medium text-sm shadow-sm`}
              onClick={() => setSelectedType('audio')}
              type="button"
            >
              <Mic
                className={`mr-1 inline h-4 w-4 ${selectedType === 'audio' ? 'text-white' : 'text-blue-500'}`}
              />
              录音
            </button>
            <button
              className={`whitespace-nowrap px-4 py-2 ${selectedType === 'image' ? 'bg-violet-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-full font-medium text-sm shadow-sm`}
              onClick={() => setSelectedType('image')}
              type="button"
            >
              <Image
                className={`mr-1 inline h-4 w-4 ${selectedType === 'image' ? 'text-white' : 'text-green-500'}`}
              />
              图片
            </button>
          </div>
        </div>
        {Object.keys(groupedMaterials).length === 0 && !isLoading ? (
          <main className="flex flex-col items-center justify-center bg-slate-50 pt-20">
            <Empty title="暂无素材" />
            <div className="rounded-lg p-4 text-center text-gray-500 text-xs">
              素材请在观察记录中添加
            </div>
          </main>
        ) : (
          <div className="mb-12">
            {Object.entries(groupedMaterials).map(
              ([observationId, materialsInGroup]) => {
                if (materialsInGroup.length === 1) {
                  const singleMaterial = materialsInGroup[0];
                  if (singleMaterial) {
                    return renderMaterialItem(singleMaterial);
                  }
                  return null;
                }
                return (
                  <div
                    className="mb-4 rounded-xl border border-indigo-50 bg-indigo-50 p-2 shadow-sm"
                    key={observationId}
                  >
                    <div className="space-y-3">
                      {materialsInGroup.map((material) =>
                        renderMaterialItem(material)
                      )}
                    </div>
                  </div>
                );
              }
            )}
            {/* 无限滚动加载组件 */}
            <InfiniteScroll
              className="!py-2"
              hasMore={!!hasNextPage}
              loadMore={loadMore}
              threshold={250}
            >
              {isFetchingNextPage ? (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">加载更多数据...</span>
                </div>
              ) : hasNextPage ? (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">上拉加载更多</span>
                </div>
              ) : Object.keys(groupedMaterials).length > 0 &&
                filteredMaterials.length > 0 ? (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">没有更多数据了</span>
                </div>
              ) : null}
            </InfiniteScroll>
          </div>
        )}
      </PullToRefresh>
      {!projectId && (
        <button
          className="fixed right-6 bottom-10 flex h-14 w-14 items-center justify-center rounded-full bg-violet-500 text-white shadow-lg transition-colors hover:bg-violet-600"
          onClick={() => {
            if (
              (brand === '1' &&
                compare(version, '1.33.0', '>') &&
                compare(version, '1.34.0', '<')) ||
              (brand === '2' &&
                compare(version, '6.21.2', '>=') &&
                compare(version, '6.22.0', '<'))
            ) {
              navigationToNativePage(
                'app://app/pbl/addMaterials?videoCompressBitrate=10000000'
              );
            } else {
              router.push(`/pbl/material/create?projectId=${projectId}`);
            }
          }}
          type="button"
        >
          <Plus className="h-6 w-6" />
        </button>
      )}
    </main>
  );
}
