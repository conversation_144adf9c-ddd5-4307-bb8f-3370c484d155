'use client';
import React, { useEffect, useRef } from 'react';

import Evaluation, {
  type EvaluationRef
} from '../../create/components/Evaluation';
import {
  updateObservationAbility,
  createObservationAbility,
  deleteObservationAbility
} from '@/api/pbl';
import { studentAtom } from '@/store/pbl';
import { Dialog, Toast } from 'antd-mobile';
import { Check, Trash2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { evaluationAtom } from '@/store/pbl';
import { useAtom } from 'jotai';

const EvaluationPage = () => {
  const searchParams = useSearchParams();
  const isEdit = searchParams?.get('isEdit') || false;
  const [student] = useAtom(studentAtom);
  console.log('🚀 ~ student:', student);
  const router = useRouter();
  const evaluationRefs = useRef<EvaluationRef>(null);
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom);
  console.log('🚀 ~ allEvaluations:', allEvaluations);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = `${student?.name} 幼儿能力评估`;
    }
  }, []);

  const submit = () => {
    const studentEvaluations = allEvaluations[student?.id] || {};

    const abilities = Object.entries(studentEvaluations)
      .filter(([_, value]) => value === true)
      .map(([abilityId]) => ({ abilityId: Number(abilityId) }));
    if (isEdit) {
      updateObservationAbility(student?.evaluation?.evaluationId, {
        studentId: student?.id,
        observationId: student?.evaluation?.observationId,
        abilities
      }).then(() => {
        Toast.show({
          content: '保存成功',
          icon: 'success'
        });
        router.back();
      });
    } else {
      createObservationAbility({
        studentId: student?.id,
        deptId: student?.deptId,
        observationId: student?.evaluation?.observationId,
        abilities
      }).then(() => {
        Toast.show({
          content: '保存成功',
          icon: 'success'
        });
        router.back();
      });
    }
  };

  const deleteStudent = () => {
    Dialog.confirm({
      content: '确定从观察记录中删除此学生吗？',
      cancelText: '取消',
      confirmText: <div className="text-red-400">确认删除</div>,
      onConfirm: async () => {
        deleteObservationAbility(student?.evaluation?.evaluationId).then(() => {
          Toast.show({
            content: '删除成功',
            icon: 'success'
          });
          router.back();
        });
      }
    });
  };

  return (
    <div className="pb-20">
      <div className="p-3">
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center">
            <img
              src={student?.avatar || ''}
              alt=""
              className="w-8 h-8 rounded-full mr-2 object-cover"
            />
            <span className="font-medium">{student?.name}</span>
          </div>
          {isEdit && (
            <div className="text-red-500 text-xs" onClick={deleteStudent}>
              <Trash2 className="w-4 h-4" />
            </div>
          )}
        </div>
        <Evaluation ref={evaluationRefs} studentId={student?.id} />
      </div>
      <div className="fixed bottom-0 left-0 right-0 flex items-center justify-center pb-3">
        <button
          type="button"
          className="bg-indigo-500 text-white shadow-none focus:ring-4 text-base font-medium rounded-full px-8 py-3 focus:outline-none dark:focus:ring-blue-800"
          onClick={submit}
        >
          <div className="flex items-center justify-center">
            <Check /> <span className="ml-1">保存</span>
          </div>
        </button>
      </div>
    </div>
  );
};

export default EvaluationPage;
