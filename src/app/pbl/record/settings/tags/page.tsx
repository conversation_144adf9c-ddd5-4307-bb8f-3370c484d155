'use client';
import { Button, Dialog, Form, Input, Toast } from 'antd-mobile';
import { Edit, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  createReportTag,
  deleteReportTag,
  getReportTags,
  updateReportTag,
} from '@/api/pbl';

interface TagItem {
  tagId: string;
  tagName: string;
  tagType: number;
}
export default function TagManagePage() {
  const [form] = Form.useForm();
  const [systemTags, setSystemTags] = useState<TagItem[]>([]);
  const [customTags, setCustomTags] = useState<TagItem[]>([]);
  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    const response = await getReportTags();
    // @ts-expect-error
    const data = response.list;

    // 系统标签
    const systems: TagItem[] = data.filter(
      (item: { tagType: number }) => item.tagType === 1
    );

    // 自定义标签
    const customs: TagItem[] = data.filter(
      (item: { tagType: number }) => item.tagType === 2
    );
    setSystemTags(systems);
    setCustomTags(customs);
  };

  const onAdd = () => {
    form.resetFields();
    Dialog.show({
      title: '添加自定义标签',
      content: (
        <Form form={form} layout="vertical">
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input maxLength={32} placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const newTagName = values.name.trim();

                // 检查标签名称是否已存在（包括系统标签和自定义标签）
                const allTags = [...systemTags, ...customTags];
                const existingTag = allTags.find(
                  (tag) => tag.tagName === newTagName
                );
                if (existingTag) {
                  Toast.show({
                    content: '该标签名称已存在，请使用其他名称',
                  });
                  return;
                }

                const newTag: TagItem = {
                  tagId: Date.now().toString(),
                  tagName: newTagName,
                  tagType: 2,
                };
                createReportTag({
                  tagId: newTag.tagId,
                  tagName: newTag.tagName,
                  tagType: newTag.tagType,
                }).then(() => {
                  Toast.show({
                    icon: 'success',
                    content: '添加成功',
                  });
                  fetchTags();
                });
              } catch (error) {
                console.log(error);
              }
            },
          },
        ],
      ],
    });
  };

  const onEdit = (tag: TagItem) => {
    if (tag.tagType === 1) {
      Toast.show({
        icon: 'fail',
        content: '系统标签不可编辑',
      });
      return;
    }

    form.resetFields();
    form.setFieldsValue({ name: tag.tagName });
    Dialog.show({
      title: '编辑自定义标签',
      content: (
        <Form form={form} layout="horizontal">
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input maxLength={32} placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
          },
          {
            key: 'submit',
            text: '提交',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const newTagName = values.name.trim();

                // 检查标签名称是否已存在（排除当前编辑的标签）
                const allTags = [...systemTags, ...customTags];
                const existingTag = allTags.find(
                  (t) => t.tagName === newTagName && t.tagId !== tag.tagId
                );
                if (existingTag) {
                  Toast.show({
                    content: '该标签名称已存在，请使用其他名称',
                  });
                  return;
                }

                updateReportTag(tag.tagId, {
                  sort: 1,
                  tagName: newTagName,
                }).then(() => {
                  Toast.show({
                    icon: 'success',
                    content: '更新成功',
                  });
                  fetchTags();
                });
              } catch (error) {
                console.log(error);
              }
            },
          },
        ],
      ],
    });
  };

  const onDelete = (tag: TagItem) => {
    if (tag.tagType === 1) {
      Toast.show({
        icon: 'fail',
        content: '系统标签不可删除',
      });
      return;
    }

    Dialog.confirm({
      content: `确定要删除标签"${tag.tagName}"吗？`,
      onConfirm: async () => {
        await deleteReportTag(tag.tagId).then(() => {
          const updatedTags = customTags.filter((t) => t.tagId !== tag.tagId);
          setCustomTags(updatedTags);
          Toast.show({
            icon: 'success',
            content: '删除成功',
          });
        });
      },
    });
  };

  return (
    <div className="bg-slate-50 py-4">
      <div className="mb-4 flex items-center justify-between px-4">
        <h1 className="font-medium text-lg">标签管理</h1>
        <Button color="primary" onClick={onAdd} size="small">
          添加标签
        </Button>
      </div>

      {/* 自定义标签 */}
      <div className="mx-4 mb-4 rounded-lg bg-white shadow-sm">
        <div className="border-gray-100 border-b px-4 py-3 font-medium text-gray-600 text-sm">
          自定义标签
        </div>
        {customTags.map((tag) => (
          <div
            className="flex items-center justify-between border-gray-100 border-b px-4 py-3 last:border-b-0"
            key={tag.tagId}
          >
            <div className="flex min-w-0 max-w-200 flex-1 items-center overflow-hidden">
              <span className="text-ellipsis break-words text-gray-800">
                {tag.tagName}
              </span>
            </div>
            <div className="flex flex-shrink-0 gap-2">
              <Button
                color="primary"
                fill="none"
                onClick={() => onEdit(tag)}
                size="small"
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                color="danger"
                fill="none"
                onClick={() => onDelete(tag)}
                size="small"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* 系统标签 */}
      <div className="mx-4 rounded-lg bg-white shadow-sm">
        <div className="border-gray-100 border-b px-4 py-3 font-medium text-gray-600 text-sm">
          系统标签
        </div>
        {systemTags.map((tag) => (
          <div
            className="border-gray-100 border-b px-4 py-3 last:border-b-0"
            key={tag.tagId}
          >
            <span className="break-words text-gray-800">{tag.tagName}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
