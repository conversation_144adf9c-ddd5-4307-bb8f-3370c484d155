/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { List, Switch, Toast } from 'antd-mobile';
import { BookOpen, Brain, Heart } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { getObservationDimensions, setObservationDimensions } from '@/api/pbl';

interface ScaleConfig {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
}

// 默认配置数据
const DEFAULT_SCALE_CONFIGS: ScaleConfig[] = [
  {
    id: '2',
    title: '3-6 岁儿童学习与发展指南',
    description: '基于国家教育部发布的儿童发展指南进行评估',
    icon: <BookOpen className="h-5 w-5 text-blue-500" />,
    enabled: false,
  },
  {
    id: '3',
    title: '幸福感与参与度',
    description: '评估儿童在活动中的幸福感和参与程度',
    icon: <Heart className="h-5 w-5 text-red-500" />,
    enabled: false,
  },
  {
    id: '1',
    title: '深度学习',
    description: '评估儿童的深度学习能力和思维发展',
    icon: <Brain className="h-5 w-5 text-purple-500" />,
    enabled: false,
  },
];

export default function ScalePage() {
  const [scaleConfigs, setScaleConfigs] = useState<ScaleConfig[]>(
    DEFAULT_SCALE_CONFIGS
  );

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察指标配置';
    }
    getObservationDimensions().then((res: any) => {
      const newScaleConfigs = scaleConfigs.map((item) => {
        const found = res.dimensions.find((i: any) => i === item.id);
        if (found) {
          return {
            ...item,
            enabled: true,
          };
        }
        return item;
      });
      setScaleConfigs(newScaleConfigs);
    });
  }, []);

  const handleToggle = useCallback(
    (id: string, checked: boolean) => {
      // 如果要禁用，检查是否至少保留一个启用的指标
      if (!checked) {
        const enabledConfigs = scaleConfigs.filter(
          (config) => config.enabled && config.id !== id
        );
        if (enabledConfigs.length === 0) {
          Toast.show({
            content: '至少需要启用一个指标',
            duration: 2000,
          });
          return;
        }
      }

      let dimensions = scaleConfigs
        .filter((config) => config.enabled)
        .map((item) => item.id);
      if (checked) {
        dimensions.push(id);
      } else {
        dimensions = dimensions.filter((i) => i !== id);
      }
      setObservationDimensions({
        dimensions,
      }).then(() => {
        setScaleConfigs((prev) =>
          prev.map((config) =>
            config.id === id ? { ...config, enabled: checked } : config
          )
        );
      });

      // 显示操作反馈
      Toast.show({
        content: checked ? '已启用该指标' : '已禁用该指标',
        duration: 1500,
      });
    },
    [scaleConfigs]
  );

  const enabledCount = scaleConfigs.filter((config) => config.enabled).length;

  return (
    <main className="min-h-screen bg-slate-50">
      <div className="">
        <div className="p-4">
          <h1 className="mb-2 font-semibold text-gray-800 text-xl">
            观察指标配置
          </h1>
          <p className="mb-4 text-gray-500 text-sm">
            配置观察记录中使用的评估指标，已启用 {enabledCount} 个指标
          </p>
        </div>

        <List mode="card" style={{ '--border-inner': 'none' }}>
          {scaleConfigs.map((config) => (
            <List.Item
              className="!py-3"
              description={config.description}
              extra={
                <Switch
                  checked={config.enabled}
                  onChange={(checked) => handleToggle(config.id, checked)}
                />
              }
              key={config.id}
              prefix={config.icon}
            >
              <div className="font-medium text-gray-800">{config.title}</div>
            </List.Item>
          ))}
        </List>

        {/* 使用说明 */}
        <div className="mt-4 p-4">
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <h3 className="mb-2 font-medium text-blue-800 text-sm">使用说明</h3>
            <ul className="space-y-1 text-blue-700 text-xs">
              <li>• 启用的指标将在创建观察记录时可选择使用</li>
              <li>• 建议至少启用一个指标以确保评估的完整性</li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  );
}
