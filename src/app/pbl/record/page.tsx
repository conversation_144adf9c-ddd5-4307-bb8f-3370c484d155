'use client';

import { useQuery } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import {
  BookOpenCheck,
  FileText,
  Library,
  Settings,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { getObservationClassList } from '@/api/pbl';
import { currentTimeRangeAtom } from '@/store/timeZoneSelectorAtoms';
import TimeSelector, { type TimeRange } from './components/TimeSelector';

// 定义班级数据类型
interface ClassItem {
  id: string;
  name: string;
  evaluatedStudentCnt: number;
  studentCnt: number;
  observationCnt: number;
}

// 定义 API 响应类型
interface ApiResponse {
  list: ClassItem[];
  total?: number;
  page?: number;
  size?: number;
}

// 统计数据类型定义
interface ClassStats {
  observed: number; // 已观察
  pending: number; // 待观察
  records: number; // 记录数
  avgObservation: number; // 人均观察
  observationRate: number; // 观察率
}

// 计算班级统计数据
const calculateClassStats = (classItem: ClassItem): ClassStats => {
  const observed = classItem.evaluatedStudentCnt;
  const pending = classItem.studentCnt - classItem.evaluatedStudentCnt;
  const records = classItem.observationCnt;
  const avgObservation = observed > 0 ? records / observed : 0; // 简化为固定值，实际应根据业务逻辑计算
  const observationRate =
    classItem.studentCnt > 0 ? (observed / classItem.studentCnt) * 100 : 0;

  return {
    observed,
    pending,
    records,
    avgObservation,
    observationRate,
  };
};

// 统计展示组件
const ClassStatsDisplay = ({ stats }: { stats: ClassStats }) => {
  return (
    <div className="mt-4 grid grid-cols-5 gap-2 rounded-lg bg-gray-50 px-2 py-3">
      <div className="text-center">
        <div className="mb-1 font-bold text-gray-800 text-lg">
          {stats.observed}
        </div>
        <div className="font-medium text-gray-500 text-xs">已观察</div>
      </div>
      <div className="text-center">
        <div className="mb-1 font-bold text-gray-800 text-lg">
          {stats.pending}
        </div>
        <div className="font-medium text-gray-500 text-xs">待观察</div>
      </div>
      <div className="text-center">
        <div className="mb-1 font-bold text-gray-800 text-lg">
          {stats.records}
        </div>
        <div className="font-medium text-gray-500 text-xs">记录数</div>
      </div>
      <div className="text-center">
        <div className="mb-1 font-bold text-gray-800 text-lg">
          {stats.avgObservation.toFixed(1)}
        </div>
        <div className="font-medium text-gray-500 text-xs">人均观察</div>
      </div>
      <div className="text-center">
        <div className="mb-1 font-bold text-gray-800 text-lg">
          {stats.observationRate.toFixed(0)}%
        </div>
        <div className="font-medium text-gray-500 text-xs">观察率</div>
      </div>
    </div>
  );
};

export default function App() {
  if (typeof document !== 'undefined') {
    document.title = '观察记录';
  }
  const router = useRouter();

  // 使用全局时间状态
  const [currentTimeRange] = useAtom(currentTimeRangeAtom);

  // 构建 API 请求参数
  const buildApiParams = useMemo(() => {
    const params: { observeDate?: string[] } = {};

    if (currentTimeRange) {
      // 检查时间格式，如果已经包含时间部分则直接使用，否则添加时间部分
      const formatTimeString = (timeStr: string, isEndTime = false) => {
        if (timeStr.includes(' ')) {
          // 已经包含时间部分，直接返回
          return timeStr;
        }
        // 只有日期部分，添加时间
        return isEndTime ? `${timeStr} 23:59:59` : `${timeStr} 00:00:00`;
      };

      params.observeDate = [
        formatTimeString(currentTimeRange.startTime),
        formatTimeString(currentTimeRange.endTime, true),
      ];
    }

    return params;
  }, [currentTimeRange]);

  // 使用 react-query 获取班级列表数据
  const { data, isLoading, error, refetch } = useQuery<ApiResponse>({
    queryKey: ['observationClassList', currentTimeRange],
    queryFn: () =>
      getObservationClassList(
        buildApiParams
      ) as unknown as Promise<ApiResponse>,
  });

  // 获取班级列表，如果数据不存在则提供空数组作为默认值
  const classes = data?.list || [];

  // 处理时间选择确认
  const handleTimeConfirm = (timeRange?: TimeRange) => {
    // 立即触发 API 请求
    refetch();
    console.log('选择的时间段:', timeRange);
  };

  return (
    <main className="bg-slate-50">
      <div className="p-4">
        {/* 时间选择器 */}
        <TimeSelector className="mb-4" onConfirm={handleTimeConfirm} />
        <div className="mb-4 flex items-center justify-between px-2">
          <div
            className="flex cursor-pointer flex-col items-center space-y-2"
            onClick={() => {
              router.push('/pbl/');
            }}
            onKeyDown={() => null}
            role="button"
            tabIndex={0}
          >
            <div
              className={
                'flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100'
              }
            >
              <BookOpenCheck className="size-5 text-emerald-500" />
            </div>
            <span className="text-gray-700 text-xs">PBL 教学</span>
          </div>
          <div
            className="flex cursor-pointer flex-col items-center space-y-2"
            onClick={() => {
              router.push('/pbl/record/report');
            }}
            onKeyDown={() => null}
            role="button"
            tabIndex={0}
          >
            <div
              className={
                'flex h-12 w-12 items-center justify-center rounded-full bg-orange-100'
              }
            >
              <FileText className="h-5 w-5 text-orange-500" />
            </div>
            <span className="text-gray-700 text-xs">评价报告</span>
          </div>
          <div
            className="flex cursor-pointer flex-col items-center space-y-2"
            onClick={() => {
              router.push('/pbl/material?isShowPblTaskMenu=true');
            }}
            onKeyDown={() => null}
            role="button"
            tabIndex={0}
          >
            <div
              className={
                'flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100'
              }
            >
              <Library className="h-5 w-5 text-indigo-500" />
            </div>
            <span className="text-gray-700 text-xs">素材管理</span>
          </div>
          {/* <div
            className="flex flex-col items-center space-y-2 cursor-pointer"
            onClick={() => {
              router.push('/pbl/record/guide');
            }}
          >
            <div
              className={
                'w-12 h-12 rounded-full flex items-center justify-center bg-purple-100'
              }
            >
              <Compass className="w-5 h-5 text-purple-500" />
            </div>
            <span className="text-xs text-gray-700">观察指引</span>
          </div> */}
          <div
            className="flex cursor-pointer flex-col items-center space-y-2"
            onClick={() => {
              router.push('/pbl/record/settings');
            }}
            onKeyDown={() => null}
            role="button"
            tabIndex={0}
          >
            <div
              className={
                'flex h-12 w-12 items-center justify-center rounded-full bg-blue-100'
              }
            >
              <Settings className="h-5 w-5 text-blue-500" />
            </div>
            <span className="text-gray-700 text-xs">系统设置</span>
          </div>
        </div>
        <div className="">
          {(() => {
            if (isLoading) {
              return <div className="py-10 text-center">加载中...</div>;
            }

            if (error) {
              return (
                <div className="py-10 text-center text-red-500">
                  加载失败，请稍后重试
                </div>
              );
            }

            if (classes.length === 0) {
              return (
                <div className="py-10 text-center text-gray-500">
                  暂无班级数据
                </div>
              );
            }

            return classes.map((classItem: ClassItem) => {
              const stats = calculateClassStats(classItem);

              return (
                <div
                  className="card-hover mb-4 cursor-pointer rounded-xl bg-white px-4 py-5 shadow-sm"
                  key={classItem.id}
                  onClick={() => {
                    router.push(
                      `/pbl/record/class?id=${classItem.id}&name=${classItem.name}&isShowPblTaskMenu=true`
                    );
                  }}
                  onKeyDown={() => null}
                  role="button"
                  tabIndex={0}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="flex items-center gap-2 font-semibold text-xl">
                        <Users className="h-5 w-5 text-gray-600" />
                        {classItem.name}
                      </h2>
                    </div>
                  </div>
                  {(() => {
                    if (classItem.evaluatedStudentCnt === 0) {
                      return (
                        <div className="mt-3 text-gray-500">还没有记录</div>
                      );
                    }
                    return <ClassStatsDisplay stats={stats} />;
                  })()}
                </div>
              );
            });
          })()}
        </div>
      </div>
    </main>
  );
}
