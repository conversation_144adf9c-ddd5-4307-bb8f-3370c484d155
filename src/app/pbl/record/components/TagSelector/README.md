# TagSelector 标签选择组件

一个用于选择标签的弹窗组件，支持搜索、多选等功能。

## 功能特性

- 🔍 支持搜索过滤标签
- ✅ 支持多选标签
- 📱 移动端友好的弹窗界面
- 🎨 美观的UI设计
- 🔧 高度可定制

## 使用方法

### 基本用法

```tsx
import TagSelector, { type TagOption } from '@/components/TagSelector';

const [tagPopupVisible, setTagPopupVisible] = useState(false);
const [selectedTags, setSelectedTags] = useState<TagOption[]>([]);

// 标签数据
const tagOptions: TagOption[] = [
  { id: 'tag_1', name: '自由游戏' },
  { id: 'tag_2', name: '集体教学' },
  // ... 更多标签
];

<TagSelector
  visible={tagPopupVisible}
  onClose={() => setTagPopupVisible(false)}
  selectedTags={selectedTags}
  onTagsChange={setSelectedTags}
  tagOptions={tagOptions}
/>
```

### 完整示例

```tsx
import { useState } from 'react';
import TagSelector, { type TagOption } from '@/components/TagSelector';

export default function MyComponent() {
  const [tagPopupVisible, setTagPopupVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState<TagOption[]>([]);

  const tagOptions: TagOption[] = [
    { id: 'tag_1', name: '自由游戏' },
    { id: 'tag_2', name: '集体教学' },
    { id: 'tag_3', name: '小组合作' },
    { id: 'tag_4', name: '一对一辅导' },
  ];

  return (
    <div>
      {/* 触发按钮 */}
      <button onClick={() => setTagPopupVisible(true)}>
        选择标签 ({selectedTags.length})
      </button>

      {/* 显示已选标签 */}
      <div>
        {selectedTags.map(tag => (
          <span key={tag.id}>#{tag.name}</span>
        ))}
      </div>

      {/* 标签选择器 */}
      <TagSelector
        visible={tagPopupVisible}
        onClose={() => setTagPopupVisible(false)}
        selectedTags={selectedTags}
        onTagsChange={setSelectedTags}
        tagOptions={tagOptions}
        title="选择内容标签"
        searchPlaceholder="搜索标签..."
      />
    </div>
  );
}
```

## API 参数

### TagSelectorProps

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| visible | boolean | ✅ | - | 是否显示弹窗 |
| onClose | () => void | ✅ | - | 关闭弹窗的回调 |
| selectedTags | TagOption[] | ✅ | - | 已选择的标签数组 |
| onTagsChange | (tags: TagOption[]) => void | ✅ | - | 标签变化的回调 |
| tagOptions | TagOption[] | ❌ | 默认标签 | 可选择的标签列表 |
| title | string | ❌ | '选择标签' | 弹窗标题 |
| searchPlaceholder | string | ❌ | '输入关键词搜索或添加自定义标签' | 搜索框占位符 |

### TagOption

| 参数 | 类型 | 说明 |
|------|------|------|
| id | string | 标签唯一标识 |
| name | string | 标签显示名称 |

## 样式定制

组件使用 Tailwind CSS 进行样式设计，你可以通过以下方式进行定制：

1. 修改组件内部的 className
2. 使用 CSS 变量覆盖默认样式
3. 通过 Popup 的 bodyStyle 属性调整弹窗样式

## 注意事项

1. 组件依赖 `antd-mobile` 的 `Popup` 组件
2. 图标使用 `lucide-react`
3. 样式工具函数使用 `@/lib/utils` 中的 `cn`
4. 确保项目中已安装相关依赖
