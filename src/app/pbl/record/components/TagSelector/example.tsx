'use client';

import { useState } from 'react';
import TagSelector, { type TagOption } from './index';

// 示例标签数据
const exampleTags: TagOption[] = [
  { id: 'tag_1', name: '自由游戏' },
  { id: 'tag_2', name: '集体教学' },
  { id: 'tag_3', name: '小组合作' },
  { id: 'tag_4', name: '一对一辅导' },
  { id: 'tag_5', name: '角色扮演' },
  { id: 'tag_6', name: '建构操作' },
  { id: 'tag_7', name: '科学探究' },
  { id: 'tag_8', name: '艺术创作' },
  { id: 'tag_9', name: '运动挑战' },
  { id: 'tag_10', name: '主题探究' }
];

export default function TagSelectorExample() {
  const [visible, setVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState<TagOption[]>([]);

  return (
    <div className="p-4 max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-4">TagSelector 组件示例</h2>
      
      {/* 触发按钮 */}
      <button
        onClick={() => setVisible(true)}
        className="w-full py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        选择标签 ({selectedTags.length})
      </button>

      {/* 显示已选标签 */}
      <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">已选择的标签：</h3>
        {selectedTags.length === 0 ? (
          <p className="text-gray-400 text-sm">暂无选择标签</p>
        ) : (
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <span
                key={tag.id}
                className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm"
              >
                #{tag.name}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 标签选择器 */}
      <TagSelector
        visible={visible}
        onClose={() => setVisible(false)}
        selectedTags={selectedTags}
        onTagsChange={setSelectedTags}
        tagOptions={exampleTags}
        title="选择内容标签"
        searchPlaceholder="搜索标签或添加自定义标签"
      />
    </div>
  );
}
