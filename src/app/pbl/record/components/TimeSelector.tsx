/** biome-ignore-all lint/a11y/useSemanticElements: <explanation> */
'use client';

import { DatePicker, Popup, Tabs } from 'antd-mobile';
import { clsx } from 'clsx';
import { format, isAfter, isBefore } from 'date-fns';
import { useAtom } from 'jotai';
import { ChevronDown } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
  activeTabAtom,
  customDateRangeAtom,
  resetTimeSelectorAtom,
  selectedMonthAtom,
  selectedSemesterAtom,
} from '@/store/timeZoneSelectorAtoms';

// 学期选项类型
export interface SemesterOption {
  id: string;
  name: string;
  start: string;
  end: string;
}

// 月份选项类型
export interface MonthOption {
  id: string;
  name: string;
  start: string;
  end: string;
}

// 时间段类型
export interface TimeRange {
  startTime: string;
  endTime: string;
}

// 组件Props类型
export interface TimeSelectorProps {
  onConfirm: (timeRange?: TimeRange) => void;
  defaultTimeRange?: TimeRange;
  semesterOptions?: SemesterOption[];
  monthOptions?: MonthOption[];
  className?: string;
  defaultActiveTab?: 'semester' | 'month' | 'custom';
}

// 默认学期数据，最近四个学期
// 动态生成最近四个学期，符合中国学校学期划分
const generateRecentSemesters = (): SemesterOption[] => {
  const semesters: SemesterOption[] = [];
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 1-12

  // 当前学期起始年份
  let startYear = month >= 8 ? year : year - 1;
  // 当前学期类型
  let currentSemesterType = month >= 8 || month <= 1 ? '上学期' : '下学期';

  for (let i = 0; i < 4; i++) {
    const semType = currentSemesterType;
    const semStartYear = startYear;

    const semName = `${semStartYear}-${semStartYear + 1} ${semType}`;

    let start: string;
    let end: string;

    if (semType === '上学期') {
      start = `${semStartYear}-08-01 00:00:00`;
      end = `${semStartYear + 1}-02-17 23:59:59`;
    } else {
      start = `${semStartYear + 1}-02-18 00:00:00`;
      end = `${semStartYear + 1}-07-31 23:59:59`;
    }

    semesters.push({
      id: `${semStartYear}_${semType}`,
      name: semName,
      start,
      end,
    });

    // 计算上一个学期
    if (semType === '下学期') {
      currentSemesterType = '上学期';
    } else {
      currentSemesterType = '下学期';
      startYear -= 1;
    }
  }

  // 按时间从晚到早排序
  semesters.sort(
    (a, b) => new Date(b.start).getTime() - new Date(a.start).getTime()
  );

  return semesters;
};

const defaultSemesterOptions: SemesterOption[] = generateRecentSemesters();

// 生成从2024年9月到当前月份的月份选项
export const generateMonthOptions = (): MonthOption[] => {
  const options: MonthOption[] = [];
  const currentDate = new Date();

  // 从当前月份开始，往前生成12个月
  for (let i = 0; i < 12; i++) {
    const targetDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() - i,
      1
    );
    const year = targetDate.getFullYear();
    const month = targetDate.getMonth() + 1; // 转换为1-12
    const monthStr = month.toString().padStart(2, '0');

    // 计算该月的最后一天
    const lastDay = new Date(year, month, 0).getDate();
    const lastDayStr = lastDay.toString().padStart(2, '0');

    options.push({
      id: `${year}-${monthStr}`,
      name: `${year}年${month}月`,
      start: `${year}-${monthStr}-01 00:00:00`,
      end: `${year}-${monthStr}-${lastDayStr} 23:59:59`,
    });
  }

  return options; // 按时间倒序排列，最新的月份在前
};

// 默认月份数据 - 使用动态生成的选项
const defaultMonthOptions: MonthOption[] = generateMonthOptions();

// 计算自定义时间段
const calculateCustomTimeRange = (
  startDate: Date,
  endDate: Date
): { startTime: string; endTime: string } => {
  try {
    return {
      startTime: `${format(startDate, 'yyyy-MM-dd')} 00:00:00`,
      endTime: `${format(endDate, 'yyyy-MM-dd')} 23:59:59`,
    };
  } catch (error) {
    console.warn('Error calculating custom time range:', error);
    const now = new Date();
    return {
      startTime: `${format(now, 'yyyy-MM-dd')} 00:00:00`,
      endTime: `${format(now, 'yyyy-MM-dd')} 23:59:59`,
    };
  }
};

// 验证日期范围有效性
const isValidDateRange = (
  startDate: Date | null,
  endDate: Date | null
): boolean => {
  return !!(startDate && endDate && !isAfter(startDate, endDate));
};

export default function TimeSelector({
  onConfirm,
  defaultTimeRange,
  semesterOptions = defaultSemesterOptions,
  monthOptions = defaultMonthOptions,
  className = '',
}: TimeSelectorProps) {
  // 全局状态 - 用于显示和初始化
  const [globalSelectedSemester, setGlobalSelectedSemester] =
    useAtom(selectedSemesterAtom);
  const [globalSelectedMonth, setGlobalSelectedMonth] =
    useAtom(selectedMonthAtom);
  const [globalActiveTab, setGlobalActiveTab] = useAtom(activeTabAtom);
  const [globalCustomDateRange, setGlobalCustomDateRange] =
    useAtom(customDateRangeAtom);
  const [, resetTimeSelector] = useAtom(resetTimeSelectorAtom);

  // 本地状态管理 - 弹窗显示控制
  const [visible, setVisible] = useState(false);

  // 临时状态 - 用于弹窗内的选择，只有点击确定才同步到全局状态
  const [tempSelectedSemester, setTempSelectedSemester] = useState(
    globalSelectedSemester
  );
  const [tempSelectedMonth, setTempSelectedMonth] =
    useState(globalSelectedMonth);
  const [tempActiveTab, setTempActiveTab] = useState(globalActiveTab);
  const [tempCustomDateRange, setTempCustomDateRange] = useState(
    globalCustomDateRange
  );

  // 当弹窗打开时，同步全局状态到临时状态
  useEffect(() => {
    if (visible) {
      setTempSelectedSemester(globalSelectedSemester);
      setTempSelectedMonth(globalSelectedMonth);
      setTempActiveTab(globalActiveTab);
      setTempCustomDateRange(globalCustomDateRange);
    }
  }, [
    visible,
    globalSelectedSemester,
    globalSelectedMonth,
    globalActiveTab,
    globalCustomDateRange,
  ]);

  // 本地状态 - DatePicker 显示控制
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  // 创建适配器函数来处理 Tabs 组件的 onChange
  const handleTabChange = useCallback(
    (key: string) => {
      setTempActiveTab(key as 'semester' | 'month' | 'custom');
    },
    [setTempActiveTab]
  );

  // 自定义时间段状态 - 从临时状态中获取
  const customStartDate = tempCustomDateRange.startDate;
  const customEndDate = tempCustomDateRange.endDate;

  // 设置自定义日期的辅助函数
  const setCustomStartDate = useCallback(
    (date: Date | null) => {
      setTempCustomDateRange((prev) => ({ ...prev, startDate: date }));
    },
    [setTempCustomDateRange]
  );

  const setCustomEndDate = useCallback(
    (date: Date | null) => {
      setTempCustomDateRange((prev) => ({ ...prev, endDate: date }));
    },
    [setTempCustomDateRange]
  );

  // 性能优化：缓存 DatePicker min 值
  const minEndDate = useMemo(() => {
    if (customStartDate) {
      return customStartDate;
    }
    return;
  }, [customStartDate]);

  // 处理确认选择 - 使用useCallback优化
  const handleConfirm = useCallback(() => {
    // 验证选择的有效性
    if (
      tempActiveTab === 'semester' &&
      (!tempSelectedSemester?.id || tempSelectedSemester.id === '')
    ) {
      return;
    }
    if (
      tempActiveTab === 'month' &&
      (!tempSelectedMonth?.id || tempSelectedMonth.id === '')
    ) {
      return;
    }
    if (tempActiveTab === 'custom' && !(customStartDate && customEndDate)) {
      return;
    }

    // 同步临时状态到全局状态
    setGlobalSelectedSemester(tempSelectedSemester);
    setGlobalSelectedMonth(tempSelectedMonth);
    setGlobalActiveTab(tempActiveTab);
    setGlobalCustomDateRange(tempCustomDateRange);

    let timeRange: TimeRange | undefined;

    if (tempActiveTab === 'semester') {
      timeRange = {
        startTime: tempSelectedSemester.start,
        endTime: tempSelectedSemester.end,
      };
    } else if (tempActiveTab === 'month') {
      timeRange = {
        startTime: tempSelectedMonth.start,
        endTime: tempSelectedMonth.end,
      };
    } else if (tempActiveTab === 'custom') {
      // 自定义时间段
      if (customStartDate && customEndDate) {
        const { startTime, endTime } = calculateCustomTimeRange(
          customStartDate,
          customEndDate
        );
        timeRange = {
          startTime,
          endTime,
        };
      } else {
        return; // 如果日期无效，直接返回
      }
    }

    setVisible(false);
    if (timeRange) {
      onConfirm(timeRange);
    }
  }, [
    tempActiveTab,
    tempSelectedSemester,
    tempSelectedMonth,
    customStartDate,
    customEndDate,
    tempCustomDateRange,
    setGlobalSelectedSemester,
    setGlobalSelectedMonth,
    setGlobalActiveTab,
    setGlobalCustomDateRange,
    onConfirm,
  ]);

  // 获取显示文本 - 使用useMemo优化（基于全局状态显示）
  const displayText = useMemo(() => {
    // 优先根据当前激活的标签页显示对应内容
    if (globalActiveTab === 'semester') {
      if (globalSelectedSemester?.name) {
        return globalSelectedSemester.name;
      }
      return '选择学期';
    }

    if (globalActiveTab === 'month') {
      if (globalSelectedMonth?.name) {
        return globalSelectedMonth.name;
      }
      return '选择月份';
    }

    if (globalActiveTab === 'custom') {
      if (globalCustomDateRange.startDate && globalCustomDateRange.endDate) {
        return `${format(globalCustomDateRange.startDate, 'yyyy-MM-dd')} 至 ${format(globalCustomDateRange.endDate, 'yyyy-MM-dd')}`;
      }
      return '选择自定义时间段';
    }

    // 如果没有激活的标签页或者没有任何选择，显示默认文本
    return '选择时间';
  }, [
    globalActiveTab,
    globalSelectedSemester?.name,
    globalSelectedMonth?.name,
    globalCustomDateRange.startDate,
    globalCustomDateRange.endDate,
  ]);

  // 处理取消操作
  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  const handleReset = useCallback(() => {
    resetTimeSelector();
    onConfirm(undefined);
    setVisible(false);
  }, [resetTimeSelector, onConfirm]);

  return (
    <>
      {/* 触发按钮 */}
      <div
        className={clsx('cursor-pointer', className)}
        onClick={() => setVisible(true)}
        onKeyDown={() => null}
        role="button"
        tabIndex={0}
      >
        <div className="flex items-center">
          <span className="mr-2 font-medium text-sm">{displayText}</span>
          <ChevronDown className="h-4 w-4" />
        </div>
      </div>

      {/* 时间选择弹窗 */}
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          height: '70vh',
          maxHeight: '90vh',
        }}
        onMaskClick={handleCancel}
        position="bottom"
        visible={visible}
      >
        <div
          className="flex h-full flex-col"
          onKeyDown={() => null}
          role="button"
          tabIndex={-1}
        >
          {/* 固定头部 */}
          <div className="flex-shrink-0 border-gray-100 border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800 text-lg">选择时间</h3>
              <button
                className="text-gray-500 text-xl hover:text-gray-700"
                onClick={handleCancel}
                type="button"
              >
                ✕
              </button>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <div className="flex flex-1 flex-col">
            <Tabs
              activeKey={tempActiveTab}
              onChange={handleTabChange}
              style={{
                '--active-line-color': '#1677ff',
                '--active-title-color': '#1677ff',
                height: 'calc(70vh - 190px)', // 固定高度，为底部按钮留出空间
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Tabs.Tab key="semester" title="学期">
                <div
                  className="flex-1 overflow-y-auto px-4 py-2"
                  style={{ height: 'calc(70vh - 190px)' }}
                >
                  {semesterOptions.map((semester) => {
                    let c: string;
                    if (tempSelectedSemester?.id === semester.id) {
                      c = 'border border-blue-200 bg-blue-50';
                    } else {
                      c = 'bg-gray-50 hover:bg-gray-100';
                    }

                    return (
                      <div
                        className={clsx(
                          'mb-2 flex cursor-pointer items-center justify-between rounded-lg p-4 transition-colors',
                          c
                        )}
                        key={semester.id}
                        onClick={() => setTempSelectedSemester(semester)}
                        onKeyDown={() => null}
                        role="button"
                        tabIndex={0}
                      >
                        <span className="font-medium text-gray-800">
                          {semester.name}
                        </span>
                        {tempSelectedSemester?.id === semester.id && (
                          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500">
                            <div className="h-2 w-2 rounded-full bg-white" />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </Tabs.Tab>

              <Tabs.Tab key="month" title="月份">
                <div
                  className="flex-1 overflow-y-auto px-4 py-2"
                  style={{ height: 'calc(70vh - 190px)' }}
                >
                  {monthOptions.map((month) => {
                    let style: string;
                    if (tempSelectedMonth?.id === month.id) {
                      style = 'border border-blue-200 bg-blue-50';
                    } else {
                      style = 'bg-gray-50 hover:bg-gray-100';
                    }

                    return (
                      <div
                        className={clsx(
                          'mb-2 flex cursor-pointer items-center justify-between rounded-lg p-4 transition-colors',
                          style
                        )}
                        key={month.id}
                        onClick={() => setTempSelectedMonth(month)}
                        onKeyDown={() => null}
                        role="button"
                        tabIndex={0}
                      >
                        <span className="font-medium text-gray-800">
                          {month.name}
                        </span>
                        {tempSelectedMonth?.id === month.id && (
                          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-indigo-500">
                            <div className="h-2 w-2 rounded-full bg-white" />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </Tabs.Tab>

              <Tabs.Tab key="custom" title="自定义时间段">
                <div className="flex-1 overflow-y-auto px-4 py-2">
                  {/* 开始日期选择 */}
                  <div className="mb-6">
                    <label
                      className="mb-2 block font-medium text-gray-700 text-sm"
                      htmlFor="start-date-btn"
                    >
                      开始日期
                    </label>
                    <button
                      className={clsx(
                        'w-full rounded-lg border p-4 text-left transition-colors',
                        customStartDate
                          ? 'border-blue-200 bg-blue-50 text-gray-800'
                          : 'border-gray-200 bg-gray-50 text-gray-500 hover:bg-gray-100'
                      )}
                      id="start-date-btn"
                      onClick={() => setShowStartPicker(true)}
                      type="button"
                    >
                      {customStartDate
                        ? format(customStartDate, 'yyyy年MM月dd日')
                        : '请选择开始日期'}
                    </button>
                  </div>

                  {/* 结束日期选择 */}
                  <div className="mb-6">
                    <label
                      className="mb-2 block font-medium text-gray-700 text-sm"
                      htmlFor="end-date-btn"
                    >
                      结束日期
                    </label>
                    <button
                      className={clsx(
                        'w-full rounded-lg border p-4 text-left transition-colors',
                        customEndDate
                          ? 'border-blue-200 bg-blue-50 text-gray-800'
                          : 'border-gray-200 bg-gray-50 text-gray-500 hover:bg-gray-100'
                      )}
                      id="end-date-btn"
                      onClick={() => setShowEndPicker(true)}
                      type="button"
                    >
                      {customEndDate
                        ? format(customEndDate, 'yyyy年MM月dd日')
                        : '请选择结束日期'}
                    </button>
                  </div>

                  {/* 日期验证提示 */}
                  {customStartDate &&
                    customEndDate &&
                    isAfter(customStartDate, customEndDate) && (
                      <div className="rounded-lg border border-red-200 bg-red-50 p-3">
                        <p className="font-medium text-red-600 text-sm">
                          日期选择错误
                        </p>
                        <p className="mt-1 text-red-500 text-xs">
                          结束日期（{format(customEndDate, 'yyyy年MM月dd日')}
                          ）不能早于开始日期（
                          {format(customStartDate, 'yyyy年MM月dd日')}）
                        </p>
                      </div>
                    )}
                </div>
              </Tabs.Tab>
            </Tabs>
          </div>

          {/* DatePicker 组件 */}
          <DatePicker
            onClose={() => setShowStartPicker(false)}
            onConfirm={(value) => {
              setCustomStartDate(value);
              setShowStartPicker(false);
            }}
            precision="day"
            title="选择开始日期"
            visible={showStartPicker}
          />

          <DatePicker
            min={minEndDate}
            onClose={() => setShowEndPicker(false)}
            onConfirm={(value) => {
              // 验证结束日期不能早于开始日期
              if (customStartDate && isBefore(value, customStartDate)) {
                // 可以在这里添加提示逻辑，暂时不设置无效日期
                return;
              }
              setCustomEndDate(value);
              setShowEndPicker(false);
            }}
            precision="day"
            title="选择结束日期"
            visible={showEndPicker}
          />

          {/* 固定底部按钮 */}
          <div className="flex-shrink-0 border-gray-100 border-t p-4">
            <div className="flex gap-3">
              <button
                className="flex-1 rounded-full border border-gray-300 px-4 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                onClick={handleReset}
                type="button"
              >
                重置
              </button>
              <button
                className="flex-1 rounded-full bg-indigo-500 px-4 py-3 font-medium text-white transition-colors hover:bg-indigo-600 disabled:cursor-not-allowed disabled:bg-gray-300"
                disabled={
                  (tempActiveTab === 'semester' &&
                    (!tempSelectedSemester?.id ||
                      tempSelectedSemester.id === '')) ||
                  (tempActiveTab === 'month' &&
                    (!tempSelectedMonth?.id || tempSelectedMonth.id === '')) ||
                  (tempActiveTab === 'custom' &&
                    !isValidDateRange(customStartDate, customEndDate))
                }
                onClick={handleConfirm}
                type="button"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}
