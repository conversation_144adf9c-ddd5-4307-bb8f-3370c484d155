'use client';

import { Popup } from 'antd-mobile';
import { clsx } from 'clsx';
import { useAtom } from 'jotai';
import { ChevronDown, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { getReportAreas } from '@/api/pbl';
import { selectedRegionsAtom } from '@/store/timeZoneSelectorAtoms';
import type { RegionCategory, RegionSelectorProps } from './types';

export default function RegionSelector({
  onConfirm,
  className = '',
  placeholder = '全部区域',
}: Omit<RegionSelectorProps, 'defaultRegions'>) {
  // 状态管理 - 使用 jotai 全局状态
  const [visible, setVisible] = useState(false);
  const [selectedRegions, setSelectedRegions] = useAtom(selectedRegionsAtom);
  const [categories, setCategories] = useState<RegionCategory[]>([]);

  // 临时状态，用于存储用户在弹窗中的选择，只有确认时才同步到全局状态
  const [tempSelectedRegions, setTempSelectedRegions] = useState<
    RegionCategory[]
  >([]);

  useEffect(() => {
    getReportAreas().then((res: any) => {
      setCategories(res.list);
    });
  }, []);

  // 当弹窗打开时，初始化临时状态为当前全局状态
  useEffect(() => {
    if (visible) {
      setTempSelectedRegions([...selectedRegions]);
    }
  }, [visible, selectedRegions]);

  // 处理区域选择 - 使用useCallback优化，现在操作临时状态
  const handleRegionSelect = useCallback(
    (categoryId: string, categoryTitle: string, region: RegionCategory) => {
      const newSelection: RegionCategory = {
        zoneId: categoryId,
        zoneName: categoryTitle,
        parentId: region.parentId,
        children: region.children,
      };

      // 如果点击的是已选中的区域，则取消选中
      if (tempSelectedRegions?.find((item) => item.zoneId === region.zoneId)) {
        setTempSelectedRegions(
          tempSelectedRegions.filter((item) => item.zoneId !== region.zoneId)
        );
      } else {
        setTempSelectedRegions([...tempSelectedRegions, newSelection]);
      }
    },
    [tempSelectedRegions]
  );

  // 处理确认选择 - 使用useCallback优化，确认时才更新全局状态
  const handleConfirm = useCallback(() => {
    setVisible(false);
    // 将临时状态同步到全局状态
    setSelectedRegions(tempSelectedRegions);
    onConfirm(tempSelectedRegions);
  }, [tempSelectedRegions, onConfirm, setSelectedRegions]);

  // 处理取消选择
  const handleCancel = useCallback(() => {
    setVisible(false);
    // 重置临时状态，不改变全局状态
    setTempSelectedRegions([...selectedRegions]);
  }, [selectedRegions]);

  // 处理重置选择
  const handleReset = useCallback(() => {
    setTempSelectedRegions([]);
  }, []);

  // 获取显示文本 - 使用useMemo优化
  const displayText = useMemo(() => {
    if (selectedRegions.length) {
      return selectedRegions.map((item) => item.zoneName).join('、');
    }
    return placeholder;
  }, [selectedRegions, placeholder]);

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleCancel();
      }
      if (event.key === 'Enter') {
        handleConfirm();
      }
    },
    [handleConfirm, handleCancel]
  );

  return (
    <>
      {/* 触发按钮 */}
      <div
        className={clsx('cursor-pointer', className)}
        onClick={() => setVisible(true)}
        onKeyDown={() => null}
        role="button"
        tabIndex={0}
      >
        <div className="flex items-center">
          <span className="mr-2 font-medium text-sm">{displayText}</span>
          <ChevronDown className="h-4 w-4" />
        </div>
      </div>

      {/* 区域选择弹窗 */}
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
          height: '60vh',
          maxHeight: '500px',
        }}
        onMaskClick={handleCancel}
        position="bottom"
        visible={visible}
      >
        <div
          className="flex h-full flex-col"
          onKeyDown={handleKeyDown}
          role="button"
          tabIndex={-1}
        >
          {/* 固定头部 */}
          <div className="flex-shrink-0 border-gray-100 border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800 text-lg">
                选择观察区域
              </h3>
              <button
                className="rounded-full p-1 hover:bg-gray-100"
                onClick={handleCancel}
                type="button"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
          </div>

          {/* 可滚动内容区域 */}
          <div className="flex-1 overflow-y-auto p-4">
            {categories
              .filter((category) => category.children.length > 0)
              .map((category) => (
                <div className="mb-6" key={category.zoneId}>
                  {/* 分类标题 */}
                  <div className="mb-3 flex items-center">
                    <div className="mr-2 h-4 w-1 rounded bg-indigo-500" />
                    <h4 className="font-medium text-base text-gray-700">
                      {category.zoneName}
                    </h4>
                  </div>

                  {/* 区域选项网格 */}
                  <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
                    {category.children.map((item) => (
                      <button
                        className={clsx(
                          'rounded-lg px-4 py-3 font-medium text-sm transition-colors',
                          'border',
                          'min-h-[0px] break-words text-center leading-tight',
                          tempSelectedRegions.find(
                            (region) => region.zoneId === item.zoneId
                          )
                            ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                            : 'border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100'
                        )}
                        key={item.zoneId}
                        onClick={() =>
                          handleRegionSelect(item.zoneId, item.zoneName, item)
                        }
                        title={item.zoneName}
                        type="button"
                      >
                        <span className="block">{item.zoneName}</span>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
          </div>

          {/* 固定底部按钮 */}
          <div className="flex-shrink-0 border-gray-100 border-t p-4">
            <div className="flex gap-3">
              <button
                className="flex-1 rounded-full border border-gray-300 px-4 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                onClick={handleReset}
                type="button"
              >
                清空
              </button>
              <button
                className="flex-1 rounded-full bg-indigo-500 px-4 py-3 font-medium text-white transition-colors hover:bg-blue-600"
                onClick={handleConfirm}
                type="button"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}

// 导出类型，方便外部使用
export type {
  RegionCategory,
  RegionSelectorProps,
} from './types';
