'use client';

import {
  Ava<PERSON>,
  Button,
  Card,
  Dialog,
  DotLoading,
  Error<PERSON>lock,
  FloatingBubble,
  InfiniteScroll,
  PullToRefresh,
  SwipeAction,
  Toast,
} from 'antd-mobile';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Filter,
  Plus,
  User,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { deleteStudentReport, getStudentReportList } from '@/api/pbl';

import type { FilterValues } from './components/FilterDrawer';
import FilterDrawer from './components/FilterDrawer';

// color换成具体颜色值
const statusMap = {
  0: { text: '待生成', color: '#faad14', icon: <Clock size={14} /> },
  1: { text: '生成中', color: '#1677ff', icon: <Clock size={14} /> },
  2: { text: '已生成', color: '#52c41a', icon: <CheckCircle size={14} /> },
  3: { text: '生成失败', color: '#ff4d4f', icon: <AlertCircle size={14} /> },
};

export interface reportResponse {
  list: List[];
  total: number;
}

export interface List {
  /**
   * 亮点
   */
  advantages?: null;
  /**
   * 完整报告内容
   */
  content?: null;
  createTime?: string;
  /**
   * 创建老师ID
   */
  createUserId?: string;
  /**
   * 删除标识
   */
  delFlag?: number;
  dept?: Dept;
  /**
   * 生成报告时的班级ID
   */
  deptId?: string;
  /**
   * 不足
   */
  disadvantages?: null;
  /**
   * 报告结束日期
   */
  endDate?: string;
  /**
   * 总体评价
   */
  evaluation?: null;
  /**
   * 评估人
   */
  evaluator?: string;
  handleError?: null;
  /**
   * 处理状态 0-待处理 1-处理中 2-处理成功 3-处理失败
   */
  handleStatus: 0 | 1 | 2 | 3;
  handleStatusText?: string;
  handleTime?: null;
  /**
   * 家园共育合作
   */
  homeSchoolCooperation?: null;
  id?: number;
  /**
   * 机构ID
   */
  instId?: string;
  /**
   * 观察记录数
   */
  observationCnt?: number;
  /**
   * 个性化教学策略
   */
  personalizedTeachingStrategy?: null;
  /**
   * 幼儿报告ID
   */
  reportId?: string;
  /**
   * 报告起始日期
   */
  startDate?: string;
  student?: Student;
  /**
   * 学生ID
   */
  studentId?: string;
  /**
   * 学生名称
   */
  studentName?: string;
  /**
   * 老师寄语
   */
  teacherNote?: null;
  /**
   * 教师支持策略
   */
  teacherSupportStrategy?: null;
  /**
   * 报告标题
   */
  title?: string;
  updateTime?: string;
  [property: string]: any;
}

export interface Dept {
  delFlag: number;
  graduate: number;
  id: string;
  name: string;
  nickName: string;
  [property: string]: any;
}

export interface Student {
  avatar: string;
  delFlag: number;
  gender: number;
  id: string;
  isLeave: number;
  name: string;
  [property: string]: any;
}

const StudentReportListPage: React.FC = () => {
  if (typeof window !== 'undefined') {
    document.title = '报告列表';
  }
  const router = useRouter();
  const searchParams = useSearchParams();
  const studentId = searchParams.get('studentId');
  const studentName = searchParams.get('studentName');
  const [data, setData] = useState<List[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const [revealedIds, setRevealedIds] = useState<Set<number | string>>(
    new Set()
  );
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [selectedClassName, setSelectedClassName] = useState<string | null>(
    null
  );
  const [selectedStudent, setSelectedStudent] = useState<any>(
    studentId
      ? [
          {
            studentId,
            studentName,
          },
        ]
      : []
  );
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const [filterVisible, setFilterVisible] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const pageRef = useRef(1);
  const isFirstRender = useRef(true);
  const setPageRef = (val) => {
    pageRef.current = val;
    setPage(val);
  };
  useEffect(() => {
    // if (isFirstRender.current) {
    //   isFirstRender.current = false;
    //   return;
    // }
    setPageRef(1);
    loadData();
  }, [selectedStudent, selectedClass, startDate, endDate]);

  const handleFilterConfirm = (filters: FilterValues) => {
    setSelectedClass(filters.classId);
    setSelectedClassName(filters.className);
    setSelectedStudent(filters.student);
    setStartDate(filters.startDate);
    setEndDate(filters.endDate);
    setFilterVisible(false);
  };

  const handleFilterReset = () => {
    // 重置父组件中的筛选状态
    setSelectedClass(null);
    setSelectedClassName(null);
    setSelectedStudent([]);
    setStartDate(null);
    setEndDate(null);
  };
  async function loadData() {
    try {
      const result = await getStudentReportList({
        page: pageRef.current,
        pageSize,
        deptId: selectedClass,
        createTime: [startDate, endDate],
        studentId: selectedStudent?.map((item) => item.studentId),
        handleStatus: '',
      });
      if (pageRef.current === 1) {
        setData(result.list);
      } else {
        setData((prevData) => [...prevData, ...result.list]);
      }
      setPageRef(pageRef.current + 1);
      setHasMore(result.list.length >= pageSize);
    } catch (error) {
      Toast.show({ content: '加载数据失败', icon: 'fail' });
    }
  }

  const handleRefresh = async () => {
    setPageRef(1);
    await loadData();
  };

  const renderStatusTag = (status: 0 | 1 | 2 | 3) => {
    const { text, color } = statusMap[status];
    return (
      <div
        style={{
          backgroundColor: color,
          width: '54px',
          height: '20px',
          borderRadius: '25px',
          borderBottomRightRadius: '0px',
          borderTopLeftRadius: '0px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <span className="text-[16px] text-white">{text}</span>
      </div>
    );
  };
  const handleAddReport = () => {
    router.push('/pbl/record/report/generate?type=1');
  };
  const handleDetail = (item: any) => {
    if (item.handleStatus === 0 || item.handleStatus === 1) {
      Toast.show({ content: '生成中,耐心等待几分钟...' });
      return;
    }
    if (item.handleStatus === 3) {
      Toast.show({ content: '该报告生成失败', icon: 'fail' });
      return;
    }
    router.push(`studentDetail?reportId=${item.reportId}`);
  };
  const InfiniteScrollContent = ({
    hasMore,
    empty,
  }: {
    hasMore?: boolean;
    empty?: boolean;
  }) => {
    return (
      <>
        {hasMore ? (
          <>
            <span>Loading</span>
            <DotLoading />
          </>
        ) : empty ? (
          <ErrorBlock description="" status="empty" title="暂无报告内容" />
        ) : (
          <span>～数据已经见底～</span>
        )}
      </>
    );
  };

  const handleDelete = async (reportId: string) => {
    try {
      await deleteStudentReport(reportId);
      Toast.show({ content: '删除成功', icon: 'success' });
      handleRefresh(); // Refresh the list after deletion
    } catch (error) {
      Toast.show({ content: '删除失败', icon: 'fail' });
    }
  };

  const showDeleteConfirm = (reportId: string) => {
    Dialog.confirm({
      content: '确定要删除这份报告吗？',
      onConfirm: () => handleDelete(reportId),
    });
  };
  const handleReveal = (id: number | string) => {
    setRevealedIds((prev) => new Set(prev).add(id));
  };

  const handleClose = (id: number | string) => {
    setRevealedIds((prev) => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });
  };
  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* 筛选区域 */}
      <div
        style={{
          padding: '10px 16px',
          borderBottom: '1px solid #eee',
          background: 'white',
        }}
      >
        <div className="flex flex-row items-center justify-between">
          <div className="text-base">学生报告列表</div>
          <Button onClick={() => setFilterVisible(true)} size="small">
            {/* 点击按钮打开抽屉 */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                color: '#333',
              }}
            >
              <Filter size={14} />
              <span className="text-sm">筛选</span>
            </div>
          </Button>
        </div>

        {/* 显示当前筛选条件 */}
        <div
          style={{
            fontSize: '12px',
            color: '#999',
            marginTop: '5px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}
        >
          {selectedClassName && (
            <span className="mr-2">班级: {selectedClassName} </span>
          )}
          {selectedStudent.length > 0 && (
            <span className="mr-2">
              学生: {selectedStudent.map((item) => item.studentName).join(',')}
            </span>
          )}
          {endDate && startDate && (
            <span className="mr-2">
              时间: {startDate.toLocaleDateString()}至:
              {endDate.toLocaleDateString()}
            </span>
          )}
        </div>
      </div>

      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          background: '#f2f2f2',
          padding: '10px 16px 0',
        }}
      >
        <PullToRefresh onRefresh={handleRefresh}>
          {data.map((item) => (
            <SwipeAction
              closeOnAction={false}
              key={item.id}
              onActionsReveal={() => handleReveal(item.id!)}
              onClose={() => handleClose(item.id!)}
              rightActions={[
                {
                  key: 'delete',
                  text: '删除',
                  color: 'danger',
                  onClick: () => showDeleteConfirm(item.reportId),
                },
              ]}
              style={{
                background: '#f2f2f2',
                borderRadius: '8px',
                overflow: 'hidden',
                marginBottom: '8px',
              }}
            >
              <Card
                onClick={() => handleDetail(item)}
                style={{
                  marginBottom: '0',
                  borderTopLeftRadius: 8,
                  borderBottomLeftRadius: 8,
                  borderTopRightRadius: revealedIds.has(item.id!) ? 0 : 8,
                  borderBottomRightRadius: revealedIds.has(item.id!) ? 0 : 8,
                  transition: 'border-radius 0.2s',
                }}
              >
                <div className="relative">
                  <div
                    className="absolute "
                    style={{
                      right: '-10px',
                      top: '-10px',
                    }}
                  >
                    {renderStatusTag(item.handleStatus)}
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '12px 0',
                    }}
                  >
                    <Avatar
                      fallback={<User size={24} />}
                      src={item.student?.avatar}
                      style={{ '--size': '48px', '--border-radius': '50%' }}
                    />
                    <div
                      style={{
                        flex: 1,
                        marginLeft: '12px',
                      }}
                    >
                      <div
                        style={{
                          fontSize: '15px',
                          color: '#333',
                          marginBottom: '15px',
                        }}
                      >
                        <span className="mr-2">{item.student?.name}</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          观察记录数: {item.observationCnt}
                        </span>
                      </div>
                      <div
                        style={{
                          fontSize: '12px',
                          color: '#666',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          display: 'flex',
                        }}
                      >
                        <span>{item.dept?.name}</span>
                        <span>
                          {item.startDate}至{item.endDate}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </SwipeAction>
          ))}
          <InfiniteScroll hasMore={hasMore} loadMore={() => loadData()}>
            <InfiniteScrollContent
              empty={data.length === 0}
              hasMore={hasMore}
            />
          </InfiniteScroll>
        </PullToRefresh>
      </div>
      <FloatingBubble
        onClick={handleAddReport}
        style={{
          '--initial-position-bottom': '80px', // 调整底部距离
          '--initial-position-right': '24px', // 调整右侧距离
          '--size': '48px', // 调整按钮大小
        }} // 设置点击事件
      >
        <Plus size={24} />
      </FloatingBubble>
      <FilterDrawer
        initialFilters={{
          // 传递当前的筛选状态作为初始值
          classId: selectedClass,
          className: selectedClassName,
          student: selectedStudent,
          startDate,
          endDate,
        }}
        onClose={() => setFilterVisible(false)}
        onConfirm={handleFilterConfirm} // 传递确认回调
        onReset={handleFilterReset} // 传递重置回调
        type={1} // 1:学生,2:班级
        visible={filterVisible}
      />
    </div>
  );
};

export default StudentReportListPage;
