import type { CascadePickerOption } from 'antd-mobile';
import {
  But<PERSON>,
  CascadePicker,
  DatePicker,
  List,
  Popup,
  Toast,
} from 'antd-mobile';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';
import { getMyClassList } from '@/api/invite';
import StudentPicker from '@/components/StudentPicker'; // 确保路径正确
// 定义筛选条件的数据结构
export interface FilterValues {
  classId: string | null;
  className: string | null;
  student: any | null; // 可以定义更具体的学生类型
  startDate: Date | null;
  endDate: Date | null;
}

// 定义组件 Props
interface FilterDrawerProps {
  visible: boolean;
  type: number;
  onClose: () => void;
  onConfirm: (filters: FilterValues) => void;
  onReset: () => void;
  initialFilters: FilterValues;
  // classListData: CascadePickerOption[];
}
function convertToCascadePickerOptions(
  treeArray: any[]
): CascadePickerOption[] {
  if (!Array.isArray(treeArray)) {
    console.warn(
      'convertToCascadePickerOptions 接收到的输入不是数组:',
      treeArray
    );
    return [];
  }
  return treeArray.map((item) => {
    const option: CascadePickerOption = {
      label: item.name,
      value: item.id.toString(),
    };
    if (item.children && item.children.length > 0) {
      option.children = convertToCascadePickerOptions(item.children);
    }
    return option;
  });
}
const FilterDrawer: React.FC<FilterDrawerProps> = ({
  visible,
  type,
  onClose,
  onConfirm,
  onReset,
  initialFilters,
  // classListData,
}) => {
  // 内部状态管理临时的筛选条件
  const [tempSelectedClass, setTempSelectedClass] = useState<string | null>(
    null
  );
  const [tempSelectedClassName, setTempSelectedClassName] = useState<
    string | null
  >(null);
  const [tempSelectedStudent, setTempSelectedStudent] = useState<any>(null);
  const [tempStartDate, setTempStartDate] = useState<Date | null>(null);
  const [tempEndDate, setTempEndDate] = useState<Date | null>(null);
  const [classListData, setClassListData] = useState<CascadePickerOption[]>([]);
  const [loadingClasses, setLoadingClasses] = useState(false); // 可选：添加加载状态
  // 控制内部 Picker 的可见性
  const [classPickerVisible, setClassPickerVisible] = useState(false);
  const [studentPickerVisible, setStudentPickerVisible] = useState(false);
  const [startDateVisible, setStartDateVisible] = useState(false);
  const [endDateVisible, setEndDateVisible] = useState(false);

  // 当抽屉打开或初始筛选条件变化时，同步内部临时状态
  useEffect(() => {
    if (visible) {
      setTempSelectedClass(initialFilters.classId);
      setTempSelectedClassName(initialFilters.className);
      setTempSelectedStudent(initialFilters.student);
      setTempStartDate(initialFilters.startDate);
      setTempEndDate(initialFilters.endDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, initialFilters]); // 依赖项包含 visible 和 initialFilters

  // 处理班级选择确认
  const handleClassConfirm = (
    val: string[],
    extend: { items: CascadePickerOption[] }
  ) => {
    if (extend.items.length > 0) {
      const selected = extend.items[extend.items.length - 1];
      setTempSelectedClass(selected.value as string);
      setTempSelectedClassName(selected.label);
      setClassPickerVisible(false);
    } else {
      Toast.show('请选择一个具体的班级');
    }
  };

  // 处理学生选择确认
  const handleStudentConfirm = (student: any) => {
    console.log('student: ', student);
    setTempSelectedStudent(student);
    setStudentPickerVisible(false);
  };

  // 处理重置按钮点击
  const handleInternalReset = () => {
    setTempSelectedClass(null);
    setTempSelectedClassName(null);
    setTempSelectedStudent(null);
    setTempStartDate(null);
    setTempEndDate(null);
    // 调用外部传入的 onReset 回调
    onReset();
    // 可以选择是否在重置后关闭抽屉
    // onClose();
  };

  // 处理确定按钮点击
  const handleInternalConfirm = () => {
    // 调用外部传入的 onConfirm 回调，传递当前的临时筛选值
    onConfirm({
      classId: tempSelectedClass,
      className: tempSelectedClassName,
      student: tempSelectedStudent,
      startDate: tempStartDate,
      endDate: tempEndDate,
    });
    onClose(); // 关闭抽屉
  };
  useEffect(() => {
    if (visible) {
      setTempSelectedClass(initialFilters.classId);
      setTempSelectedClassName(initialFilters.className);
      setTempSelectedStudent(initialFilters.student);
      setTempStartDate(initialFilters.startDate);
      setTempEndDate(initialFilters.endDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, initialFilters]);
  useEffect(() => {
    // 仅在首次加载或需要时获取数据
    // 如果希望每次抽屉打开都刷新，可以把依赖项改为 [visible] 并添加 if(visible) 条件
    if (visible) {
      // 避免重复请求
      setLoadingClasses(true);
      getMyClassList()
        .then((res: any) => {
          if (res && res.children && Array.isArray(res.children)) {
            const options = convertToCascadePickerOptions(res.children);
            setClassListData(options);
          } else if (res && res.name && res.id) {
            const options = [
              { label: res.name, value: res.id.toString(), children: [] },
            ];
            setClassListData(options);
          } else {
            console.warn(
              'FilterDrawer: getMyClassList 返回的数据格式不符合预期:',
              res
            );
            setClassListData([]);
          }
        })
        .catch((error) => {
          console.error('FilterDrawer: 获取班级列表失败:', error);
          setClassListData([]);
          Toast.show({ content: '获取班级列表失败', icon: 'fail' });
        })
        .finally(() => {
          setLoadingClasses(false);
        });
    }
  }, [visible]);
  return (
    <>
      <Popup
        bodyStyle={{ width: '80vw' }}
        onMaskClick={onClose}
        position="right"
        visible={visible}
      >
        <div className="flex h-full flex-col">
          <div className="border-gray-200 border-b p-4 font-bold">筛选条件</div>
          <div className="flex-1 overflow-y-auto p-0">
            <List mode="card">
              <List.Item>
                <div
                  className="flex h-11 w-full items-center rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-left text-gray-700 text-sm transition "
                  onClick={() => setClassPickerVisible(true)}
                >
                  {tempSelectedClassName || '请选择班级'}
                </div>
              </List.Item>
              {type === 1 && (
                <List.Item>
                  <StudentPicker
                    multiple={false}
                    onMultiSelect={handleStudentConfirm}
                    placeholder={tempSelectedStudent?.name || '请选择学生'}
                    title="请选择学生"
                  />
                </List.Item>
              )}
              <List.Item>
                <div className="flex items-center space-x-3">
                  <div
                    className="flex h-11 flex-1 cursor-pointer items-center rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-gray-700 text-sm transition hover:border-blue-500"
                    onClick={() => setStartDateVisible(true)}
                  >
                    {}
                    {tempStartDate
                      ? format(tempStartDate, 'yyyy/MM/dd')
                      : '开始日期'}
                  </div>
                  <span className="text-gray-500 text-sm">至</span>
                  <div
                    className="flex h-11 flex-1 cursor-pointer items-center rounded-lg border border-gray-200 bg-white px-4 py-2.5 text-gray-700 text-sm transition hover:border-blue-500" // 确保高度一致
                    onClick={() => setEndDateVisible(true)}
                  >
                    {tempEndDate
                      ? format(tempEndDate, 'yyyy/MM/dd')
                      : '结束日期'}
                  </div>
                </div>
              </List.Item>
            </List>
          </div>
          {/* 底部按钮区域 */}
          <div className="flex gap-2.5 border-gray-200 border-t px-4 py-2.5">
            <Button block onClick={handleInternalReset}>
              重置
            </Button>
            <Button block color="primary" onClick={handleInternalConfirm}>
              确定
            </Button>
          </div>
        </div>
      </Popup>

      {/* Pickers */}
      <DatePicker
        max={tempEndDate || new Date()}
        onClose={() => setStartDateVisible(false)}
        onConfirm={(val) => {
          setTempStartDate(val);
          setStartDateVisible(false);
        }}
        title="选择开始日期"
        value={tempStartDate || new Date()}
        visible={startDateVisible}
      />
      <DatePicker
        max={new Date()}
        min={tempStartDate || undefined}
        onClose={() => setEndDateVisible(false)}
        onConfirm={(val) => {
          setTempEndDate(val);
          setEndDateVisible(false);
        }} // min 可以是 undefined
        title="选择结束日期"
        value={tempEndDate || new Date()}
        visible={endDateVisible}
      />
      <CascadePicker
        onClose={() => setClassPickerVisible(false)}
        onConfirm={handleClassConfirm}
        options={classListData}
        title="选择班级"
        visible={classPickerVisible}
        // 可以根据需要设置 value={tempSelectedClass ? [tempSelectedClass] : []}
      />
      {/* 学生选择器已在 List.Item 中渲染 */}
    </>
  );
};

export default FilterDrawer;
