export interface RecordData {
  id: string;
  observationId?: string;
  title: string;
  content: string;
  image?: string;
  type: 'image' | 'video' | 'audio' | 'text';
  zone?: {
    zoneId: string;
    zoneName: string;
  };
  tags: {
    tagId: string;
    tagName: string;
    color: string;
  }[];
  students: {
    id: string;
    name: string;
    avatar: string;
  }[];
  createUser: {
    name: string;
    avatar?: string;
  };
  createTime: string;
  audio?: {
    duration: string;
    progress: number;
  };
  medias?: {
    mediaId?: number;
    type: number; // 1=图片，2=视频，3=音频
    url: string;
    thumbnail?: string;
    caption: string;
    duration?: string;
    cover?: string;
    id: string;
  }[];
  isGenerating?: number;
}
