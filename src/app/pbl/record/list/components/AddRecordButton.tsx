'use client';

import { ActionSheet } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { compare } from 'compare-versions';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { useCommonStore } from '@/store/useCommonStore';
import { navigationToNativePage } from '@/utils';

interface AddRecordButtonProps {
  extra?: string;
  cancelText?: string;
  buttonText?: string;
  icon?: React.ReactNode;
  className?: string;
  classId?: string;
}

export default function AddRecordButton({
  extra = '请选择记录模式',
  cancelText = '取消',
  buttonText = '新增观察记录',
  icon = <FaPlus />,
  className = '',
  classId = '',
}: AddRecordButtonProps) {
  const version = useCommonStore((state) => state.version);
  const brand = useCommonStore((state) => state.brand);
  const router = useRouter();
  const [visible, setVisible] = useState(false);

  // 在组件内部定义固定的 actions
  const actions: Action[] = [
    {
      text: 'AI 自动生成观察记录',
      description: (
        <div className="text-gray-500 text-xs">
          上传素材，AI 会自动生成观察记录
        </div>
      ),
      key: 'copy',
      onClick: () => {
        if (
          (brand === '1' &&
            compare(version, '1.33.0', '>') &&
            compare(version, '1.34.0', '<')) ||
          (brand === '2' &&
            compare(version, '6.21.2', '>=') &&
            compare(version, '6.22.0', '<'))
        ) {
          navigationToNativePage(
            `app://app/pbl/addMaterials?deptId=${classId}&videoCompressBitrate=10000000`
          );
          setVisible(false);
        } else {
          router.push(`/pbl/material/create?classId=${classId}`);
        }
      },
    },
    {
      text: '手动创建观察记录',
      key: 'edit',
      onClick: () => {
        router.push(`/pbl/record/create?classId=${classId}`);
      },
    },
  ];

  return (
    <>
      <div className="fixed right-0 bottom-0 left-0 px-6 pb-2">
        <button
          className={`flex w-full items-center justify-center space-x-2 rounded-full bg-gradient-to-r from-violet-400 to-violet-500 px-6 py-3 text-white shadow-md transition-all hover:shadow-lg ${className}`}
          onClick={() => setVisible(true)}
          type="button"
        >
          {icon}
          <span>{buttonText}</span>
        </button>
      </div>
      <ActionSheet
        actions={actions}
        cancelText={cancelText}
        extra={extra}
        onClose={() => setVisible(false)}
        visible={visible}
      />
    </>
  );
}
