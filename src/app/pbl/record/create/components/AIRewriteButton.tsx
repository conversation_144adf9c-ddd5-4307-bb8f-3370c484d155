'use client';

import { Popup, Stepper, Toast } from 'antd-mobile';
import clsx from 'clsx';
import { Sparkles } from 'lucide-react';
import { useState } from 'react';
import { aiGenerateContent } from '@/api/pbl';
import VoiceInputButton from './VoiceInputButton';

interface AIRewriteButtonProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

export default function AIRewriteButton({
  value,
  onChange,
  disabled = false,
  className = '',
}: AIRewriteButtonProps) {
  const [isAiRewriting, setIsAiRewriting] = useState(false);
  const [aiRewritePopupVisible, setAiRewritePopupVisible] = useState(false);
  const [aiRequirement, setAiRequirement] = useState('');
  const [wordLimit, setWordLimit] = useState(300);

  const handleAiRewrite = () => {
    if (!value.trim()) {
      Toast.show({
        icon: 'fail',
        content: '请先输入内容',
      });
      return;
    }
    setAiRewritePopupVisible(true);
  };

  const handleAiRewriteSubmit = async () => {
    setAiRewritePopupVisible(false);
    setIsAiRewriting(true);

    try {
      // 构建 AI 生成内容的查询参数
      const promptRequirement = aiRequirement
        ? `用户要求：${aiRequirement}`
        : '请优化为更专业的内容';

      const query = `
      请将以下内容优化为更专业、易懂的内容：

      原始内容：
      ${value}

      优化要求：
      1. ${promptRequirement}
      2. 字数控制在${wordLimit}字以内
      3. 保持客观、具体的描述方式
      4. 语言专业且易懂

      请直接输出优化后的内容，不要追问问题。
      `;

      const response = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query,
        response_mode: 'blocking',
      });

      if (response?.answer) {
        onChange(response.answer);
        Toast.show({
          icon: 'success',
          content: 'AI优化完成',
        });
      } else {
        throw new Error('AI 响应格式错误');
      }
    } catch (error) {
      console.error('AI重写失败：', error);
      let errorMessage = 'AI优化失败，请稍后重试';

      if (error instanceof Error && error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络后重试';
      }

      Toast.show({
        icon: 'fail',
        content: errorMessage,
      });
    } finally {
      setIsAiRewriting(false);
    }
  };

  // 计算按钮样式
  const getButtonClassName = () => {
    if (disabled || isAiRewriting) {
      return 'cursor-not-allowed bg-indigo-100 text-indigo-600';
    }
    if (value.trim()) {
      return 'bg-indigo-50 text-indigo-600 hover:bg-indigo-100 active:scale-95';
    }
    return 'cursor-not-allowed bg-gray-50 text-gray-400';
  };

  return (
    <>
      <button
        className={clsx(
          'flex items-center gap-2 rounded-full px-3 py-2 font-medium text-xs transition-all',
          getButtonClassName(),
          className
        )}
        disabled={disabled || isAiRewriting || !value.trim()}
        onClick={handleAiRewrite}
        type="button"
      >
        <Sparkles
          className={clsx('h-4 w-4', isAiRewriting && 'animate-spin')}
        />
        {isAiRewriting ? 'AI优化中...' : 'AI优化内容'}
      </button>

      {/* AI重写要求弹窗 */}
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          padding: '20px',
        }}
        onMaskClick={() => setAiRewritePopupVisible(false)}
        visible={aiRewritePopupVisible}
      >
        <div className="space-y-4">
          <div className="font-semibold text-gray-900 text-lg">
            请输入你对AI优化的要求:
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <textarea
              className="h-32 w-full resize-none border-none bg-transparent text-gray-700 placeholder-gray-400 outline-none"
              maxLength={100}
              onChange={(e) => setAiRequirement(e.target.value)}
              placeholder="例如：请将我的观察记录优化为更专业的教育建议"
              value={aiRequirement}
            />
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <VoiceInputButton
                  buttonText="语音输入"
                  className="gap-1 px-2 py-1"
                  onTextReceived={(text) =>
                    setAiRequirement((prev) =>
                      prev ? `${prev}\n${text}` : text
                    )
                  }
                  recordingText="录音中..."
                />
                <button
                  className="text-gray-400 text-xs hover:text-gray-600"
                  onClick={() => setAiRequirement('')}
                  type="button"
                >
                  清空
                </button>
              </div>
              <div className="text-gray-400 text-xs">
                {aiRequirement.length}/100
              </div>
            </div>
          </div>

          {/* 字数限制选择 */}
          <div className="flex items-center justify-between">
            <span className="text-gray-600 text-sm">AI生成的内容字数限制:</span>
            <Stepper
              defaultValue={300}
              max={3000}
              min={10}
              onChange={(val) => setWordLimit(val || 300)}
              step={10}
              value={wordLimit}
            />
          </div>

          {/* 提交按钮 */}
          <button
            className="w-full rounded-lg bg-indigo-500 py-3 font-medium text-white transition-all hover:bg-indigo-600 active:scale-95"
            onClick={handleAiRewriteSubmit}
            type="button"
          >
            提交
          </button>
        </div>
      </Popup>
    </>
  );
}
