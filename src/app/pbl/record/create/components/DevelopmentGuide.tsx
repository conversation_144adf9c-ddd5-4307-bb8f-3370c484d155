import clsx from 'clsx';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
// import { evaluationData } from './data';
import DevelopmentGuideExample from './DevelopmentGuideExample';
import Icon, { type IconName } from './Icon';
import { getObservationEvaluation } from '@/api/pbl';
import { useAtom } from 'jotai';
import { evaluationAtom } from '@/store/pbl';

// 定义 Category 的类型
interface CategoryType {
  label: string;
  icon: IconName;
  color: string;
  bgColor: string;
  subDomains: Array<{
    label: string;
    options: Array<{
      id: string;
      ability_name: string;
    }>;
  }>;
}

// --- 横向滚动 Tab 栏 ---
interface CategoryTabsProps {
  categories: CategoryType[];
  activeCategoryId: string;
  onTabSelect: (categoryId: string) => void;
}

function CategoryTabs({
  categories,
  activeCategoryId,
  onTabSelect
}: CategoryTabsProps) {
  console.log('🚀 ~ activeCategoryId:', activeCategoryId);
  return (
    <div className="z-10 bg-white">
      {/* 使 Tab 栏也吸顶 */}
      <div
        className="flex space-x-1 py-2 overflow-x-auto hide-scrollbar"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {categories.map((category) => (
          <button
            type="button"
            key={category.label}
            onClick={() => onTabSelect(category.label)}
            className={clsx(
              'flex-shrink-0 flex items-center gap-1.5 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ease-out whitespace-nowrap',
              activeCategoryId === category.label
                ? `shadow-sm ${category.bgColor}` // 激活状态
                : 'text-medium-text hover:bg-gray-100' // 非激活状态
            )}
          >
            <Icon
              name={category.icon}
              className={clsx(
                'w-4 h-4',
                activeCategoryId === category.label ? category.color : ''
              )}
            />
            {category.label}
          </button>
        ))}
      </div>
    </div>
  );
}

// 定义 Goal 类型
interface Goal {
  id: string;
  ability_name: string;
  levels?: any[]; // 可选的 levels 属性
}

// 单个评价目标
interface EvaluationGoalProps {
  goal: Goal;
  isAchieved: boolean;
  onToggleAchieved: (goalId: string) => void;
}

function EvaluationGoal({
  goal,
  isAchieved,
  onToggleAchieved
}: EvaluationGoalProps) {
  const [showCheckmark, setShowCheckmark] = useState(false);
  const [showExample, setShowExample] = useState(false);
  const [levels, setLevels] = useState<any[]>([]);

  useEffect(() => {
    if (isAchieved) {
      setShowCheckmark(true);
      const timer = setTimeout(() => setShowCheckmark(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isAchieved]);

  const handleOpenPopup = () => {
    setShowExample(true);
    // setLevels(goal.levels);
  };

  return (
    <button
      type="button"
      onClick={() => onToggleAchieved(goal.id)}
      className={clsx(
        'w-full flex items-center justify-between text-left py-3 px-3 my-1 rounded-lg border transition-all duration-200 ease-out fade-in',
        isAchieved
          ? 'bg-achieved-bg achieved-text achieved-border shadow-achieved'
          : 'bg-white text-dark-text border-gray-200 hover:bg-gray-50'
      )}
      style={{ animationDelay: `${Math.random() * 0.2}s` }}
    >
      <span className="text-sm font-serif flex-1 pr-3">
        {goal.ability_name}
        {/* <span
          className="text-sm font-serif flex-1 px-3"
          onClick={() => handleOpenPopup()}
        >
          <Icon name="CircleHelp" className="w-4 h-4 inline-block" />
        </span> */}
      </span>
      <div
        className={clsx(
          'flex-shrink-0 w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200',
          isAchieved
            ? 'bg-indigo-500 border-indigo-500'
            : 'border-gray-300 bg-white'
        )}
      >
        {isAchieved && (
          <Icon
            name="Check"
            className={clsx(
              'w-4 h-4 text-white',
              showCheckmark ? 'checkmark-animation' : ''
            )}
          />
        )}
      </div>
      <DevelopmentGuideExample
        levels={levels}
        visible={showExample}
        onClose={() => setShowExample(false)}
      />
    </button>
  );
}

// 可折叠的子分类
interface CollapsibleSubCategoryProps {
  subCategory: {
    label: string;
    options: {
      id: string;
      ability_name: string;
    }[];
  };
  studentId: string; // 添加 studentId prop
}

function CollapsibleSubCategory({
  subCategory,
  studentId
}: CollapsibleSubCategoryProps) {
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom);
  // 获取当前学生的评估数据，如果不存在则初始化为空对象
  const studentEvaluations = allEvaluations[studentId] || {};
  const [isOpen, setIsOpen] = useState(false);

  const handleToggleAchieved = useCallback(
    (goalId: string) => {
      // 更新 atom 中对应 studentId 的数据
      setAllEvaluations((prevAll) => {
        const currentStudentEvals = prevAll[studentId] || {};
        const newStudentEvals = {
          ...currentStudentEvals,
          [goalId]: !currentStudentEvals[goalId] // 切换布尔值
        };
        return {
          ...prevAll,
          [studentId]: newStudentEvals
        };
      });
    },
    [studentId, setAllEvaluations]
  );

  console.log('🚀 ~ subCategory:', subCategory, studentEvaluations);
  return (
    <div className="border border-gray-200 bg-white rounded-lg shadow-subtle mb-3 overflow-hidden">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full px-4 py-3 bg-gray-50 text-left  transition-colors duration-150"
      >
        <span className="text-base font-medium text-dark-text">
          {subCategory.label}
        </span>
        <Icon
          name="ChevronDown"
          className="w-4 h-4 text-medium-text transition-transform duration-300"
          style={{ transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}
        />
      </button>
      <div
        className={clsx(
          'transition-all duration-500 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px]' : 'max-h-0'
        )}
      >
        <div className="pl-3 pr-3 pb-3 pt-1">
          {subCategory.options.map((goal) => {
            return (
              <EvaluationGoal
                key={goal.id}
                goal={goal}
                // 检查 studentEvaluations 中对应 goal.id 的布尔值
                isAchieved={!!studentEvaluations[goal.id]}
                onToggleAchieved={handleToggleAchieved}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}

// 定义 API 返回的数据类型
interface EvaluationDomainData {
  label: string;
  subDomains: Array<{
    label: string;
    options: Array<{
      id: string;
      ability_name: string;
    }>;
  }>;
  [key: string]: unknown; // 允许其他属性
}

export function formatEvaluationData(
  data: EvaluationDomainData[]
): CategoryType[] {
  const colors: Record<
    string,
    { icon: IconName; color: string; bgColor: string }
  > = {
    健康: {
      icon: 'Heart',
      color: 'text-pink-500',
      bgColor: 'bg-pink-50'
    },
    语言: {
      icon: 'MessageCircle',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50'
    },
    社会: {
      icon: 'Users',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    科学: {
      icon: 'Atom',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    艺术: {
      icon: 'Palette',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    }
  };
  return data.map((item) => {
    const colorInfo = colors[item.label] || {
      icon: 'Heart' as IconName,
      color: 'text-gray-500',
      bgColor: 'bg-gray-50'
    };

    return {
      ...item,
      color: colorInfo.color,
      bgColor: colorInfo.bgColor,
      icon: colorInfo.icon
    };
  });
}

// --- 主组件 ---
interface DevelopmentGuideProps {
  studentId: string; // 添加 studentId prop
}

// 重命名组件为 DevelopmentGuide
function DevelopmentGuide({ studentId }: DevelopmentGuideProps) {
  // 不再需要在这里直接读写 atom，由子组件处理
  // const [evaluations, setEvaluation] = useAtom(evaluationAtom);
  const [allEvaluations] = useAtom(evaluationAtom); // 只读 atom 获取总数

  const [evaluationData, setEvaluationData] = useState<CategoryType[]>([]);
  const [activeCategoryId, setActiveCategoryId] = useState('健康'); // 默认选中第一个
  // 移除 safeEvaluations

  useEffect(() => {
    // 获取评估维度数据
    getObservationEvaluation({
      dimensionType: 2
    }).then((res) => {
      //@ts-ignore
      const data = res?.dimensions?.[0]?.domains || [];
      setEvaluationData(formatEvaluationData(data));
    });
  }, []);

  // 移除 handleGoalToggle，由 CollapsibleSubCategory 处理

  const handleTabSelect = useCallback((categoryId: string) => {
    setActiveCategoryId(categoryId);
  }, []);

  // --- 获取当前激活分类的数据 ---
  const activeCategory = useMemo(() => {
    return evaluationData.find((cat) => cat.label === activeCategoryId);
  }, [activeCategoryId, evaluationData]);

  // 计算总达成数量
  const { totalAchievedCount, totalGoalCount } = useMemo(() => {
    const studentEvaluations = allEvaluations[studentId] || {};
    const allOptions = evaluationData.flatMap(
      (cat) => cat.subDomains?.flatMap((sc) => sc.options) || []
    );
    // 计算 studentEvaluations 中值为 true 的数量
    const achievedCount =
      Object.values(studentEvaluations).filter(Boolean).length;

    return {
      totalAchievedCount: achievedCount,
      totalGoalCount: allOptions.length
    };
  }, [evaluationData, allEvaluations, studentId]); // 添加依赖

  return (
    <div className=" mx-auto flex flex-col overflow-hidden">
      <CategoryTabs
        categories={evaluationData}
        activeCategoryId={activeCategoryId}
        onTabSelect={handleTabSelect}
      />

      <div className="flex-grow overflow-y-auto mt-2">
        {activeCategory ? (
          // 渲染当前激活分类的子分类列表
          activeCategory.subDomains.map((subCategory) => (
            <CollapsibleSubCategory
              key={subCategory.label}
              subCategory={subCategory}
              studentId={studentId} // 传递 studentId
            />
          ))
        ) : (
          <p className="text-center text-medium-text mt-10">
            请选择一个评价领域
          </p> // 处理未选中或数据为空的情况
        )}
      </div>

      {/* 底部操作栏 (吸底) */}
      <div className="p-2 bg-white  flex items-center justify-between mt-auto">
        {' '}
        <div className="text-sm text-medium-text">
          总计:{' '}
          <span className="font-semibold text-brand-primary">
            {totalAchievedCount} / {totalGoalCount}
          </span>{' '}
          项已达成
        </div>
      </div>
    </div>
  );
}

export default DevelopmentGuide; // 导出正确的组件名
