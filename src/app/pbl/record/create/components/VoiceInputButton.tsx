'use client';

import { Toast } from 'antd-mobile';
import clsx from 'clsx';
import { Mic, Square } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { transcribeAudio } from '@/api/pbl';

interface VoiceInputButtonProps {
  onTextReceived: (text: string) => void;
  onUploadComplete?: (url: string) => void;
  onError?: (error: string) => void;
  buttonText?: string;
  recordingText?: string;
  uploadingText?: string;
  processingText?: string;
  className?: string;
}

export default function VoiceInputButton({
  onTextReceived,
  onUploadComplete,
  onError,
  buttonText = '语音转文字',
  recordingText = '录音中...',
  uploadingText = '上传中...',
  processingText = '语音转文字中...',
  className,
}: VoiceInputButtonProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 开始录音
  const startRecording = async () => {
    try {
      // 检查浏览器是否支持 getUserMedia
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error('您的浏览器不支持录音功能，请使用现代浏览器');
      }

      // 直接请求麦克风权限并开始录音
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const preferType = 'audio/wav';
      const fallbackType = 'audio/webm;codecs=opus';

      const mimeType = MediaRecorder.isTypeSupported(preferType)
        ? preferType
        : fallbackType;

      const mediaRecorder = new MediaRecorder(stream, { mimeType });
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: mimeType,
        });
        uploadAudio(audioBlob);
      };

      // 每秒收集数据
      mediaRecorder.start(1000);

      setIsRecording(true);
      setRecordingTime(0);

      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (recordingError: unknown) {
      console.error('录音失败：', recordingError);

      let errorMessage = '录音失败，请重试';
      if (recordingError instanceof Error) {
        if (recordingError.name === 'NotAllowedError') {
          errorMessage = '麦克风权限被拒绝，请允许访问麦克风后重试';
        } else if (recordingError.name === 'NotFoundError') {
          errorMessage = '未找到麦克风设备，请检查设备连接';
        } else if (recordingError.name === 'NotSupportedError') {
          errorMessage = '您的浏览器不支持录音功能';
        } else {
          errorMessage = recordingError.message;
        }
      }

      onError?.(errorMessage);
      Toast.show({
        icon: 'fail',
        content: errorMessage,
      });
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // 停止所有音轨
      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }
    }
  };

  // 上传音频
  const uploadAudio = async (audioBlob: Blob) => {
    setIsUploading(true);
    setUploadProgress(0);
    try {
      // 导入上传工具函数
      const { uploadMedia } = await import(
        '@/components/UploadFile/utils/upload'
      );

      // 上传文件并获取进度
      const url = await uploadMedia(audioBlob, 'audio', (progress) => {
        // 更新上传进度
        setUploadProgress(progress);
      });

      // 上传成功后调用语音转文字接口
      setIsUploading(false);
      setIsTranscribing(true);
      onUploadComplete?.(url);

      // 调用语音转文字接口
      const response = await transcribeAudio(url);
      const text = response?.text || '';

      // 将转录的文本传递给回调函数
      onTextReceived(text);

      setIsTranscribing(false);
      Toast.show({
        icon: 'success',
        content: '语音转文字成功',
      });
    } catch (error: unknown) {
      console.error('语音处理失败：', error);
      setIsUploading(false);
      setIsTranscribing(false);
      const errorMessage =
        error instanceof Error ? error.message : '语音处理失败';
      onError?.(errorMessage);
      Toast.show({
        icon: 'fail',
        content: errorMessage,
      });
    }
  };

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }
    };
  }, []);

  // 引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const handleVoiceInput = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };

  return (
    <div>
      <button
        className={clsx(
          'flex items-center justify-center gap-2 rounded-full px-3 py-2 font-medium text-xs transition-all',
          isRecording
            ? 'bg-indigo-100 text-indigo-600'
            : 'bg-indigo-50 text-indigo-600 hover:bg-indigo-100 ',
          className
        )}
        disabled={isUploading || isTranscribing}
        onClick={handleVoiceInput}
        type="button"
      >
        {(() => {
          if (isTranscribing) {
            return (
              <div className="flex items-center">
                <div className="mr-1 h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent border-solid" />
                <span>{processingText}</span>
              </div>
            );
          }
          if (isUploading) {
            return (
              <div className="flex items-center">
                <div className="mr-1 h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent border-solid" />
                <span>
                  {uploadingText} {uploadProgress}%
                </span>
              </div>
            );
          }
          if (isRecording) {
            return (
              <>
                <Square className="mx-auto h-4 w-4" />
                <span>
                  {recordingText} ({formatTime(recordingTime)})
                </span>
              </>
            );
          }
          return (
            <>
              <Mic className="mx-auto h-4 w-4" />
              <span>{buttonText}</span>
            </>
          );
        })()}
      </button>
    </div>
  );
}
