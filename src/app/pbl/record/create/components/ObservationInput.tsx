'use client';

import AIRewriteButton from './AIRewriteButton';
import VoiceInputButton from './VoiceInputButton';

interface ObservationInputProps {
  value: string;
  onChange: (value: string) => void;
}

export default function ObservationInput({
  value,
  onChange,
}: ObservationInputProps) {
  return (
    <div className="relative">
      <textarea
        className="w-full resize-none rounded-xl border border-gray-200 p-4 pb-16 text-gray-700 leading-relaxed focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500"
        id="observation"
        maxLength={3000}
        name="observation"
        onChange={(e) => onChange(e.target.value)}
        placeholder="以客观、具体描述的方式记录观察过程"
        rows={8}
        style={{ minHeight: '200px' }}
        value={value}
      />

      {/* 底部工具栏 */}
      <div className="absolute right-3 bottom-3 left-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <VoiceInputButton
            onTextReceived={(text) =>
              onChange(value ? `${value}\n${text}` : text)
            }
          />
          {/* AI重写按钮 */}
          <AIRewriteButton onChange={onChange} value={value} />
        </div>

        {/* 字数统计 */}
        <div className="text-gray-400 text-xs">{value.length}/3000</div>
      </div>
    </div>
  );
}
