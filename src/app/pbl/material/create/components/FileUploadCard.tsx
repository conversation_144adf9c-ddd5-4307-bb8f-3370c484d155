'use client';

import { CloudUpload, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadCardProps {
  isUploading: boolean;
  onFileSelect: () => void;
  className?: string;
  multiple?: boolean;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({
  isUploading,
  onFileSelect,
  className,
  multiple = false,
}) => {
  return (
    <div
      className={cn(
        'mx-auto flex w-full max-w-md flex-col items-center',
        className
      )}
    >
      <button
        className="group relative flex aspect-square w-56 flex-col items-center justify-center overflow-hidden rounded-2xl border-2 border-blue-300 border-dashed bg-white transition-all duration-300 hover:border-blue-500 disabled:cursor-not-allowed disabled:opacity-70"
        disabled={isUploading}
        onClick={onFileSelect}
        type="button"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

        <div className="relative z-10 flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-50 transition-transform duration-300 group-hover:scale-110">
            <CloudUpload
              className="h-8 w-8 text-blue-500 transition-colors group-hover:text-blue-600"
              strokeWidth={1.5}
            />
          </div>

          <h3 className="mb-2 font-medium text-gray-800 text-xl transition-colors group-hover:text-blue-700">
            添加素材文件
          </h3>

          <p className="text-gray-500 text-sm transition-colors group-hover:text-gray-700">
            点击选择{multiple ? '一个或多个' : ''}视频或录音文件
          </p>
        </div>
      </button>

      <div className="mt-4 flex items-center justify-center text-gray-500 text-sm">
        <Info className="mr-2 h-4 w-4" />
        <span className="text-xs">
          支持 MP4, MOV, M4A 格式，最大 1000MB{multiple ? '，可多选' : ''}
        </span>
      </div>
    </div>
  );
};

export default FileUploadCard;
