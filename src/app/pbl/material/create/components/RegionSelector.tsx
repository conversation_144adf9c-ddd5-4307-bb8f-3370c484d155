/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { Popup } from 'antd-mobile';
import { clsx } from 'clsx';
import { X } from 'lucide-react';

// 区域数据类型定义
interface RegionData {
  zoneId: string;
  zoneName: string;
  selected: boolean;
  parentId: string;
  children: RegionData[];
}

// 组件属性类型
interface RegionSelectorProps {
  visible: boolean;
  onClose: () => void;
  selectedRegionId?: string;
  onRegionSelect: (regionId: string, regionName: string) => void;
  regionData?: RegionData[];
}

// 默认区域数据

const RegionSelector: React.FC<RegionSelectorProps> = ({
  visible,
  onClose,
  selectedRegionId,
  onRegionSelect,
  regionData = [],
}) => {
  return (
    <Popup
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        height: '60vh',
        maxHeight: '500px',
      }}
      onMaskClick={onClose}
      position="bottom"
      visible={visible}
    >
      <div className="flex h-full flex-col">
        {/* 固定标题栏 */}
        <div className="flex-shrink-0 border-gray-100 border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-800 text-lg">选择观察地点</h3>
            <button
              className="rounded-full p-1 hover:bg-gray-100"
              onClick={onClose}
              type="button"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* 可滚动的区域分类 */}
        <div className="flex-1 overflow-y-auto p-4">
          {regionData
            .filter((item) => item.children.length > 0)
            .map((item) => (
              <div className="mb-6" key={item.zoneId}>
                <div className="mb-3 flex items-center">
                  <div className="mr-2 h-4 w-1 rounded bg-green-500" />
                  <h4 className="font-medium text-base text-gray-700">
                    {item.zoneName}
                  </h4>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  {item.children.map((c) => (
                    <button
                      className={clsx(
                        'rounded-lg px-4 py-3 font-medium text-sm transition-colors',
                        'border border-gray-200 hover:border-gray-300',
                        'min-h-[0px] break-words text-center leading-tight',
                        selectedRegionId === c.zoneId
                          ? 'border-green-300 bg-green-50 text-green-700'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      )}
                      key={c.zoneId}
                      onClick={() => {
                        onRegionSelect(c.zoneId, c.zoneName);
                        onClose();
                      }}
                      type="button"
                    >
                      {c.zoneName}
                    </button>
                  ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </Popup>
  );
};

export default RegionSelector;
export type { RegionData, RegionSelectorProps };
