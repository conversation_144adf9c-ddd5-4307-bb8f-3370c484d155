'use client';

import {
  Button,
  Checkbox,
  Image,
  Input,
  Radio,
  TextArea,
  Toast,
} from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { submitSurveyProject } from '@/api/surveyProject';
import PageMetadata from '@/components/PageMeta';
import { hinaRegisterCommonProperties, hinaTrack } from '@/utils';
import { openTypeFilter } from '@/utils/filters';

import styles from './index.module.css';

interface IKetType {
  [key: string]: string;
}
interface IFormData extends IKetType {
  schoolName: string;
  schoolAddress: string;
  schoolScale: string;
  impression: string;
  hasOther: string;
  hasOtherSuperiority: string;
  difficulty: string; // 招生中困扰点
  monitorVideo: string; // 是否开放监控视频给家长
  diligentEquipment: string; // 是否使用了小朋友出入园考勤设备
  mobile: string; // 联系电话
}
export default function SurveyProject() {
  const decryptParams = (param: string) => {
    let str = param.replace(/-/g, '+').replace(/_/g, '/');
    while (str.length % 4) str += '=';
    const [d, p] = decodeURIComponent(atob(str)).split(',');
    return {
      saleName: d,
      openType: p,
    };
  };
  const searchParams = useSearchParams();
  const encryptParams = searchParams.get('p') || null;
  const saleId = searchParams?.get('saleId') || null;
  let saleName = searchParams?.get('saleName') || null;
  let openType = searchParams?.get('openType') || null; // 打开该链接的类型，1-抖音;2-微信
  let openTypeName = searchParams?.get('openTypeName') || ''; // 打开该链接的类型，抖音,微信
  if (encryptParams) {
    const deParams = decryptParams(encryptParams);
    saleName = deParams.saleName || null;
    openType = deParams.openType || null;
    openTypeName = openTypeFilter(deParams.openType);
  }
  const [formFields] = React.useState([
    {
      name: '幼儿园名称',
      type: 'input',
      field: 'schoolName',
      placeholder: '请输入幼儿园名称',
      required: true,
    },
    {
      name: '幼儿园所在区域',
      type: 'radio',
      field: 'schoolAddress',
      required: true,

      options: [
        { label: '老城区', value: '老城区' },
        { label: '新城区', value: '新城区' },
        { label: '城乡结合部', value: '城乡结合部' },
      ],
    },
    {
      name: '幼儿园规模',
      type: 'radio',
      field: 'schoolScale',
      required: true,

      options: [
        { label: '小型（100人以下', value: '小型（100人以下' },
        { label: '中型（100-300人）', value: '中型（100-300人）' },
        { label: '大型（300人以上）', value: '大型（300人以上）' },
      ],
    },
    {
      name: '家长普遍对幼儿园有哪些方面最满意？（可多选)',
      type: 'checkbox',
      field: 'impression',
      required: true,

      otherField: 'impressionOther',
      options: [
        { label: '教师的亲和力和专业度', value: '教师的亲和力和专业度' },
        { label: '课程设置和教学质量', value: '课程设置和教学质量' },
        { label: '园区环境与卫生', value: '园区环境与卫生' },
        { label: '安全管理', value: '安全管理' },
        { label: '家校沟通渠道', value: '家校沟通渠道' },
        { label: '其他（请注明)', value: '其他（请注明)' },
      ],
    },
    {
      name: '您周边是否有其他私立幼儿园？',
      type: 'radio',
      field: 'hasOther',
      required: true,

      options: [
        { label: '有，较多', value: '有，较多' },
        { label: '有，较少', value: '有，较少' },
        { label: '无竞争', value: '无竞争' },
      ],
    },
    {
      name: '您认为周边竞争幼儿园的优势在哪些方面？（可多选）',
      type: 'checkbox',
      field: 'hasOtherSuperiority',
      required: true,
      otherField: 'hasOtherSuperiorityOther',
      options: [
        { label: '收费较低', value: '收费较低' },
        { label: '环境和设施更好', value: '环境和设施更好' },
        { label: '师资更强', value: '师资更强' },
        { label: '教育理念更吸引人', value: '教育理念更吸引人' },
        { label: '宣传力度更大', value: '宣传力度更大' },
        { label: '其他（请注明)', value: '其他（请注明)' },
      ],
    },
    {
      name: '招生中遇到的主要困扰有哪些？（可多选）',
      type: 'checkbox',
      field: 'difficulty',
      otherField: 'difficultyOther',
      required: true,
      options: [
        { label: '生源不足', value: '生源不足' },
        { label: '费用问题', value: '费用问题' },
        { label: '师资短缺', value: '师资短缺' },
        { label: '宣传推广', value: '宣传推广' },
        { label: '家长沟通', value: '家长沟通' },
        { label: '其他', value: '其他' },
      ],
    },
    {
      name: '是否开放监控视频给家长',
      type: 'radio',
      field: 'monitorVideo',
      required: true,

      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    {
      name: '是否使用了小朋友出入园考勤设备',
      type: 'radio',
      field: 'diligentEquipment',
      required: true,

      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    {
      name: '其他您想补充的招生需求或问题',
      type: 'textArea',
      field: 'remark',
      placeholder: '其他请补充',
    },
    {
      required: true,
      name: '联系电话',
      type: 'input',
      field: 'mobile',
      placeholder: '请输入联系电话',
    },
  ]);
  const [isSubmit, setIsSubmit] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [tipText] = React.useState<IFormData>({
    schoolName: '幼儿园名称',
    schoolAddress: '幼儿园所在区域',
    schoolScale: '幼儿园规模',
    impression: '家长普遍对幼儿园有哪些方面最满意',
    impressionOther: '家长普遍对幼儿园有其他哪些方面最满意请您注明',
    hasOther: '您周边是否有其他私立幼儿园',
    hasOtherSuperiority: '您认为周边竞争幼儿园的优势在哪些方面',
    hasOtherSuperiorityOther: '您认为周边竞争幼儿园的其他优势请注明',
    difficulty: '招生中困扰点',
    difficultyOther: '招生中困扰点其他',
    address: '幼儿园地址',
    monitorVideo: '是否开放监控视频给家长',
    diligentEquipment: '是否使用了小朋友出入园考勤设备',
    mobile: '联系电话',
    remark: '其他您想补充的招生需求或问题',
  });
  const [formData, setFormData] = React.useState<IFormData>({
    schoolName: '',
    schoolAddress: '', // 幼儿园所在区域
    schoolScale: '', // 幼儿园规模
    impression: '', // 家长普遍对幼儿园有哪些方面最满意？（可多选)
    impressionOther: '', // 家长普遍对幼儿园有其他哪些方面最满意请您注明
    hasOther: '', // 您周边是否有其他私立幼儿园？
    hasOtherSuperiority: '', // 您认为周边竞争幼儿园的优势在哪些方面（可多选）
    hasOtherSuperiorityOther: '', // 您认为周边竞争幼儿园的其他优势请注明
    difficulty: '', // 招生中遇到的主要困扰有哪些？（可多选）
    difficultyOther: '', // 招生中遇到的其他困扰
    monitorVideo: '', // 是否开放监控视频给家长
    diligentEquipment: '', // 是否使用了小朋友出入园考勤设备
    remark: '',
    mobile: '', // 联系电话
  });
  useEffect(() => {
    if (saleName) {
      hinaRegisterCommonProperties({
        marketer_name: () => saleName,
        channel: () => openTypeName,
      });
      hinaTrack('forms_page_show', {
        marketer_name: saleName,
        channel: openTypeName,
      });
    }
  }, [openTypeName, saleName]);
  const onChange = (field: string, val: string) => {
    const newFormData = { ...formData, [field]: val };
    setFormData(newFormData);
  };
  const transformObject = (obj1: IFormData, obj2: IFormData) => {
    const result: { [key: string]: IFormData[keyof IFormData] } = {};
    for (const [key, value] of Object.entries(obj1)) {
      if (Object.hasOwn(obj2, key)) {
        result[obj2[key] ?? ''] = value;
      }
    }
    return result;
  };
  const handleSubmit = async () => {
    if (isSubmit) {
      Toast.show('您已提交，无需再重复提交');
      return;
    }
    setLoading(true);
    // 遍历formData，值为空就提醒
    for (const [key, value] of Object.entries(formData)) {
      let needTip =
        !value || (key === 'mobile' && !/^1[3-9]\d{9}$/.test(value));

      if (
        !value &&
        ((key === 'impressionOther' &&
          !formData.impression?.includes('其他')) ||
          (key === 'hasOtherSuperiorityOther' &&
            !formData.hasOtherSuperiority.includes('其他')) ||
          (key === 'difficultyOther' && !formData.difficulty.includes('其他')))
      ) {
        needTip = false;
      }
      if (needTip && key !== 'remark') {
        Toast.show(`麻烦您完善“${tipText[key]}”一项!`);
        setLoading(false);
        return;
      }
    }
    setLoading(false);
    const transformedObj = transformObject(formData, tipText);
    submitSurveyProject({
      title: '问卷调查',
      content: JSON.stringify(transformedObj),
      saleId,
      saleName,
      openTypeName,
      openType,
    })
      .then((res) => {
        setIsSubmit(true);
        if (saleName) {
          hinaTrack('forms_page_sumbit', {
            marketer_name: saleName,
            channel: openTypeName,
          });
        }
        Toast.show('提交成功，感谢您的配合');
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <>
      <PageMetadata description="" title="" />
      <div className={`${styles.content} min-h-screen w-full`}>
        <div className={styles.contentHeader}>
          <Image
            alt="问卷调查"
            className="min-h-[200px] w-full"
            src="/images/surveyProject/bg.svg"
          />
          <Image
            alt="问卷调查"
            className={styles.headerIcon}
            src="/images/surveyProject/bookIcon.png"
          />
          <div className={styles.headerTitle}>
            <p className={styles.respectTitle}>尊敬的园长/负责人，您好：</p>
            <p className={styles.letterContent}>
              请花一分钟填写这份招生诊断问卷。您的反馈将帮助我们为您提供提升招生的建议。感谢您的支持！
            </p>
          </div>
        </div>
        <div className="relative z-10 px-8">
          <div className={`${styles.formContent} min-h-screen`}>
            {formFields.map((item) => {
              if (item.type === 'input') {
                return (
                  <div className={styles.formItem} key={item.name}>
                    <div className={styles.formLabel}>
                      <span className="text-[#333]">{item.name}</span>
                      {item.required && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </div>
                    <div className={styles.formInput}>
                      <Input
                        className={styles.inputText}
                        maxLength={item.field === 'mobile' ? 11 : undefined}
                        onChange={(val) => onChange(item.field, val)}
                        placeholder={item.placeholder}
                        type="text"
                        value={formData[item.field]}
                      />
                    </div>
                  </div>
                );
              }
              if (item.type === 'radio') {
                return (
                  <div className={styles.formItem} key={item.name}>
                    <div className={styles.formLabel}>
                      <span className="text-[#333]">{item.name}</span>
                      {item.required && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </div>
                    <div className={styles.formInput}>
                      <Radio.Group
                        onChange={(val) => onChange(item.field, val as string)}
                        value={formData[item.field]}
                      >
                        {item.options?.map((option) => (
                          <div className={styles.radioItem} key={option.value}>
                            <Radio
                              className={styles.radioText}
                              value={option.value}
                            >
                              {option.value}
                            </Radio>
                          </div>
                        ))}
                      </Radio.Group>
                      {formData[item.field] === '其他' && (
                        <TextArea
                          autoSize={{ minRows: 3, maxRows: 5 }}
                          className={styles.textarea}
                          onChange={(val) =>
                            onChange(item.otherField as string, val)
                          }
                          placeholder="请注明具体情况"
                          value={formData[item.otherField as string]}
                        />
                      )}
                    </div>
                  </div>
                );
              }
              if (item.type === 'checkbox') {
                return (
                  <div className={styles.formItem} key={item.name}>
                    <div className={styles.formLabel}>
                      <span className="text-[#333]">{item.name}</span>
                      {item.required && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </div>
                    <div className={styles.formInput}>
                      <Checkbox.Group
                        onChange={(val) =>
                          onChange(item.field, val.toString() as string)
                        }
                        value={formData[item.field]?.split(',')}
                      >
                        {item.options?.map((option) => (
                          <div className={styles.radioItem} key={option.value}>
                            <Checkbox
                              className={styles.radioText}
                              value={option.value}
                            >
                              {option.value}
                            </Checkbox>
                          </div>
                        ))}
                      </Checkbox.Group>
                      {formData[item.field]?.includes('其他') && (
                        <TextArea
                          autoSize={{ minRows: 3, maxRows: 5 }}
                          className={styles.textarea}
                          onChange={(val) =>
                            onChange(item.otherField as string, val)
                          }
                          placeholder="请注明具体情况"
                          value={formData[item.otherField as string]}
                        />
                      )}
                    </div>
                  </div>
                );
              }
              if (item.type === 'textArea') {
                return (
                  <div className={styles.formItem} key={item.name}>
                    <div className={styles.formLabel}>
                      <span className="text-[#333]">{item.name}</span>
                      {item.required && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                    </div>
                    <div className={styles.formInput}>
                      <TextArea
                        autoSize={{ minRows: 3, maxRows: 5 }}
                        className={styles.textarea}
                        onChange={(val) => onChange(item.field, val)}
                        placeholder={item.placeholder}
                        value={formData[item.field]}
                      />
                    </div>
                  </div>
                );
              }
              return null;
            })}
            <Button
              block
              color="primary"
              loading={loading}
              onClick={handleSubmit}
              shape="rounded"
              size="large"
            >
              提交
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
