'use client';

import { format } from 'date-fns';
import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getSubmitDetail } from '@/api/form';

import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import classPicker from '../../workflow/create/components/widgets/ClassPicker';
import departmentPicker from '../../workflow/create/components/widgets/DepartmentPicker';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import studentPicker from '../../workflow/create/components/widgets/StudentPicker';
import teacherPicker from '../../workflow/create/components/widgets/TeacherPicker';
import video from '../../workflow/create/components/widgets/Video';

type State = {
  instanceId: string;
  instanceName: string;
  form: object;
  content: object;
  status: number;
  submitTime: number;
  submitUserName: string;
  settings: string;
};

type SubmitDetailResponse = {
  instanceId: string;
  instanceName: string;
  form: string;
  content: string;
  status: number;
  submitTime: number;
  submitUserName: string;
  settings: string;
};

interface FormField {
  printable?: boolean;
  properties?: Record<string, FormField>;
  [key: string]: unknown;
}

function Page() {
  const searchParams = useSearchParams();
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const form = useForm();
  const [data, setData] = useState<State>({
    instanceId: '',
    instanceName: '',
    form: {},
    content: {},
    status: 0,
    submitTime: 0,
    submitUserName: '',
    settings: '',
  });

  useEffect(() => {
    if (submitId) {
      getSubmitDetail(submitId).then((res: unknown) => {
        if (res && typeof res === 'object') {
          const typedRes = res as SubmitDetailResponse;
          const formJSON = JSON.parse(typedRes.form);
          // 删除不打印的控件（包括分组内的字段）
          const filterPrintableProperties = (
            properties: Record<string, FormField>
          ) => {
            for (const key of Object.keys(properties)) {
              const field = properties[key];
              if (field?.printable === false) {
                delete properties[key];
              } else if (
                field?.properties &&
                typeof field.properties === 'object'
              ) {
                // 递归处理分组内的字段
                filterPrintableProperties(field.properties);
              }
            }
          };

          filterPrintableProperties(formJSON.properties);
          const d = {
            ...typedRes,
            form: formJSON,
            content: JSON.parse(typedRes.content),
          };
          setData(d);
        }
      });
    }
  }, [submitId]);

  const onMount = () => {
    if (data.content) {
      form.setValues(data.content);
    }
    // setLoading(false);
  };

  return (
    <div className="h-screen">
      <div className="relative p-4">
        <div className="px-4 pt-2 font-bold text-2xl">{data.instanceName}</div>
        <div className="px-4 pt-2 text-sm text-stone-400">
          {data.submitUserName} 提交于：
          {format(data.submitTime * 1000, 'yyyy-MM-dd HH:mm')}
        </div>
        {/* <div className="px-4 pt-4 text-base font-bold">提交内容：</div> */}
        {data.form && Object.keys(data.form).length > 0 && (
          <FormRender
            form={form}
            // displayType="column"
            onFinish={() => {
              console.log('onFinish');
            }}
            onMount={onMount}
            readOnly
            schema={data.form}
            style={
              {
                '--border-bottom': 'none',
                '--border-inner': 'none',
                '--border-top': 'none',
              } as React.CSSProperties
            }
            widgets={{
              checkbox,
              checkboxes,
              richText,
              signature,
              image,
              video,
              attachment,
              address,
              student: studentPicker,
              teacher: teacherPicker,
              class: classPicker,
              department: departmentPicker,
            }}
          />
        )}
      </div>
    </div>
  );
}

export default Page;
