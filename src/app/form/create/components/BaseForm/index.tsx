'use client';

import { ActionSheet, Form, Input, Toast } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import type { FormInstance } from 'antd-mobile/es/components/form';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

import { getGroup } from '@/api/form';
import { useFormStore } from '@/store/useFormStore';
import { debounce } from '@/utils';
import Emitter from '@/utils/emitter';

import type { Ref } from '../../../../workflow/create/components/IconPicker';
import IconPicker from '../../../../workflow/create/components/IconPicker';

const icons = Array.from({ length: 15 }).map(
  (_, index) =>
    `https://unicorn-media.ancda.com/production/app/form/icon/${index + 1}.png`
);

export default function BaseForm() {
  const baseForm = useFormStore((state) => state.baseForm);
  const setBaseForm = useFormStore((state) => state.setBaseForm);

  const [form] = Form.useForm();
  const formRef = useRef<FormInstance>(null);
  const iconPickerRef = useRef<Ref>(null);

  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actions, setActions] = useState<Action[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionsCache, setActionsCache] = useState<Action[]>([]);

  const onChange = debounce((value: string) => {
    setBaseForm({ ...baseForm, instanceName: value });
  }, 500);

  useEffect(() => {
    form.setFieldsValue(baseForm);
  }, [baseForm]);

  useEffect(() => {
    Emitter.on('validateFields', validateFields);
    return () => {
      Emitter.off('validateFields', validateFields);
    };
  }, []);

  const validateFields = () => {
    return new Promise((resolve, reject) => {
      form
        .validateFields()
        .then((values) => {
          resolve(values);
        })
        .catch((err) => {
          if (Array.isArray(err.errorFields)) {
            Toast.show({
              content: err.errorFields[0].errors[0],
            });
          }
          reject(err);
        });
    });
  };

  const handleCategoryClick = async () => {
    console.log('333');
    if (loading) {
      return;
    }

    if (actionsCache.length > 0) {
      setActions(actionsCache);
      setActionSheetVisible(true);
      return;
    }

    setLoading(true);
    try {
      const res = await getGroup();
      if (Array.isArray(res?.list)) {
        const newActions = res.list.map(
          (item: { cateId: string; name: string }) => ({
            key: item.cateId,
            text: item.name,
          })
        );
        setActions(newActions);
        setActionsCache(newActions);
        setActionSheetVisible(true);
      }
    } catch (error) {
      console.error('获取分类失败:', error);
      Toast.show({
        content: '获取分类失败，请重试',
        icon: 'fail',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Form
        form={form}
        layout="horizontal"
        mode="card"
        ref={formRef}
        // requiredMarkStyle="text-required"
      >
        <Form.Header>基础信息</Form.Header>
        <Form.Item
          className="border-none"
          label="名称"
          name="instanceName"
          rules={[{ required: true }]}
        >
          <Input
            maxLength={50}
            minLength={2}
            onChange={onChange}
            // defaultValue={baseForm.name}
            // value={baseForm.name}
            placeholder="请输入名称"
            style={{ '--text-align': 'right' }}
          />
        </Form.Item>
        <Form.Item
          clickable
          extra={
            <div className="text-[#CCC]">
              {baseForm.iconUrl ? (
                <Image
                  alt=""
                  className="h-[64px] w-[64px] rounded object-cover"
                  height="0"
                  sizes="64px"
                  src={baseForm.iconUrl}
                  width="0"
                />
              ) : (
                '请选择图标'
              )}
            </div>
          }
          label="图标"
          name="iconUrl"
          onClick={() => {
            iconPickerRef.current?.toggle();
          }}
          rules={[{ required: true, message: '请选择一个图标' }]}
        />
        <Form.Item
          arrow
          clickable
          extra={
            baseForm.cateName ? (
              <div className="text-[#333]">{baseForm.cateName}</div>
            ) : (
              <div className="text-[#CCC]">请选择分类</div>
            )
          }
          label="分类"
          name="cateId"
          onClick={() => handleCategoryClick()}
          rules={[{ required: true, message: '请选择分类' }]}
        />
      </Form>
      <IconPicker
        icons={icons}
        onSelect={(value) => {
          setBaseForm({ ...baseForm, iconUrl: value });
        }}
        ref={iconPickerRef}
      />
      <ActionSheet
        actions={actions}
        cancelText="取消"
        extra="请选择"
        onAction={(action) => {
          if (action.key) {
            setBaseForm({
              ...baseForm,
              cateId: String(action.key),
              cateName: String(action.text || ''),
            });
          }
          setActionSheetVisible(false);
        }}
        onClose={() => setActionSheetVisible(false)}
        visible={actionSheetVisible}
      />
    </>
  );
}
