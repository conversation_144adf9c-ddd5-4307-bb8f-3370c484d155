'use client';

import { <PERSON><PERSON>, SpinLoading, Toast } from 'antd-mobile';
import FormRender, { type SchemaBase, useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getFormDetail, getSubmitDetail, submit, update } from '@/api/form';
import api from '@/lib/api';
import { postMessage } from '@/utils';
// import { schema as testSchema } from '../create/components/data';
import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import classPicker from '../../workflow/create/components/widgets/ClassPicker';
import departmentPicker from '../../workflow/create/components/widgets/DepartmentPicker';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import studentPicker from '../../workflow/create/components/widgets/StudentPicker';
import Table from '../../workflow/create/components/widgets/Table';
import teacherPicker from '../../workflow/create/components/widgets/TeacherPicker';
import video from '../../workflow/create/components/widgets/Video';

const findLabels = (value: any[], options: any[]) => {
  if (!(isValidateArray(value) && isValidateArray(options))) {
    return [];
  }

  return value.map((v) => options.find((o) => o.value === v)?.label);
};

export function getFormat(format: string) {
  switch (format) {
    case 'date':
      return 'YYYY-MM-dd';
    case 'year':
      return 'YYYY';
    case 'month':
      return 'YYYY-MM';
    case 'week':
      return 'YYYY-w';
    case 'hour':
      return 'YYYY-MM-dd hh';
    case 'minute':
      return 'YYYY-MM-dd hh:mm';
    case 'second':
      return 'YYYY-MM-dd hh:mm:ss';
    case 'week-day':
      return 'w-d';
    default:
      return 'YYYY-MM-dd';
  }
}

// const flatCascaderOptions = (options: any[]) => {
//   const result = [];

//   const walk = (list: any[]) => {
//     list.forEach((i) => {
//       result.push(i);
//       if (isValidateArray(i.children)) {
//         walk(i.children);
//       }
//     });
//   };

//   walk(options);
//   return result;
// };

const isValidateArray = (list: unknown) =>
  Array.isArray(list) && list.length > 0;

// 获取嵌套对象属性值，支持点号路径访问
const getNestedValue = (obj: any, path: string): any => {
  if (!(obj && path)) {
    return;
  }

  try {
    // 使用 new Function 执行字符串定义的规则
    let functionBody: string;

    // 如果 path 已经包含 return 语句，直接使用
    if (path.trim().startsWith('return ')) {
      functionBody = path;
    } else {
      // 否则在前面加上 obj. 前缀
      functionBody = `return obj.${path}`;
    }

    const func = new Function('data', functionBody);
    return func(obj);
  } catch (error) {
    console.warn(`执行规则失败: ${path}`, error);
    // 降级处理：如果不包含点号，直接返回属性值（向后兼容）
    if (!path.includes('.')) {
      return obj[path];
    }
    return;
  }
};

const getLabel = (value: any, schema: any) => {
  const { props } = schema;
  console.log('🚀 ~ props:', props);

  let __html = '';

  switch (schema.widget) {
    case 'input':
    case 'textArea':
    case 'inputNumber':
    case 'amountNumber':
    case 'phoneNumber':
    case 'richText':
    case 'rate':
    case 'stepper':
    case 'datePicker':
      __html = value;
      break;
    case 'image':
      __html = value?.length ? `${value.length}张图片` : '-';
      break;
    case 'video':
      __html = value?.length ? `${value.length}个视频` : '-';
      break;
    case 'attachment':
      __html = value?.length ? `${value.length}个附件` : '-';
      break;
    case 'signature':
      __html = value ? '已签名' : '-';
      break;
    case 'slider':
      if (isValidateArray(value)) {
        __html = value.join(' - ');
      } else {
        __html = value;
      }
      break;
    case 'checkbox':
    case 'selector':
      {
        const { options } = props;
        if (isValidateArray(value)) {
          __html = findLabels(value, options).join('，');
        }
      }
      break;
    case 'switch': {
      const { uncheckedText = '否', checkedText = '是' } = props || {};
      __html = value ? checkedText : uncheckedText;
      break;
    }
    case 'address':
      if (isValidateArray(value)) {
        __html = value.map((item) => item.label).join(' - ');
      }
      break;
    case 'radio':
      {
        const { options } = props;
        __html = options.find((o) => o.value === value)?.label;
      }
      break;
    case 'picker': {
      const { options } = props;
      if (options?.length) {
        __html = findLabels(value, options).join('-') || '-';
      }
      break;
    }
    case 'group': {
      // 地址控件
      const { area, detail } = props || {};
      if (area && detail) {
        const areaArr = value.area.map((item) => item.label).join('-');
        __html = `${areaArr} ${value.detail || '-'}`;
      }
      break;
    }
    default:
      __html = '-';
  }
  return __html;
};

export default function Index() {
  const searchParams = useSearchParams();
  const form = useForm();
  const instanceId = searchParams?.get('instanceId');
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [schema, setSchema] = useState<SchemaBase | null>(null);
  const [formData, setFormData] = useState<Record<string, unknown> | null>(
    null
  );
  const [dataLoading, setDataLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [versionId, setVersionId] = useState('');

  // 联动规则类型定义
  interface LinkageRule {
    source: string[];
    target: Array<{
      type: 'value' | 'schema';
      name: string;
      responseKey: string;
    }>;
    request: {
      url: string;
      method: string;
      bodyKey: Record<string, string>;
    };
  }

  const [linkageRules, setLinkageRules] = useState<LinkageRule[]>([]);
  const _exampleRules = {
    rules: [
      {
        source: ['form-Ct7MnWf3HU6v', 'form-xvCl3PwvpHfg'],
        target: [
          {
            name: 'form-6YPuwiUDPGS6',
            type: 'value',
            responseKey: 'return data[0].needCheckNum + data[1].needCheckNum',
          },
          {
            name: 'form-7rtktQtjPKvp',
            type: 'value',
            responseKey: 'return data[0].checkedNum + data[1].checkedNum',
          },
        ],
        request: {
          url: 'https://test-ucapp-api.ancda.com/v1/affairs/attendance/checkin/studentList',
          method: 'POST',
          bodyKey: {
            'form-Ct7MnWf3HU6v': 'date',
            'form-xvCl3PwvpHfg': 'classIds',
          },
        },
      },
      {
        source: ['form-X2bGSDAEL1Eg'],
        target: [
          {
            name: 'form-WTHwi1OcppmL',
            type: 'schema',
            responseKey: 'children',
          },
        ],
        request: {
          url: 'https://test-ucapp-api.ancda.com/v1/contact/school/departments',
          method: 'GET',
          bodyKey: { 'form-X2bGSDAEL1Eg': 'date' },
        },
      },
    ],
  };

  const [watch, setWatch] = useState<
    Record<string, (val: string | null) => void>
  >({});

  // 构建 watch 对象
  useEffect(() => {
    const newWatch: Record<string, (val: string | null) => void> = {};

    // 防抖处理，避免同一规则重复触发
    const ruleTimers = new Map<string, NodeJS.Timeout>();

    // 创建监听器函数
    const createWatchHandler = (rule: LinkageRule, ruleIndex: number) => {
      return (val: string | null) => {
        const ruleKey = `rule-${ruleIndex}`;

        // 清除之前的定时器
        const existingTimer = ruleTimers.get(ruleKey);
        if (existingTimer) {
          clearTimeout(existingTimer);
        }

        // 设置新的定时器，防抖处理
        const timer = setTimeout(() => {
          console.log(
            `联动触发 - 规则${ruleIndex}: ${rule.source.join(', ')}, 触发值:`,
            val
          );

          // 收集所有相关字段的值
          const allValues: Record<string, string | null> = {};
          for (const field of rule.source) {
            const fieldValue = form.getValueByPath(field);
            allValues[field] = fieldValue;
          }
          console.log('🚀 ~ timer ~ allValues:', allValues);

          // 检查是否所有必需字段都有值
          const hasEmptyValues = rule.source.some((field) => {
            const value = allValues[field];
            return value == null || value === '';
          });

          if (hasEmptyValues) {
            console.log(`规则${ruleIndex}: 存在空值，清空目标字段`);
            // 如果有空值，清空目标字段
            for (const target of rule.target) {
              form.setValueByPath(target.name, undefined);
            }
            return;
          }

          // 构建请求体
          const body: Record<string, unknown> = {};
          for (const field of rule.source) {
            const paramKey = rule.request.bodyKey[field];
            if (paramKey && allValues[field]) {
              const fieldValue = allValues[field];
              // 检查是否为包含对象的数组，如果是则提取id属性
              if (
                Array.isArray(fieldValue) &&
                fieldValue.length > 0 &&
                typeof fieldValue[0] === 'object' &&
                fieldValue[0] !== null &&
                'id' in fieldValue[0]
              ) {
                body[paramKey] = fieldValue.map(
                  (item: { id: string | number }) => item.id
                );
              } else {
                body[paramKey] = fieldValue;
              }
            }
          }

          console.log(`规则${ruleIndex}: 发送联动请求:`, {
            url: rule.request.url,
            body,
          });

          // 发送 API 请求
          const requestConfig =
            rule.request.method.toUpperCase() === 'GET'
              ? { params: body }
              : { data: body };

          api({
            url: rule.request.url,
            method: rule.request.method,
            headers: { 'Content-Type': 'application/json' },
            ...requestConfig,
          })
            .then((res) => {
              const data = res.data ?? res;
              console.log(`规则${ruleIndex}: 联动请求成功:`, data);

              for (const t of rule.target) {
                if (t.type === 'value') {
                  const value = getNestedValue(data, t.responseKey);
                  // 判断值是否有效，只有当值不为 undefined、null、空字符串时才设置
                  if (value !== undefined && value !== null && value !== '') {
                    form.setValueByPath(t.name, String(value));
                  } else {
                    console.warn(
                      `联动规则获取到无效值: ${t.responseKey} = ${value}`
                    );
                    // 清空目标字段
                    form.setValueByPath(t.name, undefined);
                  }
                }
                if (t.type === 'schema') {
                  form.setSchemaByPath(t.name, {
                    props: {
                      options: data[t.responseKey].map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      })),
                    },
                  });
                }
              }
            })
            .catch((error) => {
              console.error(`规则${ruleIndex}: 联动请求失败:`, error);
              // 请求失败时清空目标字段
              for (const target of rule.target) {
                form.setValueByPath(target.name, undefined);
              }
            });

          ruleTimers.delete(ruleKey);
        }, 100); // 100ms 防抖

        ruleTimers.set(ruleKey, timer);
      };
    };

    linkageRules.forEach((rule, index) => {
      const handler = createWatchHandler(rule, index);
      // 为每个 source 字段创建监听器
      for (const sourceField of rule.source) {
        newWatch[sourceField] = handler;
      }
    });

    setWatch(newWatch);

    // 清理函数
    return () => {
      for (const timer of ruleTimers.values()) {
        clearTimeout(timer);
      }
      ruleTimers.clear();
    };
  }, [linkageRules]);

  useEffect(() => {
    if (!(instanceId || submitId)) {
      return;
    }
    if (submitId) {
      setDataLoading(true);
      getSubmitDetail(submitId)
        .then((res: any) => {
          setDataLoading(false);
          if (typeof res === 'object') {
            setSchema(JSON.parse(res.form));
            setFormData(JSON.parse(res.content));
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    } else if (instanceId) {
      setDataLoading(true);
      getFormDetail(instanceId)
        .then((res: any) => {
          setDataLoading(false);
          if (res.form) {
            setSchema(JSON.parse(res.form));
          }
          setVersionId(res.versionId);

          // 处理联动规则
          if (res.settings) {
            try {
              const { rules } = JSON.parse(res.settings);
              if (Array.isArray(rules)) {
                setLinkageRules(rules);
                console.log('联动规则加载成功:', rules);
              } else {
                console.warn('联动规则格式不正确，应为数组格式');
                setLinkageRules([]);
              }
            } catch (error) {
              console.error(
                '解析联动规则失败:',
                error,
                'settings内容:',
                res.settings
              );
              setLinkageRules([]);
            }
          } else {
            console.log('未找到联动规则配置');
            setLinkageRules([]);
          }
        })
        .catch(() => {
          setDataLoading(false);
        });
    }
  }, [instanceId, submitId]);

  const onFinish = (formData: Record<string, unknown>) => {
    if (!schema?.properties) {
      return;
    }
    console.log('🚀 ~ formData:', formData);
    console.log('🚀 ~ schema:', schema);

    const formDataCopy = { ...formData };
    const { properties } = schema;
    for (const key in formDataCopy) {
      if (Object.hasOwn(formDataCopy, key) && properties[key]) {
        formDataCopy[key] = getLabel(formDataCopy[key], properties[key]);
      }
    }
    const data = {
      instanceId,
      form: JSON.stringify(schema),
      content: JSON.stringify(formData),
      result: formDataCopy,
      versionId,
    };
    setSubmitLoading(true);
    if (submitId) {
      update(submitId, data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('更新成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    } else {
      submit(data)
        .then((res: any) => {
          setSubmitLoading(false);
          Toast.show('提交成功');
          postMessage({ goBack: 2, instanceId: res.instanceId });
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    }
  };

  const onMount = () => {
    // setLoading(false);
    setIsMounted(true);
    if (submitId && formData) {
      form.setValues(formData);
    }
  };

  return (
    <div className="relative flex h-screen flex-col bg-white ">
      {dataLoading && (
        <div className="flex h-screen w-full items-center justify-center">
          <SpinLoading />
        </div>
      )}
      <div className="">
        {!!schema && (
          <FormRender
            form={form}
            // requiredMarkStyle="text-required"
            // displayType="column"
            mode="card"
            onFinish={onFinish}
            onMount={onMount}
            schema={schema}
            watch={watch}
            widgets={{
              checkbox,
              checkboxes,
              richText,
              signature,
              image,
              video,
              attachment,
              address,
              Table,
              student: studentPicker,
              teacher: teacherPicker,
              class: classPicker,
              department: departmentPicker,
            }}
          />
        )}
      </div>
      {isMounted && (
        <div className="w-full p-4">
          <Button
            block
            color="primary"
            loading={submitLoading}
            onClick={() => {
              form.submit();
            }}
            size="large"
            type="submit"
          >
            {submitId ? '更新' : '提交'}
          </Button>
        </div>
      )}
    </div>
  );
}
