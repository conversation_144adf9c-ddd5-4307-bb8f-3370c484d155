'use client';

import { Tabs } from 'antd-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getFormDetail } from '@/api/form';

import Forms from '../../workflow/preview/components/Forms';
import Base from './components/Base';
import Settings from './components/Settings';

export default function Index() {
  const searchParams = useSearchParams();
  const instanceId = searchParams?.get('instanceId');
  const authorization = searchParams?.get('authorization');

  if (authorization) {
    Cookies.set('Authorization', authorization);
  }

  const [data, setData] = useState({
    instanceName: '-',
    iconUrl: 'https://unicorn-media.ancda.com/production/app/form/icon/1.png',
    remark: '',
    cateId: '',
    cateName: '-',
  });
  const [formSchema, setFormSchema] = useState(null);
  const [activeIndex, setActiveIndex] = useState('1');

  useEffect(() => {
    if (instanceId) {
      getFormDetail(instanceId).then((res: any) => {
        const { form } = res;
        setData(res);
        if (form) {
          const formJson = JSON.parse(form);
          setFormSchema(formJson);
        }
      });
    }
  }, []);

  return (
    <div className="flex h-screen flex-col bg-[#F7F9FF]">
      <div className="fixed top-0 z-10 w-full bg-white">
        <Tabs
          activeLineMode="fixed"
          onChange={(key) => {
            setActiveIndex(key);
          }}
          style={{
            '--fixed-active-line-width': '30px',
            '--content-padding': '0',
            '--active-line-height': '4px',
            '--active-line-color':
              'linear-gradient(90deg, #31C3FF 0%, #4E78FF 100%)',
            '--active-line-border-radius': '2px',
          }}
        >
          <Tabs.Tab className="w-1/2" forceRender key="1" title="基础信息" />
          <Tabs.Tab className="w-1/2" forceRender key="2" title="设置" />
        </Tabs>
      </div>
      <div className="flex flex-1 flex-col">
        {activeIndex === '1' && (
          <div className="flex flex-1 flex-col overflow-scroll bg-[#F7F9FF] pt-[80px]">
            <Base data={data} />
            <div className="px-4 text-base text-stone-400">表单信息预览</div>
            <div className="p-4">
              <div className="rounded-xl bg-white">
                <Forms formSchema={formSchema} />
              </div>
            </div>
          </div>
        )}
        {activeIndex === '2' && (
          <div className="flex flex-1 flex-col overflow-scroll bg-[#F7F9FF] pt-[80px]">
            <Settings data={data} />
          </div>
        )}
      </div>
    </div>
  );
}
