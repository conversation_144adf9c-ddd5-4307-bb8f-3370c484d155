'use client';

import FormRender, { useForm } from 'form-render-mobile';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { getSubmitDetail } from '@/api/form';

import address from '../../workflow/create/components/widgets/Address';
import attachment from '../../workflow/create/components/widgets/Attachment';
import checkbox from '../../workflow/create/components/widgets/Checkbox';
import checkboxes from '../../workflow/create/components/widgets/Checkboxes';
import classPicker from '../../workflow/create/components/widgets/ClassPicker';
import departmentPicker from '../../workflow/create/components/widgets/DepartmentPicker';
import image from '../../workflow/create/components/widgets/Image';
import richText from '../../workflow/create/components/widgets/RichText';
import signature from '../../workflow/create/components/widgets/Signature';
import studentPicker from '../../workflow/create/components/widgets/StudentPicker';
import teacherPicker from '../../workflow/create/components/widgets/TeacherPicker';
import video from '../../workflow/create/components/widgets/Video';

type State = {
  instanceId: string;
  instanceName: string;
  form: object;
  content: object;
  status: number;
  submitTime: number;
  settings: string;
};

function Page() {
  const searchParams = useSearchParams();
  const _instanceId = searchParams?.get('instanceId');
  const submitId = searchParams?.get('submitId');
  const authorization = searchParams?.get('authorization');
  if (authorization) {
    Cookies.set('Authorization', authorization);
  }
  const form = useForm();

  const [data, setData] = useState<State>({
    instanceId: '',
    instanceName: '',
    form: {},
    content: {},
    status: 0,
    submitTime: 0,
    settings: '',
  });

  useEffect(() => {
    if (submitId) {
      getSubmitDetail(submitId).then((res: any) => {
        if (typeof res === 'object') {
          const data = {
            ...res,
            form: JSON.parse(res.form),
            content: JSON.parse(res.content),
          };
          setData(data);
        }
      });
    }
  }, [submitId]);

  const onMount = () => {
    if (data.content) {
      form.setValues(data.content);
    }
    // setLoading(false);
  };

  return (
    <div className="h-screen bg-[#F7F9FF]">
      {/* <div className="flex items-center justify-between bg-white p-4 " /> */}
      <div className="relative p-4">
        <div className="rounded-xl bg-gradient-to-b bg-white from-[#EAF0FF] to-40% to-[#FFFFFF] p-2">
          <div className="px-4 pt-2 font-bold text-base">
            {data.instanceName}
          </div>
          {data.form && Object.keys(data.form).length > 0 && (
            <FormRender
              form={form}
              // displayType="column"
              onFinish={() => {
                console.log('onFinish');
              }}
              onMount={onMount}
              readOnly
              schema={data.form}
              style={{
                '--border-bottom': 'none',
                '--border-inner': 'none',
                '--border-top': 'none',
              }}
              widgets={{
                checkbox,
                checkboxes,
                richText,
                signature,
                image,
                video,
                attachment,
                address,
                student: studentPicker,
                teacher: teacherPicker,
                class: classPicker,
                department: departmentPicker,
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Page;
